package dataAll.arms.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.creator.OneProData;
   import dataAll.must.define.MustDefine;
   
   public class ArmsRemakeCtrl
   {
      
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function ArmsRemakeCtrl()
      {
         super();
      }
      
      public static function get maxLevel() : Number
      {
         return CF.getAttribute("maxLevel");
      }
      
      public static function set maxLevel(v0:Number) : void
      {
         CF.setAttribute("maxLevel",v0);
      }
      
      public static function init() : void
      {
         maxLevel = 45;
      }
      
      public static function getRemake(s0:ArmsSave, proDataArr0:Array) : ArmsSave
      {
         var newS0:ArmsSave = null;
         for(var i:int = 0; i < 30; i++)
         {
            newS0 = getRemakeOne(s0,proDataArr0);
            if(!samePan(s0,newS0))
            {
               return newS0;
            }
         }
         return null;
      }
      
      private static function getRemakeOne(s0:ArmsSave, proDataArr0:Array) : ArmsSave
      {
         var newS0:ArmsSave = copyOneOnSpecialAndSkill(s0);
         remakeSpcial(s0,proDataArr0,newS0);
         remakeSkill(s0,proDataArr0,newS0);
         return newS0;
      }
      
      private static function copyOneOnSpecialAndSkill(s0:ArmsSave) : ArmsSave
      {
         var newS0:ArmsSave = s0.copy();
         var zeroS0:ArmsSave = new ArmsSave();
         setAllByOther(newS0,zeroS0);
         return newS0;
      }
      
      public static function setAllByOther(s0:ArmsSave, otherS0:ArmsSave) : void
      {
         var specialName0:String = null;
         var allArr0:Array = ArmsSpecial.specialArr.concat([]);
         for each(specialName0 in allArr0)
         {
            ArmsSpecialAndSkill.setOneValueByOther(s0,otherS0,specialName0);
         }
         s0.skillArr = otherS0.skillArr.concat([]);
      }
      
      private static function remakeSpcial(s0:ArmsSave, proDataArr0:Array, newS0:ArmsSave) : void
      {
         var da0:OneProData = null;
         var num0:int = 0;
         var allArr0:Array = null;
         var canArr0:Array = null;
         var newArr0:Array = null;
         var haveName0:String = null;
         var haveArr0:Array = [];
         for each(da0 in proDataArr0)
         {
            if(da0.lockB && da0.type == "special")
            {
               haveArr0.push(da0.name);
            }
         }
         num0 = ArmsSpecialAndSkill.haveSpecialNum(s0) - haveArr0.length;
         allArr0 = ArmsSpecial.specialArr.concat([]);
         canArr0 = ComMethod.deductArr(allArr0,haveArr0);
         newArr0 = ComMethod.getRandomArray(canArr0,num0);
         for each(haveName0 in haveArr0)
         {
            ArmsSpecialAndSkill.setOneValueByOther(newS0,s0,haveName0);
         }
         ArmsSpecialAndSkill.setSpecialByNameArr(newS0,newArr0);
      }
      
      private static function remakeSkill(s0:ArmsSave, proDataArr0:Array, newS0:ArmsSave) : void
      {
         var da0:OneProData = null;
         var num0:int = 0;
         var allArr0:Array = null;
         var canArr0:Array = null;
         var newArr0:Array = null;
         var haveArr0:Array = [];
         for each(da0 in proDataArr0)
         {
            if(da0.lockB && da0.type == "skill")
            {
               haveArr0.push(da0.name);
            }
         }
         num0 = s0.skillArr.length - haveArr0.length;
         allArr0 = ArmsSpecialAndSkill.getCanSkillNameArr(s0);
         canArr0 = ComMethod.deductArr(allArr0,haveArr0);
         newArr0 = ComMethod.getRandomArray(canArr0,num0);
         newS0.skillArr = sortSkillArrByOther(haveArr0.concat(newArr0),s0.skillArr);
      }
      
      private static function sortSkillArrByOther(arr0:Array, arr1:Array) : Array
      {
         var s0:String = null;
         var f0:int = 0;
         var newArr0:Array = [];
         for each(s0 in arr1)
         {
            f0 = int(arr0.indexOf(s0));
            if(f0 >= 0)
            {
               newArr0.push(s0);
               arr0.splice(f0,1);
            }
         }
         return newArr0.concat(arr0);
      }
      
      private static function samePan(s0:ArmsSave, s1:ArmsSave) : Boolean
      {
         return ArmsSpecialAndSkill.samePan(s0,s1);
      }
      
      public static function getMust(s0:ArmsSave, proDataArr0:Array) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var lv0:int = s0.getTrueLevel();
         d0.lv = lv0;
         if(lv0 < maxLevel)
         {
            d0.lv = maxLevel;
         }
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(lv0);
         var b0:Number = getMustBaseValue(lv0);
         var p0:Number = getMustByLockNum(proDataArr0);
         var n0:Number = getMustByProNum(proDataArr0.length);
         var c0:Number = getMustByColor(s0.color);
         var type0:Number = 1;
         var num0:int = Math.ceil((b0 * p0 + b0 * n0) * c0 * type0);
         d0.inThingsDataByArr(["godStone;" + num0]);
         return d0;
      }
      
      private static function getMustByColor(color0:String) : Number
      {
         var index0:int = EquipColor.getIndex(color0);
         if(index0 >= 5)
         {
            return 2;
         }
         return 1;
      }
      
      private static function getMustByLockNum(proDataArr0:Array) : Number
      {
         var da0:OneProData = null;
         var lockNum0:int = 0;
         for each(da0 in proDataArr0)
         {
            if(da0.lockB)
            {
               lockNum0++;
            }
         }
         return lockNum0 * 0.3;
      }
      
      private static function getMustBaseValue(lv0:int) : Number
      {
         var v0:Number = lv0 / 3 - 8;
         if(lv0 > 80)
         {
            v0 *= 4;
         }
         if(v0 < 2)
         {
            v0 = 2;
         }
         return v0;
      }
      
      private static function getMustByProNum(num0:int) : Number
      {
         return num0 * 0.3;
      }
   }
}

