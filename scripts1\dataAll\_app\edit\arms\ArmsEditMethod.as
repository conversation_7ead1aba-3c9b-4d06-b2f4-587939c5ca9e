package dataAll._app.edit.arms
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.pro.ProType;
   
   public class ArmsEditMethod
   {
      
      private static var armsAgent:EditListAgent = null;
      
      private static var mapAgent:EditListAgent = null;
      
      public function ArmsEditMethod()
      {
         super();
      }
      
      public static function getArmsName(saveLastId0:Number, uidx0:String) : String
      {
         return "a" + saveLastId0 + "$" + uidx0;
      }
      
      public static function getUidxByArmsName(name0:String) : String
      {
         var f0:int = int(name0.indexOf("$"));
         if(f0 > 0)
         {
            return name0.substr(f0 + 1);
         }
         return "";
      }
      
      public static function afterDefineInit() : void
      {
         var json0:String = null;
         var obj0:Object = null;
         var codeArr0:Array = [];
         codeArr0.push("{\"name\":\"sniperCicada$2\",\"lineD\":{\"lightSize\":15,\"size\":5},\"bulletWidth\":800,\"baseLabel\":\"sniperCicada\",\"attackGap\":0.7,\"reloadGap\":1,\"godSkillArr\":[\"Hit_posion7_godArmsSkill\"]}");
         codeArr0.push("{\"name\":\"shotgunSkunkTwo$2\",\"baseLabel\":\"shotgunSkunkTwo\",\"skillArr\":[\"Hit_imploding_godArmsSkill\"]}");
         codeArr0.push("{\"name\":\"christmasGun$2\",\"baseLabel\":\"christmasGun\",\"attackGap\":0.5,\"capacity\":30}");
         for each(json0 in codeArr0)
         {
            obj0 = JSON2.decode(json0);
            addInDefByObj(obj0);
         }
      }
      
      private static function getDefByObj(obj0:Object) : ArmsRangeDefine
      {
         var range0:ArmsRangeDefine = null;
         var baseLabel0:String = obj0["baseLabel"];
         var baseRange0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(baseLabel0);
         if(Boolean(baseRange0))
         {
            range0 = baseRange0.clone();
            range0.clearRange();
            range0.def.inData_bySaveObj(obj0);
         }
         else
         {
            INIT.showError("找不到ArmsRangeDefine定义：" + baseLabel0);
         }
         return range0;
      }
      
      public static function addInDefByObj(obj0:Object, name0:String = "") : ArmsRangeDefine
      {
         var d0:ArmsRangeDefine = getDefByObj(obj0);
         if(Boolean(d0))
         {
            if(name0 != "")
            {
               d0.def.name = name0;
            }
            Gaming.defineGroup.bullet.addInRange(d0);
         }
         return d0;
      }
      
      public static function getArmsListAgent() : EditListAgent
      {
         var fatherArr0:Array = null;
         var cnArr0:Array = null;
         var darr0:Array = null;
         var rangeD0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var father0:String = null;
         var a0:EditListAgent = armsAgent;
         if(Boolean(a0))
         {
            a0.clearFun();
         }
         else
         {
            a0 = new EditListAgent();
            fatherArr0 = ArmsType.uiArr.concat([]);
            cnArr0 = ArmsType.getCnArr(fatherArr0);
            fatherArr0.push("other");
            cnArr0.push("其他");
            a0.inTitle(fatherArr0,cnArr0);
            darr0 = Gaming.defineGroup.bullet.getRangeArr();
            for each(rangeD0 in darr0)
            {
               d0 = rangeD0.def;
               if(d0.isImgOne() && d0.color != EquipColor.YAGOLD)
               {
                  father0 = d0.armsType;
                  if(fatherArr0.indexOf(father0) == -1)
                  {
                     father0 = "other";
                  }
                  a0.addDataLast(d0,father0);
               }
            }
            armsAgent = a0;
         }
         if(Boolean(Gaming.PG.da))
         {
            armsAgentdealNoLink(a0,Gaming.PG.da);
         }
         a0.createAllText();
         return a0;
      }
      
      private static function armsAgentdealNoLink(a0:EditListAgent, pd0:PlayerData) : void
      {
         var armsDa0:ArmsData = null;
         var noArr0:Array = null;
         var darr0:Array = null;
         var rangeD0:ArmsRangeDefine = null;
         var name0:String = null;
         var d0:ArmsDefine = null;
         var armsArr0:Array = pd0.getArmsDataArr(true,true,true,true);
         var armsNameObj0:Object = {};
         for each(armsDa0 in armsArr0)
         {
            name0 = armsDa0.name;
            if(armsNameObj0.hasOwnProperty(name0) == false)
            {
               armsNameObj0[name0] = armsDa0;
            }
         }
         noArr0 = [];
         darr0 = Gaming.defineGroup.bullet.getRangeArr();
         for each(rangeD0 in darr0)
         {
            d0 = rangeD0.def;
            if(d0.isImgOne())
            {
               if(armsNameObj0.hasOwnProperty(d0.name) == false)
               {
                  noArr0.push(d0.name);
               }
            }
         }
         a0.setNoLinkArr(noArr0," ×");
      }
      
      public static function getMainLimitObj(armsD0:ArmsDefine) : Object
      {
         var obj0:Object = {};
         obj0["penetrationNum"] = 10;
         obj0["bounceD.floor"] = 5;
         obj0["bounceD.body"] = 5;
         if(armsD0.isLongLine())
         {
            obj0["bulletLife"] = 0.1;
         }
         else
         {
            obj0["bulletWidth"] = 100;
         }
         return obj0;
      }
      
      public static function getMainLimitTip(da0:ArmsTorData) : String
      {
         var n:* = undefined;
         var name0:String = null;
         var d0:EditProDefine = null;
         var v0:Number = NaN;
         var max0:Number = NaN;
         var bb0:Boolean = false;
         var obj0:Object = getMainLimitObj(da0.getArmsDef());
         var str0:String = "要成为主力武器，必须满足以下属性条件：\n";
         for(n in obj0)
         {
            name0 = n as String;
            d0 = Gaming.defineGroup.editPro.getArmsDefine(name0);
            v0 = da0.getValue(d0) as Number;
            max0 = Number(obj0[n]);
            bb0 = v0 <= max0;
            str0 += "\n" + d0.cnName + ComMethod.color(" 不超过 ","#00FFFF") + max0 + " " + ProType.booleanToUIRed(bb0);
         }
         return str0;
      }
      
      public static function getMainLimitB(da0:ArmsTorData) : Boolean
      {
         var n:* = undefined;
         var name0:String = null;
         var d0:EditProDefine = null;
         var v0:Number = NaN;
         var max0:Number = NaN;
         var obj0:Object = getMainLimitObj(da0.getArmsDef());
         for(n in obj0)
         {
            name0 = n as String;
            d0 = Gaming.defineGroup.editPro.getArmsDefine(name0);
            v0 = da0.getValue(d0) as Number;
            max0 = Number(obj0[n]);
            if(v0 > max0)
            {
               return false;
            }
         }
         return true;
      }
      
      public static function getTestLevelDefine(armsName0:String, diff0:Number) : LevelDefine
      {
         var base0:LevelDefine = Gaming.defineGroup.level.getDefineBy("armsEditTest");
         var d0:LevelDefine = base0.clone();
         var weD0:OneUnitOrderDefine = d0.unitG.getOneByName("Striker2");
         weD0.armsRange = [armsName0];
         d0.info.diff = diff0;
         return d0;
      }
      
      public static function getTempArmsSave(d0:ArmsDefine) : ArmsSave
      {
         var color0:String = d0.color;
         if(color0 == "")
         {
            color0 = EquipColor.RED;
         }
         return Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(99,d0.name,color0);
      }
      
      public static function getTempArmsData(d0:ArmsDefine) : ArmsData
      {
         var s0:ArmsSave = getTempArmsSave(d0);
         var da0:ArmsData = new ArmsData();
         da0.inData_bySave(s0,null);
         return da0;
      }
      
      public static function getMapLevelDefine(armsName0:String, map0:String, diff0:int = 1) : LevelDefine
      {
         var baseD0:LevelDefine = null;
         var d0:LevelDefine = null;
         var editD0:LevelDefine = null;
         var weD0:OneUnitOrderDefine = null;
         var mapS0:WorldMapSave = Gaming.PG.da.worldMap.saveGroup.getSave(map0);
         if(Boolean(mapS0))
         {
            baseD0 = mapS0.getLevelDefine();
            if(Boolean(baseD0))
            {
               d0 = baseD0.clone();
               editD0 = Gaming.defineGroup.level.getDefineBy("armsEditMap");
               d0.fixedBy(editD0);
               weD0 = d0.unitG.getOneByName("Striker2");
               weD0.armsRange = [armsName0];
               d0.info.diff = diff0;
               return d0;
            }
         }
         return null;
      }
      
      public static function getMapListAgent() : EditListAgent
      {
         var a0:EditListAgent = null;
         var fatherArr0:Array = null;
         var cnArr0:Array = null;
         var father0:String = null;
         var arr0:Array = null;
         var d0:WorldMapDefine = null;
         if(Boolean(mapAgent))
         {
            mapAgent.clearFun();
            return mapAgent;
         }
         a0 = new EditListAgent();
         fatherArr0 = [WorldMapDefine.INFECTED_AREA];
         cnArr0 = ["大地图"];
         a0.inTitle(fatherArr0,cnArr0);
         a0.info = "选择要测试的地图";
         for each(father0 in fatherArr0)
         {
            arr0 = Gaming.defineGroup.worldMap.getArrByFather(father0);
            for each(d0 in arr0)
            {
               if(d0.canEditB())
               {
                  a0.addDataLast(d0,father0);
               }
            }
         }
         a0.createAllText();
         mapAgent = a0;
         return a0;
      }
   }
}

