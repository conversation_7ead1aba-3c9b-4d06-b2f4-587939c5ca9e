package dataAll._app.achieve
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.achieve.define.AchieveDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.ui.GatherColor;
   import gameAll.achieve.AchieveValueGetting;
   
   public class AchieveData
   {
      
      public var name:String = "";
      
      private var save:AchieveSave;
      
      public var def:AchieveDefine;
      
      public var dataGroup:AchieveDataGroup;
      
      public var newB:Boolean = false;
      
      public function AchieveData()
      {
         super();
      }
      
      public function inData_bySave(s0:AchieveSave, dataGroup0:AchieveDataGroup) : void
      {
         this.dataGroup = dataGroup0;
         this.save = s0;
         this.name = this.save.name;
         this.def = Gaming.defineGroup.achieve.getDefine(this.name);
      }
      
      public function inData_byDefine(d0:AchieveDefine, dataGroup0:AchieveDataGroup) : void
      {
         this.dataGroup = dataGroup0;
         this.def = d0;
         this.name = d0.name;
      }
      
      private function noSaveAndAddSave() : void
      {
         if(!(this.save is AchieveSave))
         {
            this.save = new AchieveSave();
            this.save.inData_byDefine(this.def);
         }
      }
      
      public function complete() : void
      {
         this.noSaveAndAddSave();
         if(this.save.state != "complete")
         {
            this.save.state = "complete";
            this.newB = true;
         }
      }
      
      public function clear() : void
      {
         this.save = null;
      }
      
      public function inInfo(str0:String, value0:Number) : void
      {
         this.noSaveAndAddSave();
         this.save.info = str0;
         this.save.infoValue = value0;
      }
      
      public function giftGettedB() : Boolean
      {
         return this.save.gB;
      }
      
      public function giftEvent() : void
      {
         this.save.gB = true;
      }
      
      public function isCompleteB() : Boolean
      {
         if(this.save is AchieveSave)
         {
            return this.save.state == "complete";
         }
         return false;
      }
      
      public function getCnName() : String
      {
         return this.def.cnName;
      }
      
      public function getIconUrl() : String
      {
         return this.def.iconUrl;
      }
      
      public function getUnlockLv() : int
      {
         return this.def.unlockLv;
      }
      
      public function getDescription() : String
      {
         return this.def.description;
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         return this.def.getGift();
      }
      
      public function getGiftTip() : String
      {
         var s0:String = null;
         var g0:GiftAddDefineGroup = this.def.getGift();
         if(Boolean(g0))
         {
            s0 = "\n\n奖励：" + g0.getDescription(99);
            if(Boolean(this.save) && this.save.gB)
            {
               s0 += " (已领取)";
            }
            else if(this.isCompleteB())
            {
               s0 += ComMethod.color("<b>(点击领取)</b>",GatherColor.purpleColor);
            }
            return s0;
         }
         return "";
      }
      
      public function getLoadingText() : String
      {
         var v0:Number = NaN;
         if(!this.def.condition.isLoadingB())
         {
            return "";
         }
         v0 = AchieveValueGetting.getValue(this.def.condition.pro);
         if(this.def.condition.value == 0)
         {
            return TextWay.numberToPer(v0) + " / " + TextWay.numberToPer(this.def.condition.mul);
         }
         return v0.toFixed() + " / " + this.def.condition.value;
      }
      
      public function getLoadingPer() : Number
      {
         var v0:Number = NaN;
         if(!this.def.condition.isLoadingB())
         {
            return -1;
         }
         v0 = AchieveValueGetting.getValue(this.def.condition.pro);
         if(this.def.condition.value == 0)
         {
            return v0 / this.def.condition.mul;
         }
         return v0 / this.def.condition.value;
      }
      
      public function getInfo() : String
      {
         if(this.save is AchieveSave)
         {
            if(this.save.infoValue != -1)
            {
               return "最近数据:" + this.def.condition.getValueString(this.save.infoValue);
            }
         }
         return "";
      }
      
      public function getInfoValue() : Number
      {
         if(this.save is AchieveSave)
         {
            return this.save.infoValue;
         }
         return -1;
      }
      
      public function isWearB() : Boolean
      {
         if(this.save is AchieveSave)
         {
            return this.save.wearB;
         }
         return false;
      }
      
      public function setWearB(bb0:Boolean, wearIndex0:Number) : void
      {
         if(this.save is AchieveSave)
         {
            this.save.wearB = bb0;
            this.save.wearIndex = wearIndex0;
         }
      }
      
      public function getProObj() : Object
      {
         return Gaming.defineGroup.achieve.creator.getObj(this);
      }
      
      public function getGatherTip() : String
      {
         var str0:String = "";
         str0 += "<i1>|<blue <b>勋章属性</b>：/>";
         return str0 + ("\n" + Gaming.defineGroup.achieve.creator.getGatherText(this.getProObj()));
      }
      
      public function getSave() : AchieveSave
      {
         return this.save;
      }
   }
}

