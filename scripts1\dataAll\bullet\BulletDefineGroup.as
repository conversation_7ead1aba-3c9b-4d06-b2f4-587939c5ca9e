package dataAll.bullet
{
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsNameDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.define.EquipColor;
   
   public class BulletDefineGroup
   {
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public var rangeTypeObj:Object = {};
      
      public var rangeRandomTypeObj:Object = {};
      
      private var rangeObj:Object = {};
      
      private var rangeArr:Array = [];
      
      private var outObj:Object = {};
      
      private var inObj:Object = {};
      
      public var rareArmsRangeArr:Array = [];
      
      public var blackArmsRangeArr:Array = [];
      
      public var darkgoldArmsRangeArr:Array = [];
      
      public var armsName:ArmsNameDefine = new ArmsNameDefine();
      
      public function BulletDefineGroup()
      {
         super();
         this.obj["bullet"] = {};
         this.obj["arms"] = {};
         this.firstArmsDefine();
      }
      
      private function firstArmsDefine() : void
      {
         this.addTextureByName(ArmsDefine.TEXTURE_ARR,"m",32);
         ArmsDefine.TEXTURE_ARR.reverse();
         this.addTextureByName(ArmsDefine.T_TEXTURE_ARR,"t",17);
         ArmsDefine.T_TEXTURE_ARR.reverse();
      }
      
      public function getBulletArr() : Array
      {
         return this.arr;
      }
      
      private function addTextureByName(arr0:Array, name0:String, num0:int) : void
      {
         for(var i:int = 1; i <= num0; i++)
         {
            arr0.push("texture/" + name0 + i);
         }
      }
      
      public function inData_byXML(xml0:XML, type0:String = "bullet", xmlName0:String = "bullet") : void
      {
         var i:* = undefined;
         var bulletXML0:XMLList = null;
         var armsType0:String = null;
         var n:* = undefined;
         var d0:* = undefined;
         var arr0:Array = this.arr;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            bulletXML0 = fatherXML0[i][xmlName0];
            armsType0 = String(fatherXML0[i].@type);
            for(n in bulletXML0)
            {
               if(type0 == "bullet")
               {
                  d0 = new BulletDefine();
               }
               else
               {
                  d0 = new ArmsDefine();
                  d0.armsType = armsType0;
               }
               d0.inData_byXML(bulletXML0[n]);
               arr0.push(d0);
               this.obj[type0][d0.name] = d0;
            }
         }
      }
      
      public function inArmsRangeData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var bulletXML0:XMLList = null;
         var armsType0:String = null;
         var n:* = undefined;
         var d0:ArmsRangeDefine = null;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            bulletXML0 = fatherXML0[i].bullet;
            armsType0 = String(fatherXML0[i].@type);
            for(n in bulletXML0)
            {
               d0 = new ArmsRangeDefine();
               d0.inData_byXML(bulletXML0[n]);
               d0.def.armsType = armsType0;
               this.rangeObj[d0.def.name] = d0;
               this.rangeArr.push(d0);
               if(!this.rangeTypeObj.hasOwnProperty(armsType0))
               {
                  this.rangeTypeObj[armsType0] = [];
               }
               if(!this.rangeRandomTypeObj[armsType0])
               {
                  this.rangeRandomTypeObj[armsType0] = [];
               }
               this.rangeTypeObj[armsType0].push(d0);
               this.rangeRandomTypeObj[armsType0].push(d0.def.randomPro);
               if(d0.def.color == EquipColor.RED)
               {
                  this.rareArmsRangeArr.push(d0);
               }
               else if(d0.def.color == EquipColor.BLACK)
               {
                  this.blackArmsRangeArr.push(d0);
               }
               else if(d0.def.color == EquipColor.DARKGOLD)
               {
                  this.darkgoldArmsRangeArr.push(d0);
               }
            }
         }
      }
      
      public function addOutRange(d0:ArmsRangeDefine) : void
      {
         this.outObj[d0.name] = d0;
      }
      
      public function delOutRange(d0:ArmsRangeDefine) : void
      {
         this.outObj[d0.name] = null;
      }
      
      public function getOutDef(name0:String) : ArmsRangeDefine
      {
         return this.outObj[name0];
      }
      
      public function outLoginEvent() : void
      {
         this.outObj = {};
      }
      
      public function addInRange(d0:ArmsRangeDefine) : void
      {
         var name0:String = d0.name;
         if(this.rangeObj.hasOwnProperty(name0))
         {
            INIT.showError("不能覆盖range中包含的定义：" + name0);
         }
         else if(this.inObj.hasOwnProperty(name0) == false)
         {
            this.inObj[name0] = d0;
         }
      }
      
      public function getInDef(name0:String) : ArmsRangeDefine
      {
         return this.inObj[name0];
      }
      
      public function getRangeArr() : Array
      {
         return this.rangeArr;
      }
      
      public function getArmsRangeDefine(name0:String) : ArmsRangeDefine
      {
         var d0:ArmsRangeDefine = this.outObj[name0] as ArmsRangeDefine;
         if(Boolean(d0))
         {
            return d0;
         }
         d0 = this.inObj[name0] as ArmsRangeDefine;
         if(Boolean(d0))
         {
            return d0;
         }
         return this.rangeObj[name0];
      }
      
      public function getRangeOnlyRange(name0:String) : ArmsRangeDefine
      {
         return this.rangeObj[name0];
      }
      
      public function getArmsRangeDefineByCn(cn0:String) : ArmsRangeDefine
      {
         var d0:ArmsRangeDefine = null;
         for each(d0 in this.rangeObj)
         {
            if(d0.def.cnName == cn0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getArmsCnArrByArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var r0:ArmsRangeDefine = null;
         var cnArr0:Array = [];
         for each(name0 in nameArr0)
         {
            r0 = this.getArmsRangeDefine(name0);
            if(Boolean(r0))
            {
               cnArr0.push(r0.def.cnName);
            }
            else
            {
               cnArr0.push(name0);
            }
         }
         return cnArr0;
      }
      
      public function getArmsRangeDefineArr(type0:String) : Array
      {
         return this.rangeTypeObj[type0];
      }
      
      public function test_getBarrelArr() : void
      {
         var n:* = undefined;
         var d0:ArmsRangeDefine = null;
         var r_arr0:Array = null;
         var name0:String = null;
         var str0:String = "";
         var arr0:Array = [];
         for(n in this.rangeObj)
         {
            d0 = this.rangeObj[n];
            r_arr0 = d0.def.barrelImgRange;
            for(n in r_arr0)
            {
               name0 = r_arr0[n];
               if(arr0.indexOf(name0) == -1)
               {
                  arr0.push(name0);
                  str0 += "\n" + name0 + ":;" + this.armsName.getNameArr("barrel",name0);
               }
            }
         }
      }
      
      public function getArmsDefine(name0:String) : ArmsDefine
      {
         var d0:ArmsDefine = this.obj["arms"][name0];
         if(!d0)
         {
            d0 = this.getArmsDefineInRange(name0);
         }
         return d0;
      }
      
      public function getDefine(name0:String) : BulletDefine
      {
         return this.obj["bullet"][name0];
      }
      
      public function getArmsDefineInRange(name0:String) : ArmsDefine
      {
         var range0:ArmsRangeDefine = this.getArmsRangeDefine(name0);
         if(Boolean(range0))
         {
            return range0.def;
         }
         return null;
      }
      
      public function getAllLabel() : Array
      {
         var n:* = undefined;
         var d0:BulletDefine = null;
         var arr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            arr0.push(d0.name);
         }
         return arr0;
      }
      
      public function getRareArmsNameArr(lv0:int) : Array
      {
         var d0:ArmsRangeDefine = null;
         var nameArr0:Array = [];
         for each(d0 in this.rareArmsRangeArr)
         {
            if(d0.def.rareDropLevel <= lv0)
            {
               nameArr0.push(d0.def.name);
            }
         }
         return nameArr0;
      }
      
      public function getBlackArmsNameArr(lv0:int, bodyName0:String) : Array
      {
         var d0:ArmsRangeDefine = null;
         var bodyArr0:Array = null;
         var bodyD0:NormalBodyDefine = Gaming.defineGroup.body.getDefine(bodyName0);
         var mustBodyNameB0:Boolean = false;
         if(Boolean(bodyD0))
         {
            mustBodyNameB0 = bodyD0.dropD.blackArmsBodyB;
         }
         var nameArr0:Array = [];
         for each(d0 in this.blackArmsRangeArr)
         {
            if(d0.def.dropLevelArr.indexOf(String(lv0)) >= 0)
            {
               bodyArr0 = d0.def.dropBodyArr;
               if(!mustBodyNameB0 && bodyArr0.length == 0 || bodyArr0.indexOf(bodyName0) >= 0)
               {
                  nameArr0.push(d0.def.name);
               }
            }
         }
         return nameArr0;
      }
      
      public function getAllDropBlackArmsNameArr() : Array
      {
         var d0:ArmsRangeDefine = null;
         var nameArr0:Array = [];
         for each(d0 in this.blackArmsRangeArr)
         {
            if(d0.def.dropLevelArr.length > 0)
            {
               if(d0.def.dropLevelArr[0] < 100)
               {
                  if(d0.def.dropBodyArr.length == 0)
                  {
                     nameArr0.push(d0.def.name);
                  }
               }
            }
         }
         return nameArr0;
      }
      
      public function testBlackArms() : void
      {
      }
   }
}

