package com.sounto.motion.tween
{
   public class LinesTweenMotion
   {
      
      public var now:Number = 0;
      
      public var end:Number = 0;
      
      public var max:Number = 30;
      
      public function LinesTweenMotion()
      {
         super();
      }
      
      public function init(x0:Number) : void
      {
         this.now = x0;
         this.end = x0;
      }
      
      public function inData(x0:Number) : void
      {
         this.end = x0;
         var v0:Number = (x0 - this.now) / 5;
         if(v0 < 0.1 && v0 > -0.1)
         {
            v0 = 0;
         }
         else
         {
            if(v0 > this.max)
            {
               v0 = this.max;
            }
            if(v0 < -this.max)
            {
               v0 = -this.max;
            }
            this.now += v0;
         }
      }
   }
}

