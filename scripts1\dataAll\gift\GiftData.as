package dataAll.gift
{
   import com.sounto.utils.TextMethod;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._player.PlayerData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.level.GiftLevelGiftCreator;
   import dataAll.gift.level.LevelGiftData;
   import dataAll.gift.save.GiftSave;
   
   public class GiftData
   {
      
      public var playerData:PlayerData;
      
      public var save:GiftSave = null;
      
      public function GiftData()
      {
         super();
      }
      
      public function inData_bySave(s0:GiftSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.newDayCtrl(timeStr0);
      }
      
      public function newWeek(timeStr0:String) : void
      {
         this.save.newWeek(timeStr0);
      }
      
      public function setNowReadTime(str0:String) : void
      {
         this.save.setNowReadTime(str0);
      }
      
      public function setLevelGiftGetB(name0:String, bb0:Boolean) : void
      {
         this.save.levelGiftObj[name0] = bb0;
      }
      
      public function getLevelGiftGetB(name0:String) : Boolean
      {
         return this.save.levelGiftObj[name0];
      }
      
      public function getLevelGiftArr() : Array
      {
         var gift0:GiftAddDefineGroup = null;
         var da0:LevelGiftData = null;
         var mapName0:String = null;
         var mapD0:WorldMapDefine = null;
         var arr0:Array = [];
         var giftArr0:Array = Gaming.defineGroup.gift.getArrByFather("levelGift");
         for each(gift0 in giftArr0)
         {
            da0 = new LevelGiftData();
            gift0 = GiftLevelGiftCreator.conver(gift0);
            da0.name = gift0.name;
            da0.define = gift0;
            da0.getGiftB = this.getLevelGiftGetB(da0.name);
            da0.openB = this.playerData.level >= gift0.mustLevel;
            mapName0 = gift0.cnName;
            mapD0 = Gaming.defineGroup.worldMap.getDefine(mapName0);
            if(mapName0 == "")
            {
               da0.mapStr = "";
            }
            else
            {
               da0.mapStr = "必须通关\n";
               if(this.playerData.worldMap.saveGroup.getWinB(mapName0))
               {
                  da0.mapStr += TextMethod.color(mapD0.cnName + "√","#00FF00");
               }
               else
               {
                  da0.mapStr += TextMethod.color(mapD0.cnName,"#FFFF00");
                  da0.openB = false;
               }
            }
            arr0.push(da0);
         }
         return arr0;
      }
      
      public function haveNewGiftGetB() : Boolean
      {
         var gift0:GiftAddDefineGroup = null;
         var giftArr0:Array = Gaming.defineGroup.gift.getArrByFather("levelGift");
         for each(gift0 in giftArr0)
         {
            if(this.playerData.level >= gift0.mustLevel && !this.getLevelGiftGetB(gift0.name))
            {
               return true;
            }
         }
         return false;
      }
   }
}

