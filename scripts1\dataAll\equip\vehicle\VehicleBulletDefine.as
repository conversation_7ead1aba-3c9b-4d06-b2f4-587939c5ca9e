package dataAll.equip.vehicle
{
   import com.common.text.TextWay;
   import com.sounto.math.Maths;
   import com.sounto.utils.ClassProperty;
   import dataAll.bullet.BulletDefine;
   
   public class VehicleBulletDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var cn:String = "";
      
      public var type:String = "";
      
      public var label:String = "";
      
      public var len:Number = 0;
      
      public var yGap:Number = 0;
      
      public var maxRa:Number = 180;
      
      public var minRa:Number = -180;
      
      public var hideB:Boolean = false;
      
      public var bodyRecoil:Number = 0;
      
      public var actionLabel:String = "";
      
      public var swapLabel:String = "";
      
      public var swapSoundUrl:String = "";
      
      private var _dpsMul:String = "";
      
      public function VehicleBulletDefine()
      {
         super();
         this.dpsMul = 1;
      }
      
      public static function getDefArrByXml(xl0:XMLList) : Array
      {
         var i:* = undefined;
         var d0:VehicleBulletDefine = null;
         var arr0:Array = [];
         for(i in xl0)
         {
            d0 = new VehicleBulletDefine();
            d0.inData_byXML(xl0[i]);
            arr0.push(d0);
         }
         return arr0;
      }
      
      public function set dpsMul(v0:Number) : void
      {
         this._dpsMul = TextWay.toCode32(String(v0));
      }
      
      public function get dpsMul() : Number
      {
         return Number(TextWay.getText32(this._dpsMul));
      }
      
      public function inData_byXML(xml0:XML, type0:String = "") : void
      {
         this.type = type0;
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.maxRa = this.maxRa / 180 * Math.PI;
         this.minRa = this.minRa / 180 * Math.PI;
         if(this.swapSoundUrl == "" && type0 != "")
         {
            this.swapSoundUrl = "sound/" + type0 + "_vehicle";
         }
         if(this.cn == "")
         {
            this.cn = VehicleAttackLabel.getCn(this.type);
         }
      }
      
      public function isMainB() : Boolean
      {
         return this.type == "main";
      }
      
      public function isSubB() : Boolean
      {
         return this.type == "sub";
      }
      
      public function isLimitRaB() : Boolean
      {
         return Math.abs(this.maxRa - this.minRa) < Math.PI * 2;
      }
      
      public function getBulletDefine() : BulletDefine
      {
         return Gaming.defineGroup.bullet.getDefine(this.label);
      }
      
      public function getRotateRange() : Number
      {
         var v0:Number = 0;
         v0 = Maths.J_J2(this.maxRa,this.minRa);
         if(Math.abs(v0) <= 0.0001)
         {
            v0 = Math.PI * 2;
         }
         return int(v0 * 180 / Math.PI);
      }
      
      public function getPreBulletNameArr() : Array
      {
         var arr0:Array = [];
         if(this.label != "")
         {
            arr0.push(this.label);
         }
         return arr0;
      }
      
      public function getSoundUrlArr() : Array
      {
         var arr0:Array = [];
         if(this.swapSoundUrl != "")
         {
            arr0.push(this.swapSoundUrl);
         }
         return arr0;
      }
   }
}

