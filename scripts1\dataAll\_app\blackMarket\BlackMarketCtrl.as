package dataAll._app.blackMarket
{
   import dataAll._app.blackMarket.define.BlackMarketDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.save.ItemsSave;
   
   public class BlackMarketCtrl
   {
      
      public static var DG:BlackMarketDefineGroup;
      
      public function BlackMarketCtrl()
      {
         super();
      }
      
      public static function getPrice(da0:IO_ItemsData) : Number
      {
         var s0:ItemsSave = da0.getSave();
         var DG:BlackMarketDefineGroup = Gaming.defineGroup.blackMarket;
         var v0:Number = Gaming.defineGroup.dataList.getValue("blackMartketPrice",s0.getTrueLevel());
         return v0 * DG.getColorMul(s0.color);
      }
      
      public static function getCanLevel() : int
      {
         return 30;
      }
      
      public static function levelCanB() : Boolean
      {
         return Gaming.PG.da.level >= getCanLevel();
      }
   }
}

