package dataAll.gift.zhongQiu
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.TextMethod;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.utils.getTimer;
   
   public class DicePackDefine
   {
      
      private static var obj:Object = {};
      
      private static var arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var cakeB:Boolean = true;
      
      public function DicePackDefine()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         addDefine("kingGold","状元插金花",444411,100);
         addDefine("king1","状元",111111,30);
         addDefine("king2","状元",222222,30);
         addDefine("king3","状元",333333,30);
         addDefine("king4","状元",444444,30);
         addDefine("king5","状元",555555,30);
         addDefine("king6","状元",666666,30);
         addDefine("king11","状元",111110,30);
         addDefine("king12","状元",222220,30);
         addDefine("king13","状元",333330,30);
         addDefine("king14","状元",444440,30);
         addDefine("king15","状元",555550,30);
         addDefine("king16","状元",666660,30);
         addDefine("king44","状元",444400,30);
         addDefine("six","对堂",123456,10);
         addDefine("threeFour","三红",444000,5);
         addDefine("fourThree1","四进",111100,3);
         addDefine("fourThree2","四进",222200,3);
         addDefine("fourThree3","四进",333300,3);
         addDefine("fourThree4","四进",444400,3);
         addDefine("fourThree5","四进",555500,3);
         addDefine("fourThree6","四进",666600,3);
         addDefine("twoFour","二举",440000,1);
         addDefine("oneFour","一秀",400000,3,false);
         addDefine("none","无",0,1,false);
      }
      
      private static function addDefine(name0:String, cn0:String, pack0:int, giftNum0:int, cakeB0:Boolean = true) : void
      {
         var d0:DicePackDefine = new DicePackDefine();
         d0.name = name0;
         d0.cnName = cn0;
         d0.packNum = pack0;
         d0.giftNum = giftNum0;
         d0.cakeB = cakeB0;
         obj[name0] = d0;
         arr.push(d0);
      }
      
      public static function getRan6Arr() : Array
      {
         var n0:int = 0;
         var arr0:Array = [];
         for(var i:int = 0; i < 6; i++)
         {
            n0 = 1 + Math.random() * 6;
            arr0.push(n0);
         }
         return arr0;
      }
      
      public static function getCakeMax() : int
      {
         return 200;
      }
      
      public static function findSameDefine(parr0:Array) : DicePackDefine
      {
         var d0:DicePackDefine = null;
         var pack0:Number = NaN;
         var bb0:Boolean = false;
         for each(d0 in arr)
         {
            pack0 = d0.packNum;
            if(pack0 == 0)
            {
               return d0;
            }
            bb0 = samePan(parr0,pack0);
            if(bb0)
            {
               return d0;
            }
         }
         return arr[arr.length - 1];
      }
      
      private static function samePan(pArr0:Array, p2:int) : Boolean
      {
         var n1:int = 0;
         var f0:int = 0;
         var zeroNum0:int = 0;
         var s2Arr:Array = getNumberArr(p2);
         var len2:int = int(s2Arr.length);
         var len1:int = int(pArr0.length);
         var sameNum0:int = 0;
         for(var i:int = 0; i < len1; i++)
         {
            n1 = int(pArr0[i]);
            f0 = int(s2Arr.indexOf(n1));
            if(f0 >= 0)
            {
               s2Arr[f0] = 0;
               sameNum0++;
            }
         }
         if(sameNum0 >= len2)
         {
            return true;
         }
         zeroNum0 = getZeroNumInArr(s2Arr);
         if(zeroNum0 >= len2)
         {
            return true;
         }
         return false;
      }
      
      private static function getNumberArr(n0:int) : Array
      {
         var e1:String = null;
         var s0:String = String(n0);
         var arr0:Array = [];
         for(var i:int = 0; i < s0.length; i++)
         {
            e1 = s0.charAt(i);
            arr0.push(int(e1));
         }
         return arr0;
      }
      
      private static function getZeroNumInArr(arr0:Array) : int
      {
         var n0:Number = NaN;
         var num0:int = 0;
         for each(n0 in arr0)
         {
            if(n0 == 0)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public static function testSamePan() : void
      {
         var parr0:Array = null;
         var d0:DicePackDefine = null;
         var trace0:String = "";
         var tt0:Number = getTimer();
         for(var j:int = 0; j < 100; j++)
         {
            parr0 = getRan6Arr();
            d0 = findSameDefine(parr0);
            trace0 += "\n" + parr0 + "     " + d0.packNum;
         }
         trace0 += "\n耗时：" + (getTimer() - tt0);
         trace(trace0);
      }
      
      public static function testSamePan2() : void
      {
         var d0:DicePackDefine = null;
         var pack0:int = 0;
         var j:int = 0;
         var parr0:Array = null;
         var i:int = 0;
         var pn0:int = 0;
         var trace0:String = "";
         for each(d0 in arr)
         {
            pack0 = d0.packNum;
            if(pack0 != 0)
            {
               trace0 += "\n测试【" + pack0 + "】==============";
               for(j = 0; j < 10; j++)
               {
                  parr0 = getNumberArr(pack0);
                  parr0.sort(ArrayMethod.randomSortFun);
                  for(i = 0; i < parr0.length; i++)
                  {
                     pn0 = int(parr0[i]);
                     if(pn0 == 0)
                     {
                        pn0 = 1 + Math.random() * 6;
                        parr0[i] = pn0;
                     }
                  }
                  d0 = findSameDefine(parr0);
                  trace0 += "\n" + parr0 + "     " + d0.packNum;
               }
            }
         }
         trace(trace0);
      }
      
      public function get packNum() : Number
      {
         return this.CF.getAttribute("packNum");
      }
      
      public function set packNum(v0:Number) : void
      {
         this.CF.setAttribute("packNum",v0);
      }
      
      public function get giftNum() : Number
      {
         return this.CF.getAttribute("giftNum");
      }
      
      public function set giftNum(v0:Number) : void
      {
         this.CF.setAttribute("giftNum",v0);
      }
      
      public function isZeroB() : Boolean
      {
         return this.packNum == 0;
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         if(this.cakeB)
         {
            g0.addGiftByStr("base;anniCoin;" + this.giftNum);
            g0.addGiftByStr("base;pumpkin;" + this.giftNum);
         }
         else
         {
            g0.addGiftByStr("base;anniCoin;" + this.giftNum);
         }
         return g0;
      }
      
      public function getTipStr() : String
      {
         var s0:String = "";
         if(this.isZeroB())
         {
            s0 = "抱歉，你什么都没博中。";
         }
         else
         {
            s0 = "恭喜你，博中了" + TextMethod.color(this.cnName,"#FFFF00") + "！";
         }
         return s0 + ("\n获得" + (this.cakeB ? "桂花糕" : "纪念币") + "x" + this.giftNum + "。");
      }
   }
}

