package dataAll._app.food
{
   import com.sounto.oldUtils.StringDate;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll.ui.StateIconData;
   import gameAll.body.skill.IO_LifeStateExtraObj;
   
   public class FoodData implements IO_LifeStateExtraObj, IO_FoodAgentFather
   {
      
      private var playerData:PlayerData;
      
      public var save:FoodSave;
      
      private var rawArr:Array = null;
      
      private var rawObj:Object = {};
      
      private var bookArr:Array = null;
      
      private var bookObj:Object = {};
      
      public function FoodData()
      {
         super();
      }
      
      public function setPlayerData(pd0:PlayerData) : void
      {
         this.playerData = pd0;
      }
      
      public function inData_bySave(s0:FoodSave) : void
      {
         this.save = s0;
         this.createRaw();
         this.createBook();
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
         this.save.newDayCtrl(timeDa0);
      }
      
      public function getDayProfiMax() : int
      {
         return 4;
      }
      
      public function getProfiMax() : int
      {
         return 330;
      }
      
      public function getProfiAll() : int
      {
         return this.save.profiAll;
      }
      
      public function addProfi(v0:int, fleshUnlockB0:Boolean = true) : void
      {
         var a0:FoodBookAgent = null;
         var day0:int = this.save.profi;
         var dayMax0:int = this.getDayProfiMax();
         var all0:int = this.save.profiAll;
         var allMax0:int = this.getProfiMax();
         if(day0 + v0 <= dayMax0)
         {
            day0 += v0;
            all0 += v0;
            if(day0 > dayMax0)
            {
               day0 = dayMax0;
            }
            if(all0 > allMax0)
            {
               all0 = allMax0;
            }
            this.save.profi = day0;
            this.save.profiAll = all0;
            if(fleshUnlockB0)
            {
               for each(a0 in this.bookObj)
               {
                  a0.fleshUnlock();
               }
            }
         }
      }
      
      public function getDropMax() : int
      {
         return 10;
      }
      
      public function canDropB() : Boolean
      {
         return this.save.dropNum < this.getDropMax();
      }
      
      public function dropEvent() : void
      {
         ++this.save.dropNum;
         ++this.save.dropAll;
      }
      
      public function getRandomDrop() : FoodRawDefine
      {
         return Gaming.defineGroup.food.getRandomRawDrop();
      }
      
      public function haveEatB() : Boolean
      {
         if(Boolean(this.save))
         {
            return this.save.eatName != "";
         }
         return false;
      }
      
      public function getEatBookAgent() : FoodBookAgent
      {
         return this.getBookAgent(this.save.eatName);
      }
      
      public function eatBook(name0:String) : Boolean
      {
         var num0:int = 1;
         var now0:int = this.getBookNum(name0);
         var d0:FoodBookDefine = Gaming.defineGroup.food.book.getNormalDefine(name0) as FoodBookDefine;
         if(now0 >= num0)
         {
            if(!this.dayEatPan(name0))
            {
               this.save.bookObj.addNum(name0,-num0);
               this.save.eatName = name0;
               this.save.eatTime = d0.buffTime;
               this.save.eatNameArr.push(name0);
               ++this.save.eatNum;
               ++this.save.eatAll;
               return true;
            }
         }
         return false;
      }
      
      public function dayEatPan(name0:String) : Boolean
      {
         return this.save.eatNameArr.indexOf(name0) >= 0;
      }
      
      public function getEatName() : String
      {
         return this.save.eatName;
      }
      
      public function getEatMax() : int
      {
         return 8;
      }
      
      public function isDieB() : Boolean
      {
         return !this.haveEatB() || this.save.eatTime <= 0;
      }
      
      public function getPlayerStateDataArr(lg0:IO_PlayerLevelGetter) : Array
      {
         var a0:FoodBookAgent = null;
         var da0:StateIconData = null;
         var arr0:Array = [];
         if(this.haveEatB() && Boolean(lg0.canFoodB()))
         {
            a0 = this.getEatBookAgent();
            da0 = new StateIconData();
            da0.name = a0.name;
            da0.iconUrl = a0.getDef().iconUrl16;
            da0.time = this.save.eatTime;
            arr0.push(da0);
         }
         return arr0;
      }
      
      private function createRaw() : void
      {
         var darr0:Array = null;
         var d0:FoodRawDefine = null;
         var a0:FoodRawAgent = null;
         if(this.rawArr == null)
         {
            this.rawArr = [];
            this.rawObj = {};
            darr0 = Gaming.defineGroup.food.raw.arr;
            for each(d0 in darr0)
            {
               a0 = new FoodRawAgent();
               a0.init(d0,this);
               this.rawArr.push(a0);
               this.rawObj[d0.name] = a0;
            }
         }
      }
      
      public function getRawNum(name0:String) : int
      {
         return this.save.rawObj.getAttribute(name0);
      }
      
      public function getRawArr() : Array
      {
         return this.rawArr;
      }
      
      public function getRawAgent(name0:String) : FoodRawAgent
      {
         return this.rawObj[name0];
      }
      
      public function addRawNum(name0:String, num0:int) : void
      {
         if(num0 > 0)
         {
            this.save.rawObj.addNum(name0,num0);
         }
      }
      
      private function useRawNum(name0:String, num0:int) : void
      {
         if(num0 > 0)
         {
            this.save.rawObj.addNum(name0,-num0);
         }
      }
      
      public function addRawAll(num0:int) : void
      {
         var d0:FoodRawDefine = null;
         var darr0:Array = Gaming.defineGroup.food.raw.arr;
         for each(d0 in darr0)
         {
            this.addRawNum(d0.name,num0);
         }
      }
      
      private function createBook() : void
      {
         var darr0:Array = null;
         var d0:FoodBookDefine = null;
         var a0:FoodBookAgent = null;
         if(this.bookArr == null)
         {
            this.bookArr = [];
            this.bookObj = {};
            darr0 = Gaming.defineGroup.food.book.arr;
            for each(d0 in darr0)
            {
               a0 = new FoodBookAgent();
               a0.init(d0,this);
               this.bookArr.push(a0);
               this.bookObj[d0.name] = a0;
            }
         }
      }
      
      public function getBookNum(name0:String) : int
      {
         return this.save.bookObj.getAttribute(name0);
      }
      
      private function addBookNum(name0:String, num0:int) : void
      {
         if(num0 > 0)
         {
            this.save.bookObj.addNum(name0,num0);
         }
      }
      
      public function getBookArr() : Array
      {
         return this.bookArr;
      }
      
      public function getBookAgent(name0:String) : FoodBookAgent
      {
         return this.bookObj[name0];
      }
      
      public function getUnlockText() : String
      {
         var a0:FoodBookAgent = null;
         var unlockNum0:int = 0;
         for each(a0 in this.bookArr)
         {
            if(a0.unlockB)
            {
               unlockNum0++;
            }
         }
         return "已解锁 " + unlockNum0 + "/" + this.bookArr.length;
      }
      
      public function composeBook(name0:String) : void
      {
         var rawName0:String = null;
         var d0:FoodBookDefine = Gaming.defineGroup.food.book.getNormalDefine(name0) as FoodBookDefine;
         var rawArr0:Array = d0.getRawArr();
         for each(rawName0 in rawArr0)
         {
            this.useRawNum(rawName0,1);
         }
         this.addBookNum(name0,1);
         this.addProfi(1);
      }
      
      public function FTimerSecond(gamingB0:Boolean, canB0:Boolean) : void
      {
         var eat0:String = null;
         var time0:Number = NaN;
         if(gamingB0 && canB0)
         {
            eat0 = this.save.eatName;
            if(eat0 != "")
            {
               time0 = this.save.eatTime;
               if(time0 >= 1)
               {
                  time0 -= 1;
               }
               else
               {
                  time0 = 0;
                  this.save.eatName = "";
               }
               this.save.eatTime = time0;
            }
         }
      }
   }
}

