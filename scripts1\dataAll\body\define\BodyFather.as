package dataAll.body.define
{
   public class BodyFather
   {
      
      public static const enemy:String = "enemy";
      
      public static const we:String = "we";
      
      public static const wilder:String = "wilder";
      
      public static const other:String = "other";
      
      public static const pet:String = "pet";
      
      public static const vehicle:String = "vehicle";
      
      public static const editArr:Array = [enemy,wilder,we,vehicle];
      
      public static const sumArr:Array = [enemy,wilder];
      
      public static const noSumNameArr:Array = ["Madboss"];
      
      public static const noSumCnStr:String = "战神、尸宠、载具、我方单位";
      
      public static const friable:String = "friable";
      
      public static const craft:String = "craft";
      
      public static const space:String = "space";
      
      public static const ranStandArr:Array = ["OreBomb","OreBall","WorldSnakeTail"];
      
      public static const headSameHurtArr:Array = [friable,craft,space];
      
      public static const bosseditLocalArr:Array = [pet,other,space,craft,friable];
      
      public function BodyFather()
      {
         super();
      }
   }
}

