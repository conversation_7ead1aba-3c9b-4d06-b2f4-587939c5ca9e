package dataAll._app.goods.define
{
   import com.sounto.utils.ClassProperty;
   
   public class GoodsFatherDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var priceType:* = PriceType.COIN;
      
      public var labelArr:Array = [];
      
      public var type:String = GoodsFatherType.SHOP;
      
      public function GoodsFatherDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getLabelCnArr() : Array
      {
         var str0:String = null;
         var arr0:Array = [];
         for each(str0 in this.labelArr)
         {
            if(str0 == "all")
            {
               arr0.push("新品");
            }
            else if(str0 == "hot")
            {
               arr0.push("热销");
            }
            else if(str0 == "dis")
            {
               arr0.push("打折");
            }
            else if(str0 == "props")
            {
               arr0.push("道具");
            }
            else if(str0 == "materials")
            {
               arr0.push("材料");
            }
            else if(str0 == "arms")
            {
               arr0.push("武器");
            }
            else if(str0 == "equip")
            {
               arr0.push("装备");
            }
            else if(str0 == "fashion")
            {
               arr0.push("时装");
            }
            else if(str0 == "other")
            {
               arr0.push("其他");
            }
            else if(str0 == "vehicle")
            {
               arr0.push("载具");
            }
            else if(str0 == "parts")
            {
               arr0.push("零件");
            }
            else if(str0 == "gene")
            {
               arr0.push("基因体");
            }
            else if(str0 == "chip")
            {
               arr0.push("碎片");
            }
            else if(str0 == "wilder")
            {
               arr0.push("秘境");
            }
         }
         return arr0;
      }
   }
}

