package com.sounto.math
{
   import com.sounto.pool.OnePool;
   import com.sounto.utils.DisplayMethod;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class RectMethod
   {
      
      private static const pool:OnePool = new OnePool();
      
      private static var p:Point = new Point();
      
      public function RectMethod()
      {
         super();
         pool.maxNum = 1000;
      }
      
      public static function getNewRect() : Rectangle
      {
         var r0:Rectangle = pool.getObject() as Rectangle;
         if(!r0)
         {
            return new Rectangle();
         }
         return r0;
      }
      
      public static function recoverPool(r0:Rectangle) : void
      {
         r0.x = 0;
         r0.y = 0;
         r0.width = 0;
         r0.height = 0;
         pool.addObject(r0);
      }
      
      public static function countByBody(r0:Rectangle, x0:Number, y0:Number, ra0:Number, scaleX0:Number, tempRect0:Rectangle = null) : Rectangle
      {
         var r2:Rectangle = tempRect0;
         if(!r2)
         {
            r2 = getNewRect();
         }
         r2.width = r0.width;
         r2.height = r0.height;
         var mx0:Number = r0.x + r0.width / 2;
         var my0:Number = r0.y + r0.height / 2;
         var mp2:Point = DisplayMethod.getTempTruePoint(mx0,my0,x0,y0,ra0,scaleX0);
         r2.x = mp2.x - r2.width / 2;
         r2.y = mp2.y - r2.height / 2;
         return r2;
      }
      
      public static function countArrByBody(arr0:Array, x0:Number, y0:Number, ra0:Number, scaleX0:Number) : Array
      {
         var r0:Rectangle = null;
         var arr2:Array = [];
         for each(r0 in arr0)
         {
            arr2.push(countByBody(r0,x0,y0,ra0,scaleX0));
         }
         return arr2;
      }
      
      public static function getMergeRect(arr0:Array, scaleMul0:Number = 1) : IDRect
      {
         var index0:int = 0;
         var max_x:int = 0;
         var max_y:int = 0;
         var max_x2:int = 0;
         var max_y2:int = 0;
         var rect0:Rectangle = null;
         var idr:IDRect = null;
         var mul0:Number = NaN;
         if(arr0.length == 0)
         {
            return null;
         }
         index0 = 0;
         max_x = 0;
         max_y = 0;
         max_x2 = 0;
         max_y2 = 0;
         for each(rect0 in arr0)
         {
            if(index0 == 0)
            {
               max_x = rect0.x;
               max_y = rect0.y;
               max_x2 = rect0.right;
               max_y2 = rect0.bottom;
            }
            else
            {
               if(rect0.x < max_x)
               {
                  max_x = rect0.x;
               }
               if(rect0.y < max_y)
               {
                  max_y = rect0.y;
               }
               if(rect0.right > max_x2)
               {
                  max_x2 = rect0.right;
               }
               if(rect0.bottom > max_y2)
               {
                  max_y2 = rect0.bottom;
               }
            }
            index0++;
         }
         idr = new IDRect();
         idr.x = max_x;
         idr.y = max_y;
         idr.width = max_x2 - max_x;
         idr.height = max_y2 - max_y;
         mul0 = 1 - scaleMul0;
         idr.x += idr.width * mul0;
         idr.width -= idr.width * mul0 * 2;
         idr.y += idr.height * mul0;
         idr.height -= idr.height * mul0 * 1.5;
         return idr;
      }
      
      public static function inData(rect0:Rectangle, d0:Rectangle) : void
      {
         rect0.x = d0.x;
         rect0.y = d0.y;
         rect0.width = d0.width;
         rect0.height = d0.height;
      }
      
      public static function inDataAndScale(rect0:Rectangle, d0:Rectangle, scale:Number) : void
      {
         inData(rect0,d0);
         scaleRect(rect0,scale);
      }
      
      public static function scaleRect(rect0:Rectangle, scale:Number) : void
      {
         rect0.x *= scale;
         rect0.y *= scale;
         rect0.width *= scale;
         rect0.height *= scale;
      }
      
      public static function scaleRectNoMove(rect0:Rectangle, scale:Number) : void
      {
         rect0.width *= scale;
         rect0.height *= scale;
         rect0.x += rect0.width * (1 - scale) * 0.5;
         rect0.y += rect0.height * (1 - scale) * 0.5;
      }
      
      public static function scaleRectSize(rect0:Rectangle, size0:Number) : void
      {
         rect0.x += size0;
         rect0.y += size0;
         rect0.width -= size0 * 2;
         rect0.height -= size0 * 2;
      }
      
      public static function xLimitInRect(x0:Number, rect0:Rectangle) : Number
      {
         if(x0 > rect0.x + rect0.width)
         {
            x0 = rect0.x + rect0.width;
         }
         if(x0 < rect0.x)
         {
            x0 = rect0.x;
         }
         return x0;
      }
      
      public static function yLimitInRect(y0:Number, rect0:Rectangle) : Number
      {
         if(y0 > rect0.y + rect0.height)
         {
            y0 = rect0.y + rect0.height;
         }
         if(y0 < rect0.y)
         {
            y0 = rect0.y;
         }
         return y0;
      }
      
      public static function limitRect(r0:Rectangle, range0:Rectangle) : void
      {
         var c0:Number = 0;
         if(r0.x < range0.x)
         {
            c0 = range0.x - r0.x;
            r0.width -= c0;
            if(r0.width < 0)
            {
               r0.width = 0;
            }
            r0.x = range0.x;
         }
         if(r0.y < range0.y)
         {
            c0 = range0.y - r0.y;
            r0.height -= c0;
            if(r0.height < 0)
            {
               r0.height = 0;
            }
            r0.y = range0.y;
         }
         if(r0.x + r0.width > range0.x + range0.width)
         {
            c0 = r0.x + r0.width - (range0.x + range0.width);
            r0.width -= c0;
            if(r0.width < 0)
            {
               r0.width = 0;
            }
         }
         if(r0.y + r0.height > range0.y + range0.height)
         {
            c0 = r0.y + r0.height - (range0.y + range0.height);
            r0.height -= c0;
            if(r0.height < 0)
            {
               r0.height = 0;
            }
         }
      }
      
      public static function getRanPoint(rect0:Rectangle) : Point
      {
         p.x = rect0.x + rect0.width * Math.random();
         p.y = rect0.y + rect0.height * Math.random();
         return p;
      }
   }
}

