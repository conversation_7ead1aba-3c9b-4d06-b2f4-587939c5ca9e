package dataAll.body.attack
{
   public class ElementShell
   {
      
      public static const compound:String = "compound";
      
      public static const metal:String = "metal";
      
      public static const variation:String = "variation";
      
      public static const normal:String = "normal";
      
      public static const other:String = "other";
      
      public static const arr:Array = [compound,metal,variation,normal,other];
      
      private static var defineObj:Object = {};
      
      public function ElementShell()
      {
         super();
      }
      
      public static function defineInit() : void
      {
         var d0:ElementShellDefine = null;
         d0 = new ElementShellDefine();
         d0.name = compound;
         d0.cnName = "复合";
         defineObj[d0.name] = d0;
         d0 = new ElementShellDefine();
         d0.name = metal;
         d0.cnName = "金属";
         defineObj[d0.name] = d0;
         d0 = new ElementShellDefine();
         d0.name = variation;
         d0.cnName = "变异";
         defineObj[d0.name] = d0;
         d0 = new ElementShellDefine();
         d0.name = normal;
         d0.cnName = "正常";
         defineObj[d0.name] = d0;
         d0 = new ElementShellDefine();
         d0.name = other;
         d0.cnName = "其他";
         defineObj[d0.name] = d0;
      }
      
      public static function getDefine(name0:String) : ElementShellDefine
      {
         return defineObj[name0];
      }
   }
}

