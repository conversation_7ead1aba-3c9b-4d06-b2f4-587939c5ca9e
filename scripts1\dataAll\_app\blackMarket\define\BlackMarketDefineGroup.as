package dataAll._app.blackMarket.define
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.things.define.ThingsDefine;
   
   public class BlackMarketDefineGroup
   {
      
      public var way:BlackMarketWayDefineGroup = new BlackMarketWayDefineGroup();
      
      public var leftLabel:BlackMarketLeftLabelDefine = new BlackMarketLeftLabelDefine();
      
      private var colorMul:Object = {};
      
      private var typeMul:Object = {};
      
      private var armsMul:Object = {};
      
      private var equipMul:Object = {};
      
      private var thingsGiftArr:Array = [];
      
      public function BlackMarketDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.way.inData_byXML(xml0.wayGather[0]);
         this.inMulData_byXML(xml0.mulGather[0]);
      }
      
      private function inMulData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var x0:XML = null;
         var label0:String = null;
         var xl2:XMLList = null;
         var obj0:Object = null;
         var i:* = undefined;
         var x2:XML = null;
         var name0:String = null;
         var xl0:XMLList = xml0.elements("*");
         for(n in xl0)
         {
            x0 = xl0[n];
            label0 = x0.name().localName;
            xl2 = x0.elements("*");
            obj0 = {};
            for(i in xl2)
            {
               x2 = xl2[i];
               name0 = x2.name().localName;
               obj0[name0] = Number(x2);
            }
            this[label0] = obj0;
         }
      }
      
      public function getColorMul(color0:String) : Number
      {
         return this.colorMul[color0];
      }
      
      public function getTypeMul(type0:String) : Number
      {
         return this.typeMul[type0];
      }
      
      public function getArmsMul(type0:String) : Number
      {
         return this.armsMul[type0];
      }
      
      public function getEquipMul(type0:String) : Number
      {
         return this.equipMul[type0];
      }
      
      public function getGoodsDefineArr() : Array
      {
         var darr0:Array = null;
         var type0:String = null;
         var price0:Number = NaN;
         var pro0:Number = NaN;
         var g0:GiftAddDefineGroup = null;
         var gift0:GiftAddDefine = null;
         var thingsD0:ThingsDefine = null;
         var d0:GoodsDefine = null;
         var gift2:GiftAddDefine = null;
         var typeArr0:Array = ["normal","good","rare"];
         var priceArr0:Array = [500,1200,2000];
         var proArr0:Array = [0.5,0.3,0.2];
         var arr_len0:int = int(typeArr0.length);
         darr0 = [];
         for(var i:int = 0; i < arr_len0; i++)
         {
            type0 = typeArr0[i];
            price0 = Number(priceArr0[i]);
            pro0 = Number(proArr0[i]);
            g0 = Gaming.defineGroup.gift.getOne("blackMarketThings_" + type0);
            for each(gift0 in g0.arr)
            {
               thingsD0 = Gaming.defineGroup.things.getDefine(gift0.name);
               d0 = new GoodsDefine();
               d0.name = gift0.name + "_tax";
               d0.cnName = thingsD0.cnName;
               d0.defineLabel = gift0.name;
               d0.chooseNumB = false;
               d0.price = price0;
               d0.num = gift0.num;
               d0.priceType = PriceType.TAX_STAMP;
               d0.inBagType = d0.dataType;
               d0.fleshAllByDefine();
               darr0.push(d0);
               gift2 = gift0.clone();
               gift2.pro = pro0;
               this.thingsGiftArr.push(gift2);
            }
         }
         return darr0;
      }
      
      public function getGoodsNameArr(num0:int) : Array
      {
         var d0:GiftAddDefine = null;
         var darr0:Array = ArrayMethod.getRandomArray(this.thingsGiftArr,num0,false,"pro");
         var narr0:Array = [];
         for each(d0 in darr0)
         {
            narr0.push(d0.name + "_tax");
         }
         return narr0;
      }
   }
}

