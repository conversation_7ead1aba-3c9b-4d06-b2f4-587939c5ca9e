package dataAll.pay
{
   import com.sounto.utils.ClassProperty;
   
   public class OneDayPaySave
   {
      
      public static var pro_arr:Array = [];
      
      public var obj:Object = {};
      
      public function OneDayPaySave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
      }
      
      public function add(propsId0:String, num0:int) : void
      {
         if(this.obj.hasOwnProperty(propsId0))
         {
            this.obj[propsId0] += num0;
         }
         else
         {
            this.obj[propsId0] = num0;
         }
      }
   }
}

