package dataAll._app.city.dress
{
   import com.sounto.utils.ClassProperty;
   
   public class CityDressSave
   {
      
      public static var pro_arr:Array = null;
      
      public var type:String = "";
      
      public var id:String = "";
      
      public var x:int = 0;
      
      public var y:int = 0;
      
      public var saveId:String = "";
      
      public function CityDressSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
   }
}

