package dataAll.gift.define
{
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.dailySign.DailyGiftDefineGroup;
   
   public class GiftDefineGroup
   {
      
      public var daily:DailyGiftDefineGroup = new DailyGiftDefineGroup();
      
      private var obj:Object = {};
      
      private var fatherObj:Object = {};
      
      private var fatherArrObj:Object = {};
      
      public function GiftDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var x0:XML = null;
         var father0:String = null;
         var xml_list0:XMLList = null;
         var i:* = undefined;
         var barXML0:XML = null;
         var d0:GiftAddDefineGroup = null;
         if(Boolean(xml0.daily[0]))
         {
            this.daily.inData_byXML(xml0.daily[0]);
         }
         var xl0:XMLList = xml0.*;
         for(n in xl0)
         {
            x0 = xl0[n];
            father0 = x0.localName();
            xml_list0 = x0.one;
            if(Boolean(xml_list0))
            {
               for(i in xml_list0)
               {
                  barXML0 = xml_list0[i];
                  d0 = null;
                  if(Boolean(barXML0.hasOwnProperty("@codeExchangeId")) || (father0 == "activity" || father0 == "arenaSeason"))
                  {
                     d0 = new ExchangeGiftAddDefineGroup();
                  }
                  else
                  {
                     d0 = new GiftAddDefineGroup();
                  }
                  d0.inData_byOneXML(barXML0,father0);
                  this.obj[d0.name] = d0;
                  if(!this.fatherObj.hasOwnProperty(father0))
                  {
                     this.fatherObj[father0] = {};
                  }
                  this.fatherObj[father0][d0.name] = d0;
                  if(!this.fatherArrObj.hasOwnProperty(father0))
                  {
                     this.fatherArrObj[father0] = [];
                  }
                  this.fatherArrObj[father0].push(d0);
               }
            }
         }
      }
      
      public function getOne(name0:String) : GiftAddDefineGroup
      {
         return this.obj[name0];
      }
      
      public function getArrByFather(name0:String, nowTimeDate0:StringDate = null) : Array
      {
         var arr2:Array = null;
         var d0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var d2:ExchangeGiftAddDefineGroup = null;
         var arr0:Array = this.fatherArrObj[name0];
         if(Boolean(nowTimeDate0))
         {
            arr2 = [];
            for each(d0 in arr0)
            {
               bb0 = true;
               if(d0 is ExchangeGiftAddDefineGroup)
               {
                  d2 = d0 as ExchangeGiftAddDefineGroup;
                  if(d2.hideOutTimeB)
                  {
                     if(!d2.isStartB(nowTimeDate0) || d2.isEndB(nowTimeDate0))
                     {
                        bb0 = false;
                     }
                  }
               }
               if(bb0)
               {
                  arr2.push(d0);
               }
            }
            arr0 = arr2;
         }
         return arr0;
      }
      
      public function getOneByExchangeCode(id0:String) : GiftAddDefineGroup
      {
         var n:* = undefined;
         var d0:ExchangeGiftAddDefineGroup = null;
         var obj0:Object = this.obj;
         for(n in obj0)
         {
            d0 = obj0[n] as ExchangeGiftAddDefineGroup;
            if(Boolean(d0))
            {
               if(d0.codeExchangeId == id0)
               {
                  return d0;
               }
            }
         }
         return null;
      }
   }
}

