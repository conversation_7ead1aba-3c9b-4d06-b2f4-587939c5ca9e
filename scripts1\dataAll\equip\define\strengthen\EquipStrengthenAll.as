package dataAll.equip.define.strengthen
{
   import dataAll.equip.creator.EquipStrengthenCtrl;
   import dataAll.items.creator.ItemsStrengthenCtrl;
   import dataAll.pro.PropertyArrayDefineGroup;
   
   public class EquipStrengthenAll extends PropertyArrayDefineGroup
   {
      
      private static var MAX:int = 27;
      
      private var echelonLv:int = 0;
      
      private var vv:int = 0;
      
      public function EquipStrengthenAll()
      {
         super();
      }
      
      public function test(v0:int) : void
      {
         var arr0:Array = null;
         var n:int = 0;
         this.echelonLv = 0;
         this.vv = v0;
         var num0:int = 2000;
         var allArr0:Array = [];
         for(var i:int = 0; i < num0; i++)
         {
            arr0 = this.testArr();
            for(n = 0; n < arr0.length; n++)
            {
               if(!allArr0[n])
               {
                  allArr0[n] = 0;
               }
               if(Bo<PERSON>an(arr0[n]) && !isNaN(arr0[n]))
               {
                  allArr0[n] += arr0[n] / num0;
               }
            }
         }
         this.showArr(allArr0);
      }
      
      private function showArr(arr0:Array) : void
      {
         var str0:String = "";
         for(var n:int = 0; n < arr0.length; n++)
         {
            str0 += Number(arr0[n]).toFixed(2) + "\n";
         }
         trace(str0);
      }
      
      public function testArr() : Array
      {
         var successRate0:Number = NaN;
         var arr0:Array = [];
         var nowLv0:int = 0;
         var stoneNum0:int = 0;
         var echelonNum0:int = 0;
         var haveRacordLv0:int = 0;
         while(nowLv0 < MAX)
         {
            successRate0 = getPropertyValue("successRate",nowLv0 + 1);
            stoneNum0 += getPropertyValue("mustNum",nowLv0 + 1);
            echelonNum0 += ItemsStrengthenCtrl.getEchelonNum(nowLv0,haveRacordLv0);
            nowLv0 = this.testOne(nowLv0,haveRacordLv0);
            if(haveRacordLv0 < nowLv0)
            {
               haveRacordLv0 = nowLv0;
               if(this.vv == 0)
               {
                  arr0[nowLv0 - 1] = stoneNum0;
               }
               else if(this.vv == 1)
               {
                  arr0[nowLv0 - 1] = echelonNum0;
               }
               else if(this.vv == 2)
               {
                  ++arr0[nowLv0 - 1];
               }
            }
         }
         return arr0;
      }
      
      private function testOne(nowLv0:int, sMaxLv0:int) : int
      {
         var strengthenLv0:int = 0;
         var minLv0:int = 0;
         var successRate0:Number = getPropertyValue("successRate",nowLv0 + 1);
         if(Math.random() <= successRate0)
         {
            return this.getStrengthenLv(nowLv0);
         }
         minLv0 = ItemsStrengthenCtrl.getMinLvByMaxLv(sMaxLv0);
         if(nowLv0 > 0)
         {
            if(nowLv0 <= minLv0)
            {
               return nowLv0;
            }
            if(nowLv0 > this.echelonLv)
            {
               nowLv0 -= 1;
            }
            return nowLv0;
         }
         return 0;
      }
      
      private function getStrengthenLv(nowLv0:int) : int
      {
         return EquipStrengthenCtrl.getStrengthenLv(nowLv0,MAX);
      }
   }
}

