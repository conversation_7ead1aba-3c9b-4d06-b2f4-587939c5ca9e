package dataAll.pro
{
   public class PropertyArrayDefineGroup
   {
      
      public var propertyObj:Object = {};
      
      public var propertyArr:Array = [];
      
      private var nameArr:Array = [];
      
      public function PropertyArrayDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var pro_xml0:XML = null;
         var d0:PropertyArrayDefine = null;
         var proArr0:XMLList = xml0.pro;
         for(n in proArr0)
         {
            pro_xml0 = proArr0[n];
            d0 = this.getNewDefine();
            d0.inData_byXML(pro_xml0);
            this.propertyObj[d0.name] = d0;
            this.propertyArr[n] = d0;
            this.nameArr.push(d0.name);
         }
      }
      
      protected function getNewDefine() : PropertyArrayDefine
      {
         return new PropertyArrayDefine();
      }
      
      public function getDefine(name0:String) : PropertyArrayDefine
      {
         return this.propertyObj[name0];
      }
      
      public function getLvInOnePro(v0:Number, proName0:String) : int
      {
         var v2:Number = NaN;
         for(var i:int = 1; i < 200; i++)
         {
            v2 = this.getPropertyValue(proName0,i);
            if(v2 >= v0)
            {
               return i;
            }
         }
         return 200;
      }
      
      public function getMaxInOnePro(v0:Number, proName0:String) : int
      {
         var v2:Number = NaN;
         var d0:PropertyArrayDefine = this.getDefine(proName0);
         for(var i:int = 1; i <= d0.dataArr.length; i++)
         {
            v2 = this.getPropertyValue(proName0,i);
            if(v2 > v0)
            {
               if(i <= 1)
               {
                  i = 2;
               }
               return i - 1;
            }
         }
         return d0.dataArr.length;
      }
      
      public function getRandomByRange(pro0:String, lv0:int, lv1:int) : Number
      {
         var value0:Number = this.getPropertyValue(pro0,lv0);
         var value1:Number = this.getPropertyValue(pro0,lv0);
         return value0 + Math.random() * (value1 - value0);
      }
      
      public function getPropertyValue(pro0:String, lv0:int) : Number
      {
         var d0:PropertyArrayDefine = this.propertyObj[pro0];
         if(d0 is PropertyArrayDefine)
         {
            return d0.getValue(lv0);
         }
         return -1;
      }
      
      public function getNameArr() : Array
      {
         return this.nameArr.concat([]);
      }
      
      public function getCnNameArrByArr(arr0:Array) : Array
      {
         var name0:String = null;
         var d0:PropertyArrayDefine = null;
         var cnArr0:Array = [];
         for each(name0 in arr0)
         {
            d0 = this.getDefine(name0);
            cnArr0.push(d0.cnName);
         }
         return cnArr0;
      }
      
      public function getCnNameArr() : Array
      {
         var d0:PropertyArrayDefine = null;
         var arr0:Array = [];
         for each(d0 in this.propertyArr)
         {
            arr0.push(d0.cnName);
         }
         return arr0;
      }
   }
}

