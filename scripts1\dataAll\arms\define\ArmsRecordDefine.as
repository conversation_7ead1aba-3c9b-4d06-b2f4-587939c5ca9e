package dataAll.arms.define
{
   import com.sounto.utils.ClassProperty;
   
   public class ArmsRecordDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var moveGap:Number = 0;
      
      public var piano:String = "";
      
      public function ArmsRecordDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}

