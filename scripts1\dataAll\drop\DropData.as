package dataAll.drop
{
   import dataAll._app.worldMap.define.MapMode;
   
   public class DropData
   {
      
      public var save:DropSave;
      
      public function DropData()
      {
         super();
      }
      
      public function inData_bySave(s0:DropSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl() : void
      {
         this.save.newDayCtrl();
      }
      
      public function newWeek() : void
      {
         this.save.newWeek();
      }
      
      public function addThingsName(name0:String, num0:int) : void
      {
         if(name0 == "normalChest")
         {
            this.save.normalChestNum += num0;
         }
         else if(name0 == "magicChest")
         {
            this.save.magicChestNum += num0;
         }
         else if(name0 == "tigerChest")
         {
            ++this.save.tigerChest;
         }
      }
      
      public function deviceDrop() : void
      {
         this.save.deviceNum += 1;
      }
      
      public function getDeviceDropPro(diff0:int, mapMode0:String) : Number
      {
         var addMul0:Number = 1.5;
         var pro0:Number = diff0 * 0.15 + 0.1;
         if(diff0 >= 4)
         {
            pro0 += 0.35;
         }
         if(mapMode0 == MapMode.DEMON)
         {
            pro0 = 1.2;
         }
         pro0 *= 1.5;
         var num0:int = this.save.deviceNum;
         if(num0 < 30 * addMul0)
         {
            pro0 *= 0.2;
         }
         else
         {
            pro0 *= 0.04;
         }
         return pro0 * addMul0;
      }
      
      public function weaponDrop() : void
      {
         this.save.weaponNum += 1;
      }
      
      public function getWeaponDropPro(diff0:int, mapMode0:String) : Number
      {
         var pro0:Number = diff0 * 0.15 + 0.1;
         if(diff0 >= 4)
         {
            pro0 += 0.35;
         }
         if(mapMode0 == MapMode.DEMON)
         {
            pro0 = 1.2;
         }
         var num0:int = this.save.weaponNum;
         if(num0 < 5)
         {
            pro0 *= 0.2;
         }
         else
         {
            pro0 *= 0.04;
         }
         return pro0;
      }
      
      public function testDevice() : void
      {
      }
      
      public function testWeapon() : void
      {
      }
   }
}

