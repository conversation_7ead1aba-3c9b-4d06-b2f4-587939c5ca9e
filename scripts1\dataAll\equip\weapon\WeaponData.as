package dataAll.equip.weapon
{
   import com.common.text.TextWay;
   import com.sounto.utils.StringMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.arms.skin.IO_FashionSkinData;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.must.define.MustDefine;
   
   public class WeaponData extends EquipData implements IO_FashionSkinData
   {
      
      public var weaponDefine:WeaponDefine;
      
      public function WeaponData()
      {
         super();
      }
      
      public function getWeaponSave() : WeaponSave
      {
         return save as WeaponSave;
      }
      
      override public function inData_bySave(s0:EquipSave, pd0:NormalPlayerData) : void
      {
         super.inData_bySave(s0,pd0);
         this.weaponDefine = this.getWeaponSave().getWeaponDefine();
         save.obj = this.weaponDefine.getAddObj();
      }
      
      override public function clone() : EquipData
      {
         var da0:WeaponData = super.clone() as WeaponData;
         da0.weaponDefine = this.weaponDefine;
         return da0;
      }
      
      override public function isCanNumSwapB() : Boolean
      {
         return true;
      }
      
      override public function isCanOverlayB() : Boolean
      {
         return true;
      }
      
      override public function getNowNum() : int
      {
         return save.nowNum;
      }
      
      override public function setNowNum(num0:int) : void
      {
         save.nowNum = num0;
      }
      
      override public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
         addNowTrueNum(num0,otherDa0);
      }
      
      override public function getSellPrice() : Number
      {
         return save.nowNum * 2000 * save.itemsLevel;
      }
      
      public function getUpradeData() : WeaponData
      {
         var s0:WeaponSave = null;
         var da0:WeaponData = null;
         var upgradeName0:String = this.weaponDefine.getUpgradeName();
         var d0:WeaponDefine = Gaming.defineGroup.weapon.getDefine(upgradeName0);
         if(Boolean(d0))
         {
            s0 = WeaponDataCreator.getSave(upgradeName0,1);
            da0 = new WeaponData();
            da0.inData_bySave(s0,normalPlayerData);
            return da0;
         }
         return null;
      }
      
      public function getUpradeMust() : MustDefine
      {
         return WeaponDataCreator.getUpgradeMust(this,save.itemsLevel + 1);
      }
      
      public function changeToOneData(da0:WeaponData) : void
      {
         var name0:String = null;
         var proArr0:Array = ["skillArr","imgName","nowNum","itemsLevel","name","cnName","newB","lockB"];
         for each(name0 in proArr0)
         {
            save[name0] = da0.save[name0];
         }
         this.weaponDefine = this.getWeaponSave().getWeaponDefine();
         save.obj = this.weaponDefine.getAddObj();
      }
      
      public function getHurtValue() : Number
      {
         var relatedMax0:Number = normalPlayerData.arms.getMaxDpsByType(this.weaponDefine.armsType);
         if(relatedMax0 == 0)
         {
            relatedMax0 = normalPlayerData.arms.getMaxDpsByType("") / 4;
         }
         relatedMax0 *= this.weaponDefine.hurtMul;
         return Math.ceil(relatedMax0);
      }
      
      public function getOneAllHurtValue() : Number
      {
         return this.getHurtValue() * this.weaponDefine.hitNum;
      }
      
      override public function getGatherTip(compareDa0:EquipData = null, tipType0:String = "", showSkillB:Boolean = true) : String
      {
         return this.weaponDefine.getGatherTip(save.getTrueObj(),this.getHurtValue(),getNowHaveDay());
      }
      
      public function getUIUpgradeGatherStr() : String
      {
         var cnArr0:Array = null;
         var str0:String = "";
         str0 += "<b><orange " + this.weaponDefine.cnName + " " + this.weaponDefine.lv + "级/></b>";
         str0 += TextWay.replaceStr(this.weaponDefine.getProGatherStr(this.getHurtValue()),"|","：");
         var skillArr0:Array = this.weaponDefine.getShowSkillArr();
         if(skillArr0.length > 0)
         {
            cnArr0 = Gaming.defineGroup.skill.getCnArrByNameArr(skillArr0);
            str0 += "\n<gray 技能/>：<yellow " + StringMethod.concatStringArr(cnArr0,99) + "/>";
         }
         return str0;
      }
      
      public function getBaseWeaponUrl() : String
      {
         return this.weaponDefine.weaponUrl;
      }
      
      public function getWeaponUrl(fashionDefine0:EquipDefine = null) : String
      {
         var url0:String = this.getBaseWeaponUrl();
         var d0:ArmsSkinDefine = Gaming.defineGroup.armsCharger.getSkin(this.getNowSkin());
         if(Boolean(d0))
         {
            url0 = d0.getUrl();
         }
         if(Boolean(fashionDefine0))
         {
            d0 = fashionDefine0.getWeaponSkinD();
            if(Boolean(d0))
            {
               if(this.getWeaponSave().cf == false)
               {
                  url0 = d0.getUrl();
               }
            }
         }
         return url0;
      }
      
      override public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         var d0:ArmsSkinDefine = this.getNowSkinDefine();
         if(Boolean(d0))
         {
            return d0.getIconUrl();
         }
         return super.getIconImgUrl(maxWidth0,maxHeight0);
      }
      
      public function getBaseSkin() : String
      {
         return this.weaponDefine.getSkinName();
      }
      
      public function getNowSkin() : String
      {
         if(this.getWeaponSave().s == "")
         {
            return this.getBaseSkin();
         }
         return this.getWeaponSave().s;
      }
      
      public function getNowSkinDefine() : ArmsSkinDefine
      {
         return Gaming.defineGroup.armsCharger.getSkin(this.getNowSkin());
      }
      
      override public function haveMoreSkinB() : Boolean
      {
         return this.getSkinDefArr();
      }
      
      public function getSkinDefArr() : Array
      {
         return Gaming.defineGroup.armsCharger.getSkinArr(this.weaponDefine.baseLabel);
      }
      
      public function getEvoLv() : int
      {
         return save.getTrueLevel();
      }
      
      public function setSkin(name0:String) : void
      {
         if(name0 == this.getBaseSkin())
         {
            this.getWeaponSave().s = "";
         }
         else
         {
            this.getWeaponSave().s = name0;
         }
      }
      
      public function coverFashionSkinB() : Boolean
      {
         return this.getWeaponSave().cf;
      }
      
      public function setCoverFashionSkinB(bb0:Boolean) : void
      {
         this.getWeaponSave().cf = bb0;
      }
   }
}

