package com.sounto.net
{
   import com.sounto.utils.StringMethod;
   
   public class EmbedMethod
   {
      
      private static const swfEmbedText:String = "[Embed(source = \"@swfUrl\")]\n\t\tvar @nameClass:Class;\n\t\tvar @name:Sprite = new @nameClass();";
      
      private static const uiArr:Array = ["VipUI","VehicleUI","UnionUI","TopUI","TaskUI","SkillUI","ShopUI","PostUI","PetUI","PeakUI","PartsUI","OutfitUI","LoveUI","LotteryUI","LoginUI","HouseUI","HelperUI","HeadUI","GuideBox","GiftUI","GameWorldUI","ForgingUI","CityUI","CityDecora","BlackMarketUI","AskUI","ArenaUI","AchieveUI"];
      
      private static const gunArr:Array = ["ak","m4","pistol1","pistol2","pistol3","rocket","shotgun1","shotgun2","shotgun3","sniper1","sniper2","sniper3","texture","xm8"];
      
      private static const effectArr:Array = ["bullet","dropEffect","bladeHitEffect","fireEffect","gunFire","lightEffect","sputtering","terroristBoxEffect","textEffect"];
      
      public function EmbedMethod()
      {
         super();
      }
      
      public static function getSwfEmbed(name0:String, swfUrl0:String) : String
      {
         var s0:String = swfEmbedText;
         s0 = s0.replace("@swfUrl",swfUrl0);
         return StringMethod.replaceStr(s0,"@name",name0);
      }
      
      public static function getSwfEmbedArr(nameArr0:Array, path0:String) : String
      {
         var name0:String = null;
         var s0:String = null;
         var all0:String = "";
         for each(name0 in nameArr0)
         {
            s0 = getSwfEmbed(name0,path0 + "/" + name0 + ".swf");
            all0 += s0 + "\n";
         }
         return all0;
      }
      
      public static function getUIEmbed() : String
      {
         return getSwfEmbedArr(uiArr,"UI");
      }
      
      public static function getGunEmbed() : String
      {
         return getSwfEmbedArr(gunArr,"gun");
      }
      
      public static function getEffectEmbed() : String
      {
         return getSwfEmbedArr(effectArr,"effect");
      }
   }
}

