package dataAll.gift.save
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class SummerGiftSave
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var getObj:Object = {};
      
      public var dailyB:Boolean = false;
      
      public function SummerGiftSave()
      {
         super();
         this.num = 0;
      }
      
      public static function getStartTime() : String
      {
         return "2024-7-1";
      }
      
      public static function getOverTime() : String
      {
         return "2024-8-31 23:59:59";
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.getObj = ClassProperty.copyObj(obj0["getObj"]);
      }
      
      public function getGiftArr() : Array
      {
         return Gaming.defineGroup.gift.getArrByFather("summerSign");
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.setDayGetB(false);
         this.dailyB = false;
      }
      
      public function getLastGetB() : Boolean
      {
         var gArr0:Array = this.getGiftArr();
         var g0:GiftAddDefineGroup = gArr0[gArr0.length - 1];
         return this.getGiftGetB(String(g0.mustLevel));
      }
      
      public function getGiftGetB(id0:String) : Boolean
      {
         return this.getObj[id0];
      }
      
      public function setGiftGetB(id0:String, bb0:Boolean) : void
      {
         this.getObj[id0] = bb0;
      }
      
      public function getDayGetB() : Boolean
      {
         return this.getGiftGetB("1");
      }
      
      public function setDayGetB(bb0:Boolean) : void
      {
         return this.setGiftGetB("1",bb0);
      }
      
      public function daily() : void
      {
         if(!this.dailyB)
         {
            this.dailyB = true;
            ++this.num;
         }
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getNowCanGetGiftArr();
         for each(g0 in gArr0)
         {
            gift0.merge(g0);
         }
         return gift0;
      }
      
      public function getFillNum(timeStr0:String, overB0:Boolean) : int
      {
         var day0:int = 0;
         var v0:int = 0;
         var gArr0:Array = this.getGiftArr();
         var g0:GiftAddDefineGroup = gArr0[gArr0.length - 1];
         var max0:int = g0.mustLevel;
         if(this.num > max0)
         {
            return 0;
         }
         day0 = StringDate.compareDateByStr(getStartTime(),timeStr0) + 1;
         if(day0 > max0)
         {
            day0 = max0;
         }
         v0 = day0 - this.num;
         if(!this.dailyB && overB0 == false)
         {
            v0 -= 1;
         }
         return v0;
      }
      
      public function fill(num0:int) : void
      {
         this.num += num0;
      }
      
      public function getGiftBtnTip() : String
      {
         var g0:GiftAddDefineGroup = null;
         var str0:String = "";
         var gArr0:Array = this.getNowCanGetGiftArr();
         if(gArr0.length == 0)
         {
            str0 = "礼包已领取";
         }
         else
         {
            for each(g0 in gArr0)
            {
               str0 += "\n";
               if(g0.cnName == "")
               {
                  str0 += "<blue " + g0.mustLevel + "天礼包/>";
               }
               else
               {
                  str0 += "<blue " + g0.cnName + "礼包/>";
               }
            }
            str0 = "包含：" + str0;
         }
         return str0;
      }
      
      private function getNowCanGetGiftArr() : Array
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var getB0:Boolean = false;
         var canB0:Boolean = false;
         var arr0:Array = [];
         var gArr0:Array = this.getGiftArr();
         var anum0:int = this.num;
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            getB0 = this.getGiftGetB(String(must0));
            canB0 = anum0 >= must0;
            if(must0 <= 1)
            {
               canB0 = this.dailyB;
            }
            if(!getB0 && canB0)
            {
               arr0.push(g0);
            }
         }
         return arr0;
      }
      
      public function getGiftEvent() : void
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var canB0:Boolean = false;
         var gArr0:Array = this.getGiftArr();
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            canB0 = this.num >= must0;
            if(canB0)
            {
               this.setGiftGetB(String(must0),true);
            }
         }
      }
      
      public function getGettedGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var name0:String = null;
         var getB0:Boolean = false;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getGiftArr();
         for each(g0 in gArr0)
         {
            name0 = String(g0.mustLevel);
            getB0 = this.getGiftGetB(name0);
            if(getB0)
            {
               gift0.merge(g0);
            }
         }
         return gift0;
      }
   }
}

