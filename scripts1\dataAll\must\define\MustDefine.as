package dataAll.must.define
{
   import UI.api.shop.IO_ShopObjectFather;
   import UI.api.shop.ShopBuyObject;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.StringMethod;
   import dataAll._player.base.PlayerBaseSave;
   import dataAll._player.base.PlayerMainSave;
   import dataAll.things.define.ThingsDefine;
   
   public class MustDefine implements IO_ShopObjectFather
   {
      
      public static var pro_arr:Array = [];
      
      public static const THINGS:String = "things";
      
      public static const NUM_EQUIP:String = "equip";
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var propId:String = "";
      
      private var _things:String = "";
      
      public var thingsType:String = "things";
      
      public var numMul:Number = 1;
      
      public var tipBoxString:String = "";
      
      public var text:String = "";
      
      public var everB:Boolean = false;
      
      public function MustDefine()
      {
         super();
         this.lv = 1;
         this.coin = 0;
         this.money = 0;
         this.propNum = 1;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get coin() : Number
      {
         return this.CF.getAttribute("coin");
      }
      
      public function set coin(v0:Number) : void
      {
         this.CF.setAttribute("coin",v0);
      }
      
      public function get money() : Number
      {
         return this.CF.getAttribute("money");
      }
      
      public function set money(v0:Number) : void
      {
         this.CF.setAttribute("money",v0);
      }
      
      public function get propNum() : Number
      {
         return this.CF.getAttribute("propNum");
      }
      
      public function set propNum(v0:Number) : void
      {
         this.CF.setAttribute("propNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function clone() : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      public function inDataByStr(str0:String, name0:String, cn0:String, tipBoxString0:String = "", everB0:Boolean = false) : void
      {
         var arr0:Array = null;
         if(str0.indexOf(";") == -1)
         {
            this.text = str0;
         }
         else
         {
            arr0 = str0.split(";");
            this.lv = int(arr0[0]);
            this.coin = int(arr0[1]);
            this.money = int(arr0[2]);
            if(Boolean(arr0[3]))
            {
               this.propId = String(arr0[3]);
            }
         }
         this.name = name0;
         this.cnName = cn0;
         this.tipBoxString = tipBoxString0;
      }
      
      public function getShopObj() : ShopBuyObject
      {
         var obj0:ShopBuyObject = new ShopBuyObject();
         obj0.setFather(this);
         obj0.count = this.propNum;
         obj0.price = this.money / this.propNum;
         obj0.propId = this.propId;
         obj0.tag = "";
         return obj0;
      }
      
      public function inThingsDataByArr(arr0:Array, thingsType0:String = "things") : void
      {
         this.thingsType = thingsType0;
         this._things = Base64.encodeObject(arr0);
      }
      
      public function addThingsDataByArr(arr0:Array) : void
      {
         if(Boolean(arr0))
         {
            arr0 = this.getThingsStrArr().concat(arr0);
            this.inThingsDataByArr(arr0,this.thingsType);
         }
      }
      
      public function getThingsStrArr() : Array
      {
         if(this._things == "")
         {
            return null;
         }
         return Base64.decodeObject(this._things) as Array;
      }
      
      public function getThingsArr() : Array
      {
         var arr2:Array = null;
         var arr0:Array = null;
         var n:* = undefined;
         var str0:String = null;
         var str_arr0:Array = null;
         var name0:String = null;
         var mustNum0:int = 0;
         var d0:MustSimpleThingsDefine = null;
         if(this._things == "")
         {
            return null;
         }
         arr2 = [];
         arr0 = this.getThingsStrArr();
         for(n in arr0)
         {
            str0 = arr0[n];
            str_arr0 = str0.split(";");
            name0 = str_arr0[0];
            mustNum0 = int(str_arr0[1]);
            d0 = new MustSimpleThingsDefine();
            d0.name = name0;
            d0.num = Math.ceil(mustNum0 * this.numMul);
            arr2.push(d0);
         }
         return arr2;
      }
      
      private function getThingsMustTip() : String
      {
         var sar0:Array = null;
         var d0:MustSimpleThingsDefine = null;
         var name0:String = null;
         var td0:ThingsDefine = null;
         var ar0:Array = this.getThingsArr();
         if(Boolean(ar0))
         {
            sar0 = [];
            for each(d0 in ar0)
            {
               name0 = "XX";
               td0 = Gaming.defineGroup.things.getDefine(d0.name);
               if(Boolean(td0))
               {
                  name0 = td0.cnName;
               }
               sar0.push(name0 + "*" + d0.num);
            }
            return StringMethod.concatStringArr(sar0,3);
         }
         return "";
      }
      
      public function getText(nowDataB:Boolean = true, style0:int = 0) : String
      {
         var lv_s0:PlayerBaseSave = nowDataB ? Gaming.PG.SAVE.base : Gaming.PG.save.base;
         var s0:PlayerBaseSave = Gaming.PG.save.base;
         var main0:PlayerMainSave = Gaming.PG.save.main;
         if(this.text != "")
         {
            return ComMethod.color(this.text,"#00FFFF");
         }
         var noEnough0:String = ComMethod.color("（不足）","#FF0000");
         var str0:String = "";
         if(this.lv > 1)
         {
            str0 += "\n" + ComMethod.color(this.getOneTextByStyle("等级",this.lv + "",style0),"#00FF00");
            if(lv_s0.level < this.lv)
            {
               str0 += noEnough0;
            }
         }
         if(this.coin > 0)
         {
            str0 += "\n" + ComMethod.color(this.getOneTextByStyle("银币",this.coin + "",style0),"#FFFF00");
            if(main0.coin < this.coin)
            {
               str0 += noEnough0;
            }
         }
         if(this.money > 0)
         {
            str0 += "\n" + ComMethod.color(this.getOneTextByStyle("黄金",this.money + "",style0),"#00FF00");
            if(Gaming.PG.da.main.money < this.money)
            {
               str0 += noEnough0;
            }
         }
         if(this._things != "")
         {
            str0 += "\n" + ComMethod.color(this.getThingsMustTip(),"#FFFF00");
            if(this.panThings() == false)
            {
               str0 += noEnough0;
            }
         }
         return str0.replace("\n","");
      }
      
      private function getOneTextByStyle(name0:String, v0:String, style0:int) : String
      {
         if(style0 == 0)
         {
            return name0 + "：" + v0;
         }
         if(style0 == 1)
         {
            if(name0 == "等级")
            {
               name0 = "级";
            }
            return v0 + name0;
         }
         return "";
      }
      
      public function getHorText(nowDataB:Boolean = true) : String
      {
         var str0:String = this.getText(nowDataB);
         return TextWay.replaceStr(str0,"\n","   ");
      }
      
      public function getNumBuyText(name0:String, canBuyNum0:int = -1, oneNum0:int = 1) : String
      {
         var str0:String = "";
         str0 += "增加" + ComMethod.color(oneNum0 + "次","#00FFFF") + name0 + "，需要消耗：";
         str0 += "\n" + this.getText();
         if(canBuyNum0 >= 0)
         {
            str0 += ComMethod.color("\n\n今日还可以增加" + ComMethod.colorEnoughNum(canBuyNum0) + "次" + name0 + "。","#999999");
         }
         return str0;
      }
      
      public function panCondition(nowDataB:Boolean = true) : Boolean
      {
         var lv_s0:PlayerBaseSave = nowDataB ? Gaming.PG.SAVE.base : Gaming.PG.save.base;
         var s0:PlayerBaseSave = Gaming.PG.save.base;
         var main0:PlayerMainSave = Gaming.PG.save.main;
         return lv_s0.level >= this.lv && main0.coin >= this.coin && Gaming.PG.da.main.money >= this.money && this.panThings();
      }
      
      public function panConditionStr(nowDataB:Boolean = true) : String
      {
         var lv_s0:PlayerBaseSave = nowDataB ? Gaming.PG.SAVE.base : Gaming.PG.save.base;
         var s0:PlayerBaseSave = Gaming.PG.save.base;
         var main0:PlayerMainSave = Gaming.PG.save.main;
         if(lv_s0.level < this.lv)
         {
            return "人物等级不足" + this.lv + "级。";
         }
         if(main0.coin < this.coin)
         {
            return "银币不足" + this.coin + "个。";
         }
         if(Gaming.PG.da.main.money < this.money)
         {
            return "黄金不足" + this.coin + "个。";
         }
         if(this.panThings() == false)
         {
            return "所需物品不足。";
         }
         return "";
      }
      
      private function panThings() : Boolean
      {
         var d0:MustSimpleThingsDefine = null;
         var now0:Number = NaN;
         var ar0:Array = this.getThingsArr();
         if(Boolean(ar0))
         {
            for each(d0 in ar0)
            {
               now0 = Gaming.PG.da.thingsBag.getThingsNum(d0.name);
               if(now0 < d0.num)
               {
                  return false;
               }
            }
         }
         return true;
      }
   }
}

