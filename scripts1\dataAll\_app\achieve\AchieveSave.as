package dataAll._app.achieve
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.achieve.define.AchieveDefine;
   
   public class AchieveSave
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var state:String = "no";
      
      public var info:String = "";
      
      public var infoValue:Number = 0;
      
      public var gB:Boolean = false;
      
      public var wearB:Boolean = false;
      
      public var wearIndex:Number = 0;
      
      public function AchieveSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inData_byDefine(d0:AchieveDefine) : void
      {
         this.name = d0.name;
      }
   }
}

