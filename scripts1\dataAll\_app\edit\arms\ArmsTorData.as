package dataAll._app.edit.arms
{
   import com.sounto.utils.NumberMethod;
   import dataAll._app.edit.TorData;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProGather;
   import dataAll._app.edit.tor.IO_TorEditDefine;
   import dataAll._app.edit.tor.TorEditAgent;
   import dataAll._base.OneSave;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.bullet.BulletDefine;
   import dataAll.image.ImageUrlDefine;
   
   public class ArmsTorData extends TorData
   {
      
      protected var fatherG:ArmsTorDataGroup = null;
      
      protected var rangeDef:ArmsRangeDefine;
      
      protected var def:ArmsDefine;
      
      protected var baseDef:ArmsDefine;
      
      private var editAgent:TorEditAgent = null;
      
      private var tempArms:ArmsData = null;
      
      public function ArmsTorData()
      {
         super();
      }
      
      public function setFatherG(g0:ArmsTorDataGroup) : void
      {
         this.fatherG = g0;
      }
      
      override public function inData_bySave(s0:OneSave) : void
      {
         var range0:ArmsRangeDefine = null;
         super.inData_bySave(s0);
         var baseRange0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(torSave.baseLabel);
         if(Boolean(baseRange0))
         {
            this.baseDef = baseRange0.def;
            range0 = baseRange0.clone();
            range0.clearRange();
            this.rangeDef = range0;
            this.def = range0.def;
            this.def.inData_bySaveObj(torSave.obj);
            Gaming.defineGroup.bullet.addOutRange(range0);
         }
      }
      
      public function getRangeDef() : ArmsRangeDefine
      {
         return this.rangeDef;
      }
      
      public function getArmsDef() : ArmsDefine
      {
         return this.def;
      }
      
      public function getBaseDef() : ArmsDefine
      {
         return this.baseDef;
      }
      
      public function haveDataB() : Boolean
      {
         if(Boolean(this.def) && Boolean(this.baseDef) && Boolean(torSave))
         {
            return true;
         }
         return false;
      }
      
      private function getPnUidx() : String
      {
         var pn0:String = torSave.pn;
         if(pn0 != "")
         {
            return ArmsEditMethod.getUidxByArmsName(torSave.name);
         }
         return "";
      }
      
      public function isOtherUidxB() : Boolean
      {
         var u1:String = null;
         var pn0:String = torSave.pn;
         if(pn0 == "")
         {
            return false;
         }
         u1 = this.getPnUidx();
         if(u1 != Gaming.PG.getUidx())
         {
            return true;
         }
         return false;
      }
      
      public function getCnName() : String
      {
         return this.def.cnName;
      }
      
      public function getIconUrl() : String
      {
         return this.def.getBookIconUrl();
      }
      
      override public function getNumberMin(proD0:EditProDefine) : Number
      {
         var v0:Number = NaN;
         var pro0:String = proD0.name;
         if(pro0 == "attackGap")
         {
            return ArmsType.getMaxAttackGap(this.def.armsType,this.def);
         }
         return proD0.min;
      }
      
      override public function getTorEditAgent() : TorEditAgent
      {
         var a0:TorEditAgent = new TorEditAgent();
         a0.da = this;
         a0.initByData(this,EditProGather.arms,this.fatherG.hideHighB,this.def.hitType);
         if(this.isOtherUidxB())
         {
            a0.setLockAllB(true);
         }
         else if(this.def.hitType == BulletDefine.LONG_LINE && this.def.bulletImg.isLongLine() == false)
         {
            a0.addNoChangeDefPos("lineD");
         }
         return a0;
      }
      
      override public function getDefValue(proD0:EditProDefine) : *
      {
         return proD0.getObjValue(this.def);
      }
      
      override public function getBaseValue(proD0:EditProDefine) : *
      {
         return proD0.getObjValue(this.baseDef);
      }
      
      override public function getUICn(proD0:EditProDefine) : String
      {
         var s0:String = proD0.cnName;
         if(this.def.hitType == BulletDefine.LONG_LINE && proD0.name == "bulletWidth")
         {
            s0 = "射程";
         }
         if(proD0.isNormalGra() == false)
         {
            s0 = "·" + s0;
         }
         return s0;
      }
      
      public function getIconSmallLabel(mainB0:Boolean) : String
      {
         if(mainB0)
         {
            return "main";
         }
         if(havePn())
         {
            return "otherPn";
         }
         return "";
      }
      
      override public function getProTip(d0:IO_TorEditDefine) : String
      {
         var tip0:String = "";
         var name0:String = d0.getName();
         if(name0 == "dpsMul")
         {
            tip0 = "实际系数：" + NumberMethod.toFixed(this.def.getDpsMul(),2);
         }
         else if(name0 == "attackGap")
         {
            tip0 = "射击速度：" + NumberMethod.toFixed(1 / this.def.attackGap,2) + "发/秒";
         }
         else if(name0 == "shootAngle")
         {
            tip0 = "实际散射角度：" + this.def.shootAngle * 2 + "度";
         }
         else
         {
            tip0 = super.getProTip(d0);
         }
         return tip0;
      }
      
      public function clearTempArms() : void
      {
         this.tempArms = null;
      }
      
      public function getTempArms(addIfNo:Boolean) : ArmsData
      {
         if(this.tempArms == null && addIfNo)
         {
            this.tempArms = ArmsEditMethod.getTempArmsData(this.getArmsDef());
         }
         return this.tempArms;
      }
      
      override public function changeValue(proD0:EditProDefine, v0:*) : Boolean
      {
         var bb0:Boolean = false;
         if(this.panDefInOutB())
         {
            bb0 = super.changeValue(proD0,v0);
            if(bb0)
            {
               proD0.setObjValue(this.def,v0);
            }
         }
         return bb0;
      }
      
      override public function changeImageUrlDefine(proD0:EditProDefine, d0:ImageUrlDefine) : Boolean
      {
         var defD0:ImageUrlDefine = null;
         var bb0:Boolean = false;
         if(this.panDefInOutB())
         {
            bb0 = super.changeImageUrlDefine(proD0,d0);
            if(bb0)
            {
               defD0 = this.getDefValue(proD0) as ImageUrlDefine;
               defD0.inData_byObj(d0);
            }
         }
         return bb0;
      }
      
      private function panDefInOutB() : Boolean
      {
         if(Boolean(Gaming.defineGroup.bullet.getOutDef(this.def.name)))
         {
            return true;
         }
         return false;
      }
   }
}

