package com.sounto.math
{
   import flash.geom.Point;
   
   public class GeoMethod
   {
      
      private static var p2:TwoPoint = new TwoPoint();
      
      private static var p3:TwoPoint = new TwoPoint();
      
      private static var p4:TwoPoint = new TwoPoint();
      
      public function GeoMethod()
      {
         super();
      }
      
      public static function getAll_LineCircle(x1:Number, y1:Number, x2:Number, y2:Number, r2:Number) : TwoPoint
      {
         var ra1:Number = NaN;
         var ra0:Number = Math.atan2(y2 - y1,x2 - x1);
         var len:Number = Maths.Long(x1 - x2,y1 - y2);
         if(len < 0.0001 || r2 >= len)
         {
            p4.pNum = 0;
            return p4;
         }
         p4.pNum = 1;
         ra1 = ra0 - Math.asin(r2 / len);
         p4.ra1 = ra1;
         p4.x1 = x2 + r2 * Math.cos(ra1 - Math.PI / 2);
         p4.y1 = y2 + r2 * Math.sin(ra1 - Math.PI / 2);
         return p4;
      }
      
      public static function getIP_LineCircle2(x1:Number, y1:Number, ra:Number, x2:Number, y2:Number, r2:Number) : TwoPoint
      {
         var pp4:TwoPoint = getIP_LineCircle(x1,y1,ra,x2,y2,r2);
         var ra0:Number = 0;
         var cra0:Number = 0;
         if(pp4.pNum == 1)
         {
            ra0 = Math.atan2(pp4.y1 - y1,pp4.x1 - x1);
            cra0 = Maths.J_J(ra,ra0);
            if(cra0 > 0.001)
            {
               pp4.pNum = 0;
            }
         }
         else if(pp4.pNum == 2)
         {
            pp4.pNum = 1;
            ra0 = Math.atan2(pp4.y1 - y1,pp4.x1 - x1);
            cra0 = Maths.J_J(ra,ra0);
            if(cra0 > 0.001)
            {
               pp4.x1 = pp4.x2;
               pp4.y1 = pp4.y2;
            }
         }
         return pp4;
      }
      
      public static function getIP_LineCircle(x1:Number, y1:Number, ra:Number, x2:Number, y2:Number, r2:Number) : TwoPoint
      {
         var a0:Number = NaN;
         var b0:Number = NaN;
         var a:Number = NaN;
         var b:Number = NaN;
         var c:Number = NaN;
         var _num1:Number = NaN;
         var c_ra0:Number = ra % Math.PI;
         var c_ra1:Number = ra % (Math.PI / 2);
         var c_o:Number = 0;
         p3.pNum = 0;
         var c_l3:Number = 0;
         if(Math.abs(c_ra0) < 0.00001 || Math.PI - c_ra0 < 0.00001)
         {
            c_o = Math.abs(y1 - y2);
            if(c_o > r2)
            {
               return p3;
            }
            if(r2 - c_o < 0.00001)
            {
               p3.pNum = 1;
               p3.x1 = x2;
               p3.y1 = y1;
               return p3;
            }
            c_l3 = Math.sqrt(r2 * r2 - c_o * c_o);
            p3.pNum = 2;
            p3.y1 = y1;
            p3.y2 = y1;
            p2.x1 = x2 - c_l3;
            p2.x2 = x2 + c_l3;
            return p3;
         }
         if(Math.abs(c_ra1) < 0.00001 || Math.PI / 2 - c_ra1 < 0.00001)
         {
            c_o = Math.abs(x1 - x2);
            if(c_o > r2)
            {
               return p3;
            }
            if(r2 - c_o < 0.00001)
            {
               p3.pNum = 1;
               p3.x1 = x1;
               p3.y1 = y2;
               return p3;
            }
            c_l3 = Math.sqrt(r2 * r2 - c_o * c_o);
            p3.pNum = 2;
            p3.x1 = x1;
            p3.x2 = x1;
            p2.y1 = y2 - c_l3;
            p2.y2 = y2 + c_l3;
            return p3;
         }
         a0 = Math.tan(ra);
         b0 = y1 - x1 * Math.tan(ra);
         a = a0 * a0 + 1;
         b = 2 * (a0 * (b0 - y2) - x2);
         c = x2 * x2 + (b0 - y2) * (b0 - y2) - r2 * r2;
         _num1 = b * b - 4 * a * c;
         if(_num1 < 0)
         {
            return p3;
         }
         if(_num1 < 1e-8)
         {
            p3.pNum = 1;
            p3.x1 = -b / 2 / a;
            p3.y1 = a0 * p3.x1 + b0;
            return p3;
         }
         p3.pNum = 2;
         p3.x1 = (-b - Math.sqrt(_num1)) / 2 / a;
         p3.y1 = a0 * p3.x1 + b0;
         p3.x2 = (-b + Math.sqrt(_num1)) / 2 / a;
         p3.y2 = a0 * p3.x2 + b0;
         return p3;
      }
      
      public static function getIP_2Circle(x1:Number, y1:Number, r1:Number, x2:Number, y2:Number, r2:Number) : TwoPoint
      {
         var X32:Number = NaN;
         var Y32:Number = NaN;
         var a:Number = NaN;
         var b:Number = NaN;
         var c:Number = NaN;
         var _num1:Number = NaN;
         var _num2:Number = NaN;
         var m:Number = NaN;
         var n:Number = NaN;
         var _a:Number = NaN;
         var _b:Number = NaN;
         var _c:Number = NaN;
         var _num33:Number = NaN;
         var _num3:Number = NaN;
         var X3:Number = 0;
         var Y3:Number = 0;
         var r3:Number = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
         if(r1 + r2 > r3 && Math.abs(r1 - r2) < r3)
         {
            X32 = 0;
            Y32 = 0;
            a = -2 * (x1 - x2);
            b = -2 * (y1 - y2);
            c = x1 * x1 - x2 * x2 + y1 * y1 - y2 * y2 + r2 * r2 - r1 * r1;
            p2.pNum = 0;
            if(x1 == x2 && y1 == y2)
            {
               return p2;
            }
            if(Math.abs(a) < 0.000001)
            {
               Y3 = -c / b;
               _num1 = r1 * r1 - (y1 - Y3) * (y1 - Y3);
               if(_num1 < 0)
               {
                  return p2;
               }
               Y32 = Y3;
               X3 = x1 - Math.sqrt(_num1);
               X32 = x1 + Math.sqrt(_num1);
            }
            else if(Math.abs(b) < 0.000001)
            {
               X3 = -c / a;
               _num2 = r1 * r1 - (x1 - X3) * (x1 - X3);
               if(_num2 < 0)
               {
                  return p2;
               }
               X32 = X3;
               Y3 = y1 - Math.sqrt(_num2);
               Y32 = y1 + Math.sqrt(_num2);
            }
            else
            {
               m = -b / a;
               n = -c / a;
               _a = m * m + 1;
               _b = 2 * m * n - 2 * y1 - 2 * x1 * m;
               _c = x1 * x1 - 2 * x1 * n + n * n + y1 * y1 - r1 * r1;
               _num33 = _b * _b - 4 * _a * _c;
               if(_num33 < 0)
               {
                  return p2;
               }
               _num3 = Math.sqrt(_num33);
               Y3 = (-_b + _num3) / 2 / _a;
               X3 = m * Y3 + n;
               Y32 = (-_b - _num3) / 2 / _a;
               X32 = m * Y32 + n;
            }
            p2.pNum = 2;
            p2.x1 = X3;
            p2.y1 = Y3;
            p2.x2 = X32;
            p2.y2 = Y32;
            return p2;
         }
         if(r1 + r2 == r3 || Math.abs(r1 - r2) == r3)
         {
            X3 = (x2 - x1) * r1 / r3 + x1;
            Y3 = (y2 - y1) * r1 / r3 + y1;
            p2.pNum = 1;
            p2.x1 = X3;
            p2.y1 = Y3;
            return p2;
         }
         p2.pNum = 0;
         return p2;
      }
      
      public static function getTruePointInCon(ix0:Number, iy0:Number, mx0:Number, my0:Number, mra0:Number, scaleX0:int = 1) : Point
      {
         var l0:Number = Math.sqrt(ix0 * ix0 + iy0 * iy0);
         var ra0:Number = Math.atan2(iy0,ix0) + mra0;
         var x0:Number = l0 * Math.cos(ra0);
         x0 *= scaleX0;
         var y0:Number = l0 * Math.sin(ra0);
         return new Point(x0 + mx0,y0 + my0);
      }
   }
}

