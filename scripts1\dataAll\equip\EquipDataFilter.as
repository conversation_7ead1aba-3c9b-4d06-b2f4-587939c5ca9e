package dataAll.equip
{
   public class EquipDataFilter
   {
      
      public function EquipDataFilter()
      {
         super();
      }
      
      public static function filter(dataArr0:Array, filter0:String) : Array
      {
         var da0:EquipData = null;
         var bb0:Boolean = false;
         var arr2:Array = [];
         for each(da0 in dataArr0)
         {
            bb0 = false;
            if(filter0 == "normal")
            {
               bb0 = da0.canRemakeB();
            }
            else
            {
               bb0 = da0.save.partType == filter0;
            }
            if(bb0)
            {
               arr2.push(da0);
            }
         }
         return arr2;
      }
   }
}

