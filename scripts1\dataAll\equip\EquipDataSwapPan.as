package dataAll.equip
{
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.ui.tip.CheckData;
   
   public class EquipDataSwapPan
   {
      
      public function EquipDataSwapPan()
      {
         super();
      }
      
      public static function equipGripActivedPan(pd0:NormalPlayerData, partType0:String) : Boolean
      {
         if(Gaming.LG.isMemoryTaskB())
         {
            return false;
         }
         var check0:CheckData = canInWearType(pd0,partType0,null);
         return check0.bb;
      }
      
      public static function canLoadPan(pd0:NormalPlayerData, da0:EquipData) : CheckData
      {
         return canInWearType(pd0,da0.save.partType,da0);
      }
      
      public static function canInWearType(pd0:NormalPlayerData, partType0:String, comeinData0:EquipData) : CheckData
      {
         var gamingB0:Boolean = Gaming.LG.isGaming();
         var taskFather0:String = Gaming.LG.nowLevel.dat.getNowTaskFather();
         return canSwapPan(pd0,partType0,gamingB0,taskFather0,comeinData0);
      }
      
      private static function canSwapPan(pd0:NormalPlayerData, partType0:String, gamingB0:Boolean, taskFather0:String, comeinData0:EquipData) : CheckData
      {
         var d0:EquipDefine = null;
         var sex0:String = null;
         var check0:CheckData = new CheckData();
         if(gamingB0)
         {
            if(Gaming.LG.isMemoryTaskB())
            {
               check0.bb = false;
            }
            if(taskFather0 == "extra")
            {
               check0.bb = false;
            }
            else
            {
               if(EquipType.noSwapInLevelArr.indexOf(partType0) >= 0)
               {
                  check0.bb = false;
               }
               if(comeinData0 is EquipData)
               {
                  if(comeinData0.save.isHaveSkillAddB())
                  {
                     check0.bb = false;
                     check0.info = "游戏进行中无法更换包含“人物技能附加属性”的装备。";
                  }
               }
            }
         }
         if(comeinData0 is EquipData && check0.bb)
         {
            if(partType0 == EquipType.FASHION)
            {
               d0 = comeinData0.save.getDefine();
               sex0 = pd0.heroData.def.sex;
               if(d0.getApplySex() != sex0)
               {
                  check0.bb = false;
               }
               if(d0.panRole(pd0.getRoleName()) == false)
               {
                  check0.bb = false;
               }
            }
         }
         return check0;
      }
   }
}

