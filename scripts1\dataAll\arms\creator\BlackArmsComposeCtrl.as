package dataAll.arms.creator
{
   import UI.bag.ItemsGripBtnListCtrl;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import gameAll.arms.GameArmsCtrl;
   
   public class BlackArmsComposeCtrl
   {
      
      private static var tempTip:String = "";
      
      public function BlackArmsComposeCtrl()
      {
         super();
      }
      
      public static function getThingsTip(d0:ThingsDefine, pd0:PlayerData) : String
      {
         var num0:int = 0;
         var must0:int = 0;
         var armsMax0:int = 0;
         var lv0:int = 0;
         var str0:String = null;
         var armsS0:ArmsSave = null;
         var armsDa0:ArmsData = null;
         var getStr0:String = null;
         var armsD0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(d0.name);
         if(armsD0 is ArmsRangeDefine)
         {
            num0 = pd0.thingsBag.getThingsNum(d0.name);
            must0 = getMustNum(armsD0);
            armsMax0 = armsD0.def.composeMax;
            lv0 = getLv(armsD0);
            str0 = "可合成武器|<yellow " + lv0 + "级" + armsD0.def.cnName + "/>";
            if(armsMax0 > 0)
            {
               str0 += "\n武器数量上限|" + "<yellow " + armsMax0 + "把/>";
            }
            str0 += "\n所需" + d0.cnName + "|" + ComMethod.colorMustNum(num0,must0) + "个";
            armsS0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,armsD0.def);
            armsDa0 = new ArmsData();
            armsDa0.inData_bySave(armsS0,pd0);
            str0 += "\n" + armsDa0.getGatherTip(null,false,false);
            getStr0 = armsD0.def.getGetMethodStr();
            if(getStr0 != "" || d0.description != "")
            {
               str0 += "\n<i1>|<blue <b>获得方式：</b>/>";
               str0 += getStr0;
            }
            if(d0.description != "")
            {
               str0 += "\n" + d0.description;
            }
            return str0;
         }
         return d0.description;
      }
      
      private static function getMustNum(d0:ArmsRangeDefine) : int
      {
         return d0.def.chipNum;
      }
      
      private static function getLv(d0:ArmsRangeDefine) : int
      {
         return d0.def.composeLv;
      }
      
      public static function compose(da0:ThingsData) : void
      {
         var d0:ThingsDefine = null;
         var pd0:PlayerData = null;
         var armsD0:ArmsRangeDefine = null;
         var armsNum0:int = 0;
         var armsMaxNum0:int = 0;
         var num0:int = 0;
         var must0:int = 0;
         var lv0:int = 0;
         var armsS0:ArmsSave = null;
         var loginB0:Boolean = Gaming.PG.loginData.isLoginByJS();
         if(!loginB0)
         {
            Gaming.uiGroup.alertBox.showError("您的账号已经退出登录，无法进行此操作。");
         }
         else
         {
            d0 = da0.save.getDefine();
            pd0 = da0.playerData;
            armsD0 = Gaming.defineGroup.bullet.getArmsRangeDefine(d0.name);
            if(armsD0 is ArmsRangeDefine)
            {
               armsNum0 = int(pd0.findItemsDataByName(armsD0.def.name,ArmsData).length);
               armsMaxNum0 = armsD0.def.composeMax;
               if(armsMaxNum0 > 0 && armsNum0 >= armsMaxNum0)
               {
                  Gaming.uiGroup.alertBox.showError("您最多只能拥有" + armsMaxNum0 + "把该武器！");
               }
               else
               {
                  num0 = da0.save.nowNum;
                  must0 = getMustNum(armsD0);
                  if(num0 < must0)
                  {
                     Gaming.uiGroup.alertBox.showError("碎片个数不足" + must0 + "个，无法合成" + armsD0.def.cnName + "。");
                  }
                  else if(pd0.armsBag.getSpaceSiteNum() <= 0)
                  {
                     Gaming.uiGroup.alertBox.showError("背包空位不足，无法合成武器。");
                  }
                  else
                  {
                     pd0.thingsBag.useThings(d0.name,must0);
                     lv0 = getLv(armsD0);
                     armsS0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,armsD0.def);
                     armsS0.lockB = true;
                     pd0.armsBag.addSave(armsS0);
                     GameArmsCtrl.addArmsSaveResoure(armsS0);
                     ItemsGripBtnListCtrl.fleshAllBy(pd0.thingsBag);
                     tempTip = "成功合成" + armsD0.def.cnName + "！";
                     affterCompose();
                  }
               }
            }
         }
      }
      
      private static function affterCompose(v:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess(tempTip);
      }
   }
}

