package dataAll.gift.zhongQiu
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class ZhongQiuSave
   {
      
      public static var pro_arr:Array = null;
      
      public static const START_TIME:String = "2024-9-13";
      
      public static const END_TIME:String = "2024-9-22";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var loop:Number = 0;
      
      private var weekendB:Boolean = false;
      
      public function ZhongQiuSave()
      {
         super();
      }
      
      public function get useNum() : Number
      {
         return this.CF.getAttribute("useNum");
      }
      
      public function set useNum(v0:Number) : void
      {
         this.CF.setAttribute("useNum",v0);
      }
      
      public function get cakeNum() : Number
      {
         return this.CF.getAttribute("cakeNum");
      }
      
      public function set cakeNum(v0:Number) : void
      {
         this.CF.setAttribute("cakeNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.useNum = 0;
      }
      
      public function setNowReadTime(str0:String) : void
      {
         var da0:StringDate = new StringDate(str0);
         if(da0.isWeekendB() || da0.date == 17)
         {
            this.weekendB = true;
         }
         else
         {
            this.weekendB = false;
         }
      }
      
      public function getAllNum(nowActive0:int) : int
      {
         var add0:int = nowActive0 / 15;
         if(add0 > 10)
         {
            add0 = 10;
         }
         return 5 + add0;
      }
      
      public function getCanNum(nowActive0:int) : int
      {
         return this.getAllNum(nowActive0) - this.useNum;
      }
      
      public function getDiceObj() : Object
      {
         var breakB0:Boolean = false;
         var giftNum0:int = 0;
         var parr0:Array = null;
         var d0:DicePackDefine = null;
         var cakeSurplus0:int = DicePackDefine.getCakeMax() - this.cakeNum;
         for(var i:int = 0; i < 100; i++)
         {
            parr0 = DicePackDefine.getRan6Arr();
            d0 = DicePackDefine.findSameDefine(parr0);
            ++this.loop;
            breakB0 = true;
            if(d0.cakeB)
            {
               giftNum0 = d0.giftNum;
               if(giftNum0 >= 3)
               {
                  if(cakeSurplus0 < giftNum0)
                  {
                     breakB0 = false;
                  }
               }
            }
            if(breakB0)
            {
               break;
            }
         }
         var obj0:Object = {};
         obj0["diceArr"] = parr0;
         obj0["def"] = d0;
         ++this.useNum;
         if(d0.cakeB)
         {
            this.cakeNum += d0.giftNum;
         }
         return obj0;
      }
   }
}

