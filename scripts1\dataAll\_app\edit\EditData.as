package dataAll._app.edit
{
   import dataAll._app.edit._def.EditProDefine;
   import dataAll.ui.text.LinkTextAgent;
   
   public class EditData
   {
      
      protected var outB:Boolean = false;
      
      protected var save:EditSave = null;
      
      protected var gather:String = "";
      
      public function EditData()
      {
         super();
      }
      
      public function inData_bySave(s0:EditSave, outB0:Boolean) : void
      {
         this.save = s0;
         this.outB = outB0;
      }
      
      public function getEditSave() : EditSave
      {
         return this.save;
      }
      
      public function get id() : String
      {
         return this.save.id;
      }
      
      public function get name() : String
      {
         return this.save.getName();
      }
      
      public function set name(v0:String) : void
      {
         this.save.setName(v0);
      }
      
      public function getDefine(name0:String) : EditProDefine
      {
         return Gaming.defineGroup.editPro.getNormalDefineByGather(this.gather,name0) as EditProDefine;
      }
      
      protected function proArrInText(proArr0:Array, a0:LinkTextAgent) : void
      {
         var pro0:String = null;
         var d0:EditProDefine = null;
         var v0:* = undefined;
         var cn0:String = null;
         for each(pro0 in proArr0)
         {
            d0 = this.getDefine(pro0);
            v0 = this.getUIValueByDef(d0);
            cn0 = d0.getUIText(v0);
            a0.addData(cn0,d0.getLinkLabel(),d0);
         }
      }
      
      protected function getUIValueByDef(proD0:EditProDefine) : *
      {
         return this.save.getValueByDef(proD0);
      }
      
      public function getValueByDefName(pro0:String) : *
      {
         var d0:EditProDefine = this.getDefine(pro0);
         if(Boolean(d0))
         {
            return this.save.getValueByDef(d0);
         }
         return null;
      }
      
      protected function setValue(pro0:String, v0:*) : void
      {
         this.save.setValue(pro0,v0);
      }
      
      public function swapBoolean(name0:String) : Boolean
      {
         var bb0:Boolean = this.getValueByDefName(name0);
         bb0 = !bb0;
         this.save.setValue(name0,bb0);
         return bb0;
      }
   }
}

