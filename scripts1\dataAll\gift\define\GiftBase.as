package dataAll.gift.define
{
   import dataAll._app.goods.define.PriceType;
   
   public class GiftBase
   {
      
      public static const coin:String = "coin";
      
      public static const exp:String = "exp";
      
      public static const anniCoin:String = "anniCoin";
      
      public static const tenCoin:String = PriceType.TENCOIN;
      
      public static const partsCoin:String = PriceType.PARTSCOIN;
      
      public static const wilderKey:String = "wilderKey";
      
      public static const pumpkin:String = "pumpkin";
      
      public static const daySweeping:String = "daySweeping";
      
      public static const exploit:String = "exploit";
      
      public static const craftExp:String = "craftExp";
      
      public static const bossCardNum:String = "bossCardNum";
      
      public static const gripNoNumArr:Array = [exp];
      
      public static const coinCn:String = "银币";
      
      public static const expCn:String = "经验";
      
      public static const craftExpCn:String = "飞船经验";
      
      public static const anniCoinCn:String = "纪念币";
      
      public static const tenCoinCn:String = PriceType.tenCoinCnName;
      
      public static const partsCoinCn:String = PriceType.partsCoinCnName;
      
      public static const wilderKeyCn:String = "秘境钥匙";
      
      public static const pumpkinCn:String = PriceType.pumpkinCnName;
      
      public static const daySweepingCn:String = "每日扫荡次数";
      
      public static const bossCardNumCn:String = "抽魂卡次数";
      
      public static const arr:Array = [coin,exp,anniCoin,wilderKey,pumpkin,daySweeping,craftExp,bossCardNum,tenCoin,partsCoin];
      
      public static const cnArr:Array = [];
      
      public function GiftBase()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         var name0:* = undefined;
         var cn0:String = null;
         cnArr.length = 0;
         for each(name0 in arr)
         {
            cn0 = getCn(name0);
            cnArr.push(cn0);
         }
      }
      
      public static function getCn(name0:String) : String
      {
         return GiftBase[name0 + "Cn"];
      }
      
      public static function getIconUrl(name0:String) : String
      {
         if(name0 == pumpkin)
         {
            return "ThingsIcon/" + PriceType.pumpkinIconMcLabel;
         }
         return "ThingsIcon/" + name0;
      }
      
      public static function getGatherTip(name0:String, num0:Number) : String
      {
         var cn0:String = getCn(name0);
         var numStr0:String = num0 + "";
         if(num0 == 0)
         {
            numStr0 = "若干";
         }
         var s0:String = "添加<yellow " + numStr0 + "个/>" + cn0 + "。";
         if(name0 == anniCoin)
         {
            s0 += " \n\n" + cn0 + "可在商店里兑换非常丰富的物品。";
         }
         else if(name0 == tenCoin)
         {
            s0 += " \n\n" + cn0 + "可在商店里兑换稀有物品。";
         }
         else if(name0 == partsCoin)
         {
            s0 += " \n\n" + cn0 + "可在商店里兑换稀有零件。";
         }
         else if(name0 == wilderKey)
         {
            s0 += " \n\n" + cn0 + "可兑换任意一个秘境的挑战次数。";
         }
         else if(name0 == craftExp)
         {
            s0 += " \n\n经验到达一定值，飞船就会升级，同时将获得技能点数（用于提升飞船技能等级）。";
         }
         else if(name0 == bossCardNum)
         {
         }
         return s0;
      }
      
      public static function getNameByCn(cn0:String) : String
      {
         var f0:int = int(cnArr.indexOf(cn0));
         if(f0 >= 0)
         {
            return arr[f0];
         }
         return "";
      }
   }
}

