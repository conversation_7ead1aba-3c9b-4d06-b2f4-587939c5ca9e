package com.sounto.hit.spiderWeb
{
   import com.common.data.Base64;
   import flash.display.Graphics;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.geom.Rectangle;
   import flash.utils.ByteArray;
   import flash.utils.getTimer;
   import gameAll.process.processFun.SpiderWebProcessFun;
   
   public class SpiderWeb
   {
      
      public var arr:Array = [];
      
      public var obj:Object = new Object();
      
      private var open:Array = [];
      
      private var close:Array = [];
      
      public var nn_obj:Object;
      
      private var nn_obj_haveDataB:Boolean = false;
      
      public function SpiderWeb()
      {
         super();
      }
      
      public function setFunProcessFun(fun0:SpiderWebProcessFun) : void
      {
         fun0.spiderWeb = this;
         fun0.setLoopNum(this.arr.length);
      }
      
      public function havePathDataB() : Boolean
      {
         return this.nn_obj_haveDataB;
      }
      
      public function inData_byStr(str0:String) : void
      {
         var byte0:ByteArray = null;
         var arr0:Array = null;
         var arr_len0:int = 0;
         var n:int = 0;
         var n0:SpiderWebNode = null;
         this.clear();
         if(str0 != "")
         {
            byte0 = Base64.decode(str0);
            byte0.position = 0;
            arr0 = byte0.readObject();
            arr_len0 = int(arr0.length);
            for(n = 0; n < arr_len0; n++)
            {
               n0 = new SpiderWebNode();
               n0.inData_byObj(arr0[n]);
               this.arr.push(n0);
               this.obj[n0.id] = n0;
            }
         }
      }
      
      public function inData_bySprite(sp0:Sprite) : *
      {
         var mc0:* = undefined;
         var name0:String = null;
         var n0:SpiderWebNode = null;
         this.clear();
         var num0:int = sp0.numChildren;
         var n_obj2:Object = {};
         var shapeArr0:Array = [];
         var n_index0:int = 0;
         for(var i:int = 0; i < num0; i++)
         {
            mc0 = sp0.getChildAt(i);
            if(mc0 is MovieClip)
            {
               name0 = mc0.name;
               if(name0.indexOf("n") == 0)
               {
                  n0 = new SpiderWebNode();
                  n0.x = mc0.x;
                  n0.y = mc0.y;
                  n0.id = "n" + n_index0;
                  n_index0++;
                  if(mc0.scaleX > 1)
                  {
                     n0.type = "jump";
                     n0.jumpRa = mc0.rotation / 180 * Math.PI;
                  }
                  else if(mc0.scaleX < 1)
                  {
                     n0.type = "squat";
                     n0.jumpRa = mc0.rotation / 180 * Math.PI;
                  }
                  this.arr.push(n0);
                  this.obj[n0.id] = n0;
                  n_obj2["n" + int(n0.x) + "_" + int(n0.y)] = n0;
               }
            }
            else if(mc0 is Shape)
            {
               shapeArr0.push(mc0);
            }
         }
         this.countLine(n_obj2,shapeArr0);
      }
      
      private function countLine(obj0:Object, shapeArr0:Array) : *
      {
         var n:* = undefined;
         var _sp0:Shape = null;
         var rect0:Rectangle = null;
         var sn0:SpiderWebNode = null;
         var sn1:SpiderWebNode = null;
         var scale0:int = 0;
         var nn:int = 0;
         var x0:int = 0;
         var y0:int = 0;
         var cx:int = 0;
         var cy:int = 0;
         var sname0:String = null;
         var srange:int = 5;
         var srange2:int = int(srange / 2);
         for(n in shapeArr0)
         {
            _sp0 = shapeArr0[n];
            rect0 = _sp0.getBounds(_sp0.parent);
            sn0 = null;
            sn1 = null;
            for(scale0 = 0; scale0 < 2; scale0++)
            {
               for(nn = 0; nn < 2; nn++)
               {
                  x0 = 0;
                  y0 = 0;
                  if(nn == 0)
                  {
                     if(scale0 == 0)
                     {
                        x0 = rect0.x;
                        y0 = rect0.y;
                     }
                     else
                     {
                        x0 = rect0.x + rect0.width - 2;
                        y0 = rect0.y;
                     }
                  }
                  else if(scale0 == 0)
                  {
                     x0 = rect0.x + rect0.width - 2;
                     y0 = rect0.y + rect0.height - 2;
                  }
                  else
                  {
                     x0 = rect0.x;
                     y0 = rect0.y + rect0.height - 2;
                  }
                  for(cx = 0; cx < srange; cx++)
                  {
                     for(cy = 0; cy < srange; cy++)
                     {
                        sname0 = "n" + (x0 + cx - srange2) + "_" + (y0 + cy - srange2);
                        if(nn == 0)
                        {
                           if(!sn0)
                           {
                              sn0 = obj0[sname0];
                           }
                        }
                        else if(!sn1)
                        {
                           sn1 = obj0[sname0];
                        }
                        if(Boolean(sn0) && Boolean(sn1))
                        {
                           break;
                        }
                     }
                     if(Boolean(sn0) && Boolean(sn1))
                     {
                        break;
                     }
                  }
                  if(Boolean(sn0) && Boolean(sn1))
                  {
                     break;
                  }
               }
            }
            if(_sp0.alpha == 1)
            {
               sn0.arr.push(sn1.id);
               sn1.arr.push(sn0.id);
            }
            else if(_sp0.alpha == 0.5)
            {
               if(sn0.y > sn1.y)
               {
                  sn1.arr.push(sn0.id);
               }
               else
               {
                  sn0.arr.push(sn1.id);
               }
            }
         }
      }
      
      public function clear() : void
      {
         this.arr.length = 0;
         this.obj = new Object();
         this.open.length = 0;
         this.close.length = 0;
         this.nn_obj = new Object();
         this.nn_obj_haveDataB = false;
      }
      
      public function pre_cacheNodeToNote(p:int) : *
      {
         var n1:SpiderWebNode = null;
         this.nn_obj_haveDataB = true;
         var n0:SpiderWebNode = this.arr[p];
         this.nn_obj[n0.id] = new Object();
         var arr_len0:int = int(this.arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            n1 = this.arr[i];
            this.nn_obj[n0.id][n1.id] = this.findPath_A(n0.id,n1.id);
         }
      }
      
      private function cacheNodeToNote() : void
      {
         var tt0:Number = getTimer();
         var arr_len0:int = int(this.arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            this.pre_cacheNodeToNote(i);
         }
      }
      
      public function findPath(id0:String, id1:String) : Array
      {
         var arr0:Array = this.nn_obj[id0][id1];
         var arr1:Array = [];
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            arr1[n] = arr0[n];
         }
         return arr1;
      }
      
      public function findPathXY(x0:int, y0:int, x1:int, y1:int, minY0:int, minY1:int, mustHavePointB0:Boolean = false) : Array
      {
         var arr0:Array = null;
         var n2:SpiderWebNode = null;
         var n0:SpiderWebNode = this.findNearNode(x0,y0,minY0);
         var n1:SpiderWebNode = this.findNearNode(x1,y1,minY1);
         if(mustHavePointB0)
         {
            if(!(n0 is SpiderWebNode))
            {
               n0 = this.findNearNode(x0,y0);
            }
            if(!(n1 is SpiderWebNode))
            {
               n1 = this.findNearNode(x1,y1);
            }
         }
         if(Boolean(n0) && Boolean(n1))
         {
            arr0 = this.findPath(n0.id,n1.id);
            n2 = new SpiderWebNode();
            n2.x = x1;
            n2.y = y1;
            arr0.push(n2);
            return arr0;
         }
         return null;
      }
      
      public function findNearNode(x0:int, y0:int, minY:int = -999999, limitRect0:Rectangle = null) : SpiderWebNode
      {
         var n0:SpiderWebNode = null;
         var clong:Number = NaN;
         var minLong:Number = 10000000000000000;
         var minN:SpiderWebNode = null;
         var arr_len0:int = int(this.arr.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            n0 = this.arr[n];
            if(n0.y > minY)
            {
               if(!limitRect0 || limitRect0.contains(n0.x,n0.y))
               {
                  clong = (n0.x - x0) * (n0.x - x0) + (n0.y - y0) * (n0.y - y0);
                  if(clong < minLong)
                  {
                     minLong = clong;
                     minN = n0;
                  }
               }
            }
         }
         return minN;
      }
      
      public function randomHaveTwoNode() : SpiderWebNode
      {
         var n0:SpiderWebNode = null;
         if(this.arr.length == 0)
         {
            return null;
         }
         var loopNum0:int = 10;
         for(var i:int = 0; i <= loopNum0; i++)
         {
            n0 = this.arr[int(Math.random() * this.arr.length)];
            if(n0.arr.length >= 2)
            {
               return n0;
            }
         }
         return this.arr[int(Math.random() * this.arr.length)];
      }
      
      private function findPath_A(id0:String, id1:String) : Array
      {
         var p:* = undefined;
         var n0:SpiderWebNode = null;
         var n1:SpiderWebNode = null;
         var i:int = 0;
         var minLong:int = 0;
         var minN:SpiderWebNode = null;
         var open_len0:int = 0;
         var j:int = 0;
         var arr0:Array = null;
         var arr_len0:int = 0;
         var n:int = 0;
         var cn0:SpiderWebNode = null;
         var cn2:SpiderWebNode = null;
         var G2:int = 0;
         var father0:SpiderWebNode = null;
         for(p in this.arr)
         {
            this.arr[p].clear();
         }
         n0 = this.obj[id0];
         n1 = this.obj[id1];
         this.open.length = 0;
         this.close.length = 0;
         this.open.push(n0);
         for(i = 0; i < 10000; i++)
         {
            if(this.open.length == 0)
            {
               break;
            }
            minLong = 100000;
            minN = null;
            open_len0 = int(this.open.length);
            for(j = 0; j < open_len0; j++)
            {
               cn0 = this.open[j];
               if(cn0.F < minLong)
               {
                  minLong = cn0.F;
                  minN = cn0;
               }
            }
            this.open.splice(this.open.indexOf(minN),1);
            this.close.push(minN);
            arr0 = minN.arr;
            if(arr0.indexOf(id1) >= 0)
            {
               n1.father = minN;
               break;
            }
            arr_len0 = int(arr0.length);
            for(n = 0; n < arr_len0; n++)
            {
               cn2 = this.obj[arr0[n]];
               if(this.close.indexOf(cn2) == -1)
               {
                  if(this.open.indexOf(cn2) == -1)
                  {
                     this.open.push(cn2);
                     cn2.father = minN;
                     cn2.G = Math.sqrt((cn2.x - minN.x) * (cn2.x - minN.x) + (cn2.y - minN.y) * (cn2.y - minN.y)) + minN.G;
                     cn2.H = Math.sqrt((cn2.x - n1.x) * (cn2.x - n1.x) + (cn2.y - n1.y) * (cn2.y - n1.y));
                     cn2.F = cn2.G + cn2.H;
                  }
                  else
                  {
                     G2 = Math.sqrt((cn2.x - minN.x) * (cn2.x - minN.x) + (cn2.y - minN.y) * (cn2.y - minN.y)) + minN.G;
                     if(G2 < cn2.G)
                     {
                        cn2.G = G2;
                        cn2.father = minN;
                        cn2.F = cn2.G + cn2.H;
                     }
                  }
               }
            }
         }
         var newarr0:Array = [];
         if(Boolean(n1.father))
         {
            father0 = n1;
            while(father0 != n0)
            {
               father0 = father0.father;
               newarr0.unshift(father0);
            }
            newarr0.push(n1);
            return newarr0;
         }
         return null;
      }
      
      public function getLeftmost() : SpiderWebNode
      {
         var s0:SpiderWebNode = null;
         var left0:Number = 99999;
         var leftS0:SpiderWebNode = null;
         for each(s0 in this.arr)
         {
            if(s0.x < left0)
            {
               left0 = s0.x;
               leftS0 = s0;
            }
         }
         return leftS0;
      }
      
      public function getShape() : Shape
      {
         var n:* = undefined;
         var s0:SpiderWebNode = null;
         var i:* = undefined;
         var name0:String = null;
         var s1:SpiderWebNode = null;
         var sh0:Shape = new Shape();
         var gh0:Graphics = sh0.graphics;
         for(n in this.arr)
         {
            s0 = this.arr[n];
            gh0.beginFill(65280);
            gh0.drawCircle(s0.x,s0.y,5);
            gh0.lineStyle(null,16711680);
            for(i in s0.arr)
            {
               name0 = s0.arr[i];
               s1 = this.obj[name0];
               gh0.moveTo(s0.x,s0.y);
               gh0.lineTo(s1.x,s1.y);
            }
         }
         return sh0;
      }
   }
}

