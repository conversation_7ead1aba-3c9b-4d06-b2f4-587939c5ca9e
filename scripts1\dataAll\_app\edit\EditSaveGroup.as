package dataAll._app.edit
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   
   public class EditSaveGroup
   {
      
      public static var pro_arr:Array = null;
      
      protected var saveClass:Class = EditSave;
      
      public var arr:Array = [];
      
      public var lastId:Number = 0;
      
      public function EditSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.arr = ClassProperty.copySaveArray(obj0["arr"],this.saveClass);
      }
      
      public function addSave(s0:EditSave) : void
      {
         this.lastId = Math.round(this.lastId + 1);
         s0.id = String(this.lastId);
         this.arr.push(s0);
      }
      
      public function removeSave(s0:EditSave) : void
      {
         ArrayMethod.remove(this.arr,s0);
      }
      
      public function getNewSave() : EditSave
      {
         return new this.saveClass();
      }
   }
}

