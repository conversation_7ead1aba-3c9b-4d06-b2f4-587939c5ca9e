package dataAll.arms.save
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.bookGet.ArmsBookGetter;
   import dataAll.arms.creator.ArmsElementCtrl;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.creator.ArmsUpgradeCtrl;
   import dataAll.arms.creator.GunImageCreator;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.GunPart;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.bullet.BulletBounceDefine;
   import dataAll.bullet.BulletCritDefine;
   import dataAll.bullet.BulletFollowDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ComplexSave;
   import dataAll.items.save.ItemsSave;
   import dataAll.things.save.ThingsSaveGroup;
   
   public class ArmsSave extends ComplexSave
   {
      
      public static var pro_arr:Array = [];
      
      private static const _yearTiger:String = "yearTiger";
      
      private static const _yearSheep:String = "yearSheep";
      
      private static const _lightCone:String = "lightCone";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      protected var V:Number = Math.random() / 5 + 0.01;
      
      public var upgradeB:Boolean = true;
      
      public var armsType:String = "";
      
      public var armsLevel:int = 1;
      
      private var _hurtRatio:Number = 0;
      
      public var capacity:int = 0;
      
      public var attackGap:Number = 0;
      
      private var _reloadGap:Number = 0;
      
      public var shakeAngle:Number = 0;
      
      public var bulletWidth:int = 7;
      
      public var bulletShakeWidth:int = 0;
      
      public var bulletNum:int = 1;
      
      public var shootAngle:Number = 0;
      
      public var skillArr:Array = [];
      
      public var godSkillArr:Array = [];
      
      public var bounceD:BulletBounceDefine = new BulletBounceDefine();
      
      public var followD:BulletFollowDefine = new BulletFollowDefine();
      
      public var critD:BulletCritDefine = new BulletCritDefine();
      
      public var twoShootPro:Number = 0;
      
      public var penetrationNum:int = 0;
      
      public var penetrationGap:int = 0;
      
      public var armsImgLabel:String = "";
      
      public var s:String = "";
      
      public var shootSoundUrl:String = "";
      
      public var partsSave:ThingsSaveGroup = new ThingsSaveGroup();
      
      public var ele:String = "";
      
      public var firstChoiceB:Boolean = false;
      
      public var l:Array = [];
      
      public var o:Object = {};
      
      public function ArmsSave()
      {
         super();
         itemsType = ItemsDataGroup.TYPE_ARMS;
         this.partsSave.initSaveInArmsParts();
         this.strengthenLv = 0;
         this.strengthenNum = 0;
         this.evoLv = 1;
         this.eleLv = 0;
      }
      
      public static function getShootSoundUrl(label0:String) : String
      {
         var str0:String = getPartUrl(label0,"barrel");
         if(str0 == "0")
         {
            str0 = "";
         }
         else
         {
            str0 += "_sound";
         }
         return str0;
      }
      
      public static function getPartUrl(label0:String, str0:String) : String
      {
         var index0:int = int(GunPart.ARR.indexOf(str0));
         var arr0:Array = label0.split("_");
         var url0:String = arr0[index0];
         return url0.replace("$","/");
      }
      
      public function get strengthenLv() : Number
      {
         return this.CF.getAttribute("strengthenLv");
      }
      
      public function set strengthenLv(v0:Number) : void
      {
         this.CF.setAttribute("strengthenLv",v0);
      }
      
      public function get strengthenNum() : Number
      {
         return this.CF.getAttribute("strengthenNum");
      }
      
      public function set strengthenNum(v0:Number) : void
      {
         this.CF.setAttribute("strengthenNum",v0);
      }
      
      public function get sMaxLv() : Number
      {
         return this.CF.getAttribute("sMaxLv");
      }
      
      public function set sMaxLv(v0:Number) : void
      {
         this.CF.setAttribute("sMaxLv",v0);
      }
      
      public function get evoLv() : Number
      {
         return this.CF.getAttribute("evoLv");
      }
      
      public function set evoLv(v0:Number) : void
      {
         this.CF.setAttribute("evoLv",v0);
      }
      
      public function get eleLv() : Number
      {
         return this.CF.getAttribute("eleLv");
      }
      
      public function set eleLv(v0:Number) : void
      {
         this.CF.setAttribute("eleLv",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         var skillLabel0:String = null;
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.fleshSMaxLv();
         if(this.partsSave.arr.length == 0)
         {
            this.partsSave.initSaveInArmsParts();
         }
         if(obj0.hasOwnProperty("skillLabel"))
         {
            skillLabel0 = obj0["skillLabel"];
            if(Boolean(skillLabel0) && skillLabel0 != "")
            {
               this.skillArr.push(skillLabel0);
            }
         }
         itemsType = ItemsDataGroup.TYPE_ARMS;
         if(name == _yearTiger)
         {
            this.penetrationNum = 4;
            if(this.godSkillArr.length <= 1)
            {
               ArrayMethod.addNoRepeatInArr(this.godSkillArr,"yearTigerHurtSuper");
            }
         }
         else if(name == _yearSheep)
         {
            this.penetrationNum = 12;
         }
         else if(name == _lightCone)
         {
            this.penetrationNum = 5;
            this.shootSoundUrl = "";
            if(this.godSkillArr.length <= 1)
            {
               ArrayMethod.addNoRepeatInArr(this.godSkillArr,"lightConeBlind");
            }
         }
      }
      
      public function getArmsRangeDefine() : ArmsRangeDefine
      {
         return Gaming.defineGroup.bullet.getArmsRangeDefine(name);
      }
      
      public function fleshSMaxLv() : void
      {
         if(this.sMaxLv < this.strengthenLv)
         {
            this.sMaxLv = this.strengthenLv;
         }
      }
      
      override public function isImportantB() : Boolean
      {
         if(super.isImportantB())
         {
            return true;
         }
         if(EquipColor.moreColorPan(color,EquipColor.BLACK))
         {
            return true;
         }
         return false;
      }
      
      override public function getCnName() : String
      {
         return ArmsEvoCtrl.getCnName(cnName,this.evoLv,this.getArmsRangeDefine().def);
      }
      
      override public function getChildType() : String
      {
         return this.armsType;
      }
      
      override public function getChildTypeCnName() : String
      {
         var d0:ArmsChargerDefine = Gaming.defineGroup.armsCharger.getDefine(this.armsType);
         return d0.cnName;
      }
      
      override public function isOnceGetB() : Boolean
      {
         if(Boolean(ArmsBookGetter.getDefine(name)))
         {
            return true;
         }
         return false;
      }
      
      public function haveSpecialB() : Boolean
      {
         return this.penetrationGap > 0 || this.penetrationNum > 0 || this.bounceD.floor > 0 || this.bounceD.body > 0 || this.critD.mul > 0 || this.twoShootPro > 0;
      }
      
      override public function isMoreRedB() : Boolean
      {
         return this.getArmsRangeDefine().def.isMoreRedB();
      }
      
      public function setArmsImgLabel(str0:String, defineSoundUrl0:String = "") : void
      {
         this.armsImgLabel = str0;
         this.shootSoundUrl = getShootSoundUrl(this.armsImgLabel);
         if(this.shootSoundUrl == "")
         {
            this.shootSoundUrl = defineSoundUrl0;
         }
      }
      
      public function getImgLabel() : String
      {
         var d0:ArmsSkinDefine = null;
         if(this.s != "")
         {
            d0 = Gaming.defineGroup.armsCharger.getSkin(this.s);
            if(Boolean(d0))
            {
               return GunImageCreator.getArmsImgLabelByUrl(d0.getUrl());
            }
            this.s = "";
         }
         return this.armsImgLabel;
      }
      
      public function getNowSkin() : String
      {
         if(this.s == "")
         {
            return this.getBaseSkin();
         }
         return this.s;
      }
      
      public function getBaseSkin() : String
      {
         return GunImageCreator.getSkinByArmsImgLabel(this.armsImgLabel);
      }
      
      public function getThisPartUrl(str0:String) : String
      {
         return getPartUrl(this.getImgLabel(),str0);
      }
      
      override public function getTrueLevel() : int
      {
         return itemsLevel + addLevel;
      }
      
      public function getMaxLevel() : int
      {
         var add0:int = ArmsUpgradeCtrl.maxAddLevel;
         return int(itemsLevel + add0);
      }
      
      override public function getStrengthenLv() : int
      {
         return this.strengthenLv;
      }
      
      override public function getEvoLv() : int
      {
         return this.evoLv;
      }
      
      override public function setStrengthenLvAndMax(lv0:int) : void
      {
         this.strengthenLv = lv0;
         this.sMaxLv = lv0;
      }
      
      public function getStarNum() : int
      {
         return int(this.strengthenLv / 5);
      }
      
      public function doEvo(addLv0:int = 1) : void
      {
         var i:int = 0;
         var skillName0:String = null;
         var skillArr0:Array = ArmsEvoCtrl.darkgoldSkillArr;
         var d0:ArmsDefine = this.getArmsRangeDefine().def;
         this.evoLv += addLv0;
         var panLv0:int = this.evoLv + d0.evoMustFirstLv;
         if(panLv0 == ArmsEvoCtrl.darkgoldLv)
         {
            color = EquipColor.DARKGOLD;
            for(i = 0; i < this.godSkillArr.length; i++)
            {
               skillName0 = this.godSkillArr[i];
               if(skillArr0.indexOf(skillName0) >= 0)
               {
                  this.godSkillArr[i] = skillName0 + "2";
               }
            }
         }
         else if(panLv0 == ArmsEvoCtrl.purgoldLv)
         {
            color = EquipColor.PURGOLD;
            ArmsEvoCtrl.doEvoPurgold(this);
         }
         else if(panLv0 == ArmsEvoCtrl.yagoldLv)
         {
            color = EquipColor.YAGOLD;
            ArmsEvoCtrl.yagoldEvo(this);
         }
      }
      
      public function getEleHurtMul() : Number
      {
         return ArmsElementCtrl.getHurtMul(this.eleLv);
      }
      
      public function getEleAddDps() : Number
      {
         var v0:Number = NaN;
         if(this.ele != "")
         {
            return this.getEleHurtMul() * 0.3;
         }
         return 0;
      }
      
      public function doElement(name0:String) : Boolean
      {
         var lv0:int = 0;
         if(this.ele != name0)
         {
            this.ele = name0;
            if(this.eleLv < 1)
            {
               this.eleLv = 1;
            }
            return true;
         }
         lv0 = this.eleLv;
         if(lv0 < ArmsElementCtrl.getMaxLv())
         {
            lv0++;
            this.eleLv = lv0;
            return true;
         }
         return false;
      }
      
      public function copy() : ArmsSave
      {
         var s0:ArmsSave = new ArmsSave();
         s0.inData_byObj(this);
         return s0;
      }
      
      override public function copyOne() : ItemsSave
      {
         return this.copy();
      }
      
      override public function getSimulateData(pd0:NormalPlayerData) : IO_ItemsData
      {
         var da0:ArmsData = new ArmsData();
         da0.inData_bySave(this,pd0,false);
         da0.fleshData_byEquip(pd0.getHeroMerge());
         return da0;
      }
      
      public function set reloadGap(v0:Number) : void
      {
         this._reloadGap = v0 / this.V;
      }
      
      public function get reloadGap() : Number
      {
         return this._reloadGap * this.V;
      }
      
      public function set hurtRatio(v0:Number) : void
      {
         var xx0:int = 0;
         if(this.evoLv > 1)
         {
            xx0 = 0;
         }
         this._hurtRatio = v0 / this.V;
      }
      
      public function get hurtRatio() : Number
      {
         return Math.round(this._hurtRatio * this.V);
      }
   }
}

