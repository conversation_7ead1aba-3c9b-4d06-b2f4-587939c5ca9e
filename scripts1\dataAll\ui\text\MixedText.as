package dataAll.ui.text
{
   import com.sounto.utils.StringMethod;
   
   public class MixedText
   {
      
      private static var tempLabel:MixedTextLabel = new MixedTextLabel();
      
      private var text:String = "";
      
      public var htmlText:String = "";
      
      public var iconDataArr:Vector.<MixedTextIconData> = new Vector.<MixedTextIconData>();
      
      public function MixedText(text0:String, backColor0:String = "")
      {
         super();
         this.text = text0;
         this.toHtmlText(backColor0);
      }
      
      private function toHtmlText(backColor0:String) : void
      {
         var char0:String = null;
         var t0:String = this.text;
         var arrLen0:int = t0.length;
         var iconCharIndex0:int = 0;
         var l0:MixedTextLabel = tempLabel;
         l0.init();
         for(var i:int = 0; i < arrLen0; i++)
         {
            char0 = t0.charAt(i);
            l0.inChar(i,char0);
            if(l0.state == MixedTextState.NO)
            {
               this.htmlText += char0;
            }
            else if(l0.state == MixedTextState.OVER)
            {
               if(l0.type == MixedTextLabel.ICON)
               {
                  this.addIconData(iconCharIndex0,l0);
                  iconCharIndex0 += l0.replaceStr.length;
                  if(backColor0 == "")
                  {
                     this.htmlText += l0.replaceStr;
                  }
                  else
                  {
                     this.htmlText += "<font color=\'#202020\'>" + l0.replaceStr + "</font>";
                  }
               }
               else
               {
                  this.htmlText += l0.replaceStr;
               }
               l0.init();
            }
            else if(l0.state == MixedTextState.ERROR)
            {
               l0.init();
               return;
            }
         }
         this.dealTab();
      }
      
      private function addIconData(index0:int, l0:MixedTextLabel) : void
      {
         var da0:MixedTextIconData = new MixedTextIconData();
         da0.index = index0;
         da0.replaceStr = l0.replaceStr;
         da0.label = l0.label;
         this.iconDataArr.push(da0);
      }
      
      private function dealTab() : void
      {
         var strArr0:Array = null;
         var maxGap0:int = 0;
         var gapArr0:Array = null;
         var index0:int = 0;
         var str0:String = null;
         var index2:* = undefined;
         var arrLen0:int = 0;
         var len0:Number = NaN;
         var state0:String = null;
         var i:int = 0;
         var c0:String = null;
         var str2:String = null;
         var gap0:int = 0;
         if(this.htmlText.indexOf("|") >= 0)
         {
            strArr0 = this.htmlText.split("\n");
            maxGap0 = 0;
            gapArr0 = [];
            index0 = 0;
            for each(str0 in strArr0)
            {
               if(str0.indexOf("|") >= 0)
               {
                  arrLen0 = str0.length;
                  len0 = 0;
                  state0 = MixedTextState.NO;
                  for(i = 0; i < arrLen0; i++)
                  {
                     c0 = str0.charAt(i);
                     if(c0 == "|")
                     {
                        if(len0 > maxGap0)
                        {
                           maxGap0 = len0;
                        }
                        break;
                     }
                     if(state0 == MixedTextState.NO)
                     {
                        if(c0 == "<")
                        {
                           state0 = MixedTextState.FIND_END;
                        }
                        else
                        {
                           len0 += c0.charCodeAt() < 256 ? 1 : 2;
                        }
                     }
                     else if(state0 == MixedTextState.FIND_END)
                     {
                        if(c0 == ">")
                        {
                           state0 = MixedTextState.NO;
                        }
                     }
                  }
                  gapArr0[index0] = len0;
               }
               else
               {
                  gapArr0[index0] = -1;
               }
               index0++;
            }
            for(index2 in strArr0)
            {
               str2 = strArr0[index2];
               gap0 = int(gapArr0[index2]);
               if(gap0 >= 0)
               {
                  strArr0[index2] = str2.replace("|",StringMethod.getRepeartStr(maxGap0 - gap0 + 2," "));
               }
            }
            this.htmlText = StringMethod.concatStringArr(strArr0,1,"\n");
         }
      }
   }
}

