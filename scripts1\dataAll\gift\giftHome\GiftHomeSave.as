package dataAll.gift.giftHome
{
   import com.common.data.Base64;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class GiftHomeSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var THING_NAME:String = "occupyStamp";
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var recordArr:Array = [];
      
      public var levelObj:Object = {};
      
      public function GiftHomeSave()
      {
         super();
         this.daySubNum = 0;
         this.dayDropNum = 0;
      }
      
      public function get daySubNum() : Number
      {
         return this.CF.getAttribute("daySubNum");
      }
      
      public function set daySubNum(v0:Number) : void
      {
         this.CF.setAttribute("daySubNum",v0);
      }
      
      public function get dayDropNum() : Number
      {
         return this.CF.getAttribute("dayDropNum");
      }
      
      public function set dayDropNum(v0:Number) : void
      {
         this.CF.setAttribute("dayDropNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.levelObj = ClassProperty.copySaveObj(obj0["levelObj"],GiftHomeLevelSave);
      }
      
      public function initPan() : void
      {
         var nameArr0:Array = null;
         var name0:String = null;
         var s0:GiftHomeLevelSave = null;
         var d0:GiftHomeLevelDefine = null;
         if(ComMethod.getObjElementNum(this.levelObj) == 0)
         {
            nameArr0 = Gaming.defineGroup.giftHome.getLevelNameArr();
            for each(name0 in nameArr0)
            {
               s0 = new GiftHomeLevelSave();
               d0 = Gaming.defineGroup.giftHome.getLevelDefine(name0);
               s0.inDataByDefine(d0);
               this.levelObj[name0] = s0;
            }
         }
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.daySubNum = 0;
         this.dayDropNum = 0;
      }
      
      public function getLevelSave(name0:String) : GiftHomeLevelSave
      {
         return this.levelObj[name0];
      }
      
      public function inAllNum(num0:Number) : void
      {
         var s0:GiftHomeLevelSave = null;
         for each(s0 in this.levelObj)
         {
            s0.inAllNum(num0);
         }
      }
      
      public function inOccupyNum(num0:Number) : void
      {
         var s0:GiftHomeLevelSave = null;
         this.occupyRecord(num0);
         for each(s0 in this.levelObj)
         {
            s0.inOccupyNum(num0);
         }
      }
      
      public function getDayMaxNum() : int
      {
         return 5;
      }
      
      public function getDayNowNum() : int
      {
         return this.getDayMaxNum() - this.daySubNum;
      }
      
      public function getDayNowNumString() : String
      {
         var num0:int = this.getDayNowNum();
         return num0 == 0 ? "" : num0 + "";
      }
      
      private function occupyRecord(floor0:Number) : void
      {
         var str0:String = Base64.encodeString(floor0 + "");
         if(this.recordArr.indexOf(str0) == -1)
         {
            this.recordArr.unshift(str0);
         }
      }
      
      public function getRecordNum() : int
      {
         return this.recordArr.length;
      }
      
      public function getRecordArr() : Array
      {
         var str0:String = null;
         var arr0:Array = [];
         for each(str0 in this.recordArr)
         {
            arr0.push(Number(Base64.decodeString(str0)));
         }
         return arr0;
      }
      
      public function getNewRecordStr(name0:String) : String
      {
         var s0:GiftHomeLevelSave = null;
         var v0:Number = NaN;
         var str0:String = null;
         var arr0:Array = this.getRecordArr();
         if(arr0.length == 0)
         {
            return "无";
         }
         s0 = this.getLevelSave(name0);
         v0 = Number(arr0[0]);
         str0 = v0 + "";
         if(s0.getDefine().panEndString(v0) && s0.openB)
         {
            str0 = ComMethod.color(str0,"#00FF00");
         }
         return str0;
      }
   }
}

