package dataAll.body.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.define.EquipDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.geom.Rectangle;
   
   public class HeroDefine extends NormalBodyDefine
   {
      
      public var movieLink:String = "";
      
      public var upperImgArr:Array = EquipDefine.upperImgArr;
      
      public var headPlayB:Boolean = false;
      
      public var squatHitRect:Rectangle = new Rectangle();
      
      public var squatMaxVx:Number = 5;
      
      public var armsNumber:int = 1;
      
      public var randomArmsRange:Array = [];
      
      public var aircraft:String = "";
      
      public var studyCnNameArr:Array = [];
      
      public var p1SkillArr:Array = [];
      
      public var addMoreText:String = "";
      
      public function HeroDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML, systemType0:String, father0:String) : void
      {
         super.inData_byXML(xml0,systemType0,father0);
         this.studyCnNameArr = String(xml0.studyCnNameArr).split(",");
         this.p1SkillArr = String(xml0.p1SkillArr).split(",");
         this.addMoreText = String(xml0.addMoreText);
         this.movieLink = String(xml0.movieLink);
         this.headPlayB = Boolean(int(xml0.headPlayB));
         this.squatHitRect = ComMethod.getRect(xml0.squatHitRect);
         if(String(xml0.squatMaxVx) != "")
         {
            this.squatMaxVx = Number(xml0.squatMaxVx);
         }
         this.armsNumber = int(xml0.armsNumber);
         if(this.armsNumber <= 0)
         {
            this.armsNumber = 1;
         }
         this.randomArmsRange = String(xml0.randomArmsRange).split(",");
         if(this.randomArmsRange[0] == "")
         {
            INIT.showError("不能没有 randomArmsRange 属性");
         }
         this.aircraft = String(xml0.aircraft);
      }
      
      public function getBonesHeroDefine() : HeroDefine
      {
         if(this.movieLink == "")
         {
            return this;
         }
         return Gaming.defineGroup.body.getHeroDefine(this.movieLink);
      }
      
      override public function inFastData_byXML(xml0:XML, systemType0:String, father0:String) : void
      {
         xmlBat = xml0;
         father = father0;
         systemType = systemType0;
         ClassProperty.inData_byXML(this,xml0,pro_arr);
      }
      
      override public function inData_byObj(obj:Object) : void
      {
         ClassProperty.inData(this,obj,pro_arr);
      }
      
      public function getHeroSkillCnArr(p1B0:Boolean) : Array
      {
         if(p1B0)
         {
            return this.studyCnNameArr;
         }
         if(this.p1SkillArr.length == 0)
         {
            return this.studyCnNameArr;
         }
         return ArrayMethod.deductArr(this.studyCnNameArr,this.p1SkillArr);
      }
      
      public function isP1SkillB(d0:SkillDefine) : Boolean
      {
         return this.p1SkillArr.indexOf(d0.cnName) >= 0;
      }
      
      override public function clone() : NormalBodyDefine
      {
         var d0:NormalBodyDefine = new HeroDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      override public function cloneByXml() : NormalBodyDefine
      {
         var d0:NormalBodyDefine = new HeroDefine();
         d0.inData_byXML(xmlBat,systemType,father);
         return d0;
      }
   }
}

