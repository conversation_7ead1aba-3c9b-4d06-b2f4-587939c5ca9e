package dataAll.arms.creator
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.body.attack.ElementHurtDefine;
   import dataAll.must.define.MustDefine;
   
   public class ArmsElementCtrl
   {
      
      public function ArmsElementCtrl()
      {
         super();
      }
      
      public static function getAfterData(da0:ArmsData, elementD0:ElementHurtDefine) : ArmsData
      {
         var affterS0:ArmsSave = null;
         var affterDa0:ArmsData = null;
         var nowEle0:String = da0.save.ele;
         if(elementD0.name != nowEle0 || !isMaxB(da0.save))
         {
            affterS0 = da0.save.copy();
            affterS0.doElement(elementD0.name);
            affterDa0 = new ArmsData();
            affterDa0.inData_bySave(affterS0,da0.normalPlayerData,true);
            affterDa0.fleshData_byMeEquip();
            return affterDa0;
         }
         return null;
      }
      
      public static function getMust(da0:ArmsData, elementD0:ElementHurtDefine) : MustDefine
      {
         var s0:ArmsSave = da0.save;
         var d0:MustDefine = new MustDefine();
         var thingArr0:Array = [];
         var targetLv0:int = s0.eleLv + 1;
         var num0:Number = getGemNum(targetLv0);
         if(s0.ele != "" && s0.ele != elementD0.name)
         {
            num0 = Math.ceil(getGemSum(s0.eleLv) * 0.66);
         }
         thingArr0.push(elementD0.getGemName() + ";" + num0);
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function getGemNum(lv0:int) : Number
      {
         return (lv0 - 1) * 10 + 50;
      }
      
      private static function getGemSum(lv0:int) : Number
      {
         var num0:Number = 0;
         for(var i:int = 1; i <= lv0; i++)
         {
            num0 += getGemNum(i);
         }
         return num0;
      }
      
      public static function deal(da0:ArmsData, elementD0:ElementHurtDefine) : void
      {
         da0.save.doElement(elementD0.name);
         da0.fleshData_byMeEquip();
      }
      
      public static function isMaxB(s0:ArmsSave) : Boolean
      {
         if(s0.eleLv >= getMaxLv())
         {
            return true;
         }
         return false;
      }
      
      public static function getMaxLv() : int
      {
         return 10;
      }
      
      public static function getHurtMul(lv0:int) : Number
      {
         var maxLv0:int = getMaxLv();
         if(lv0 > maxLv0)
         {
            lv0 = maxLv0;
         }
         return lv0 * 0.05 + 0.05;
      }
   }
}

