package dataAll.equip.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.must.define.MustDefine;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipEvoCtrl
   {
      
      private static const PRO:String = "dpsAllBlack";
      
      private static const cnArr:Array = ["$","$II","$III","闪耀·$","闪耀·$II","闪耀·$III","闪耀·$IV","绝世·$","绝世·$II"];
      
      private static const hurtMulArr:Array = [100,125,160,195,220,245,270,300];
      
      private static const darkgoldCnArr:Array = ["$","$II","$III","$IV","卓越·$","卓越·$II","卓越·$III","卓越·$IV","超凡·$","超凡·$II","超凡·$III","超凡·$IV","超凡·$V","超凡·$VI","超凡·$VII","超凡·$VIII"].concat(["$","$II","$III","$IV"]);
      
      private static const hurtAddArr:Array = [0,3,6,9,12,14,16,19,22,25,28,31,33,35,37,39].concat([42,48]);
      
      public function EquipEvoCtrl()
      {
         super();
      }
      
      public static function get purgoldLv() : int
      {
         return 17;
      }
      
      private static function get madheartLv() : int
      {
         return 13;
      }
      
      public static function getCnName(d0:EquipDefine, evoLv0:int) : String
      {
         var cn0:String = d0.cnName;
         var index0:int = evoLv0 - 1;
         var arr0:Array = cnArr;
         if(d0.isDarkgoldAndMoreB())
         {
            arr0 = darkgoldCnArr;
         }
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return String(arr0[index0]).replace("$",cn0);
      }
      
      private static function getHurtMul(evoLv0:int) : Number
      {
         var index0:int = evoLv0 - 1;
         var arr0:Array = hurtMulArr;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0] / 100;
      }
      
      private static function getHurtAdd(evoLv0:int) : Number
      {
         var index0:int = evoLv0 - 1;
         var arr0:Array = hurtAddArr;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0] / 100;
      }
      
      private static function getMaxLv(d0:EquipDefine) : int
      {
         var f0:EquipFatherDefine = d0.getFatherDefine();
         if(Boolean(f0))
         {
            if(f0.evoB)
            {
               if(f0.color == EquipColor.DARKGOLD || f0.color == EquipColor.PURGOLD || f0.color == EquipColor.YAGOLD)
               {
                  if(d0.type == EquipType.COAT || d0.type == EquipType.HEAD)
                  {
                     return 18;
                  }
                  return 17;
               }
               return 8;
            }
         }
         return 0;
      }
      
      public static function canEvoB(da0:EquipData) : Boolean
      {
         var maxLv0:int = 0;
         var d0:EquipDefine = da0.save.getDefine();
         if(d0.isCanEvoB())
         {
            maxLv0 = getMaxLv(d0);
            if(da0.save.evoLv < maxLv0)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function isMaxB(da0:EquipData) : Boolean
      {
         var d0:EquipDefine = da0.save.getDefine();
         if(da0.save.evoLv >= getMaxLv(d0))
         {
            return true;
         }
         return false;
      }
      
      public static function getNewObj(d0:EquipDefine, obj0:Object, evoLv0:int) : Object
      {
         var v0:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var name0:String = PRO;
         var darkgoldB0:Boolean = d0.isDarkgoldAndMoreB();
         if(obj0.hasOwnProperty(name0))
         {
            v0 = Number(obj0[name0]);
            if(darkgoldB0)
            {
               v0 += getHurtAdd(evoLv0);
            }
            else
            {
               v0 *= getHurtMul(evoLv0);
            }
            proD0 = Gaming.defineGroup.getPropertyArrayDefine(name0);
            obj0[name0] = proD0.fixedNumber(v0);
         }
         return obj0;
      }
      
      public static function getProText(da0:EquipData) : String
      {
         var name0:String = PRO;
         var obj0:Object = da0.save.getTrueObj();
         var v0:Number = Number(obj0[name0]);
         var proD0:PropertyArrayDefine = Gaming.defineGroup.getPropertyArrayDefine(name0);
         return proD0.cnName + "：" + ComMethod.color(proD0.getFixedValueString(v0),"#00FF00");
      }
      
      public static function getAfterData(da0:EquipData) : EquipData
      {
         var affter_s0:EquipSave = null;
         var affter_da0:EquipData = null;
         if(!isMaxB(da0))
         {
            affter_s0 = da0.save.clone();
            affter_da0 = affter_s0.getDataClass();
            affter_da0.inData_bySave(affter_s0,null);
            evo(affter_da0);
            return affter_da0;
         }
         return null;
      }
      
      public static function evo(da0:EquipData) : void
      {
         var newName0:String = null;
         var newD0:EquipDefine = null;
         var s0:EquipSave = da0.save;
         ++s0.evoLv;
         if(s0.evoLv >= purgoldLv)
         {
            newName0 = "madpur_" + s0.getDefine().type;
            newD0 = Gaming.defineGroup.equip.getDefine(newName0);
            s0.purgoldInDefine(newD0);
            s0.color = EquipColor.PURGOLD;
            if(s0.evoLv == purgoldLv)
            {
               s0.skillArr = s0.skillArr.concat(newD0.skillArr);
            }
            else if(s0.evoLv == purgoldLv + 1)
            {
               s0.skillArr = s0.skillArr.concat(newD0.skillArr[0] + "2");
            }
         }
      }
      
      private static function getChipMustNum(evoLv0:int) : int
      {
         var arr0:Array = [50,60,70,80,100,120,200,300];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      public static function getAllChipMustNum(evoLv0:int) : int
      {
         var num0:Number = 0;
         for(var i:int = 1; i <= evoLv0; i++)
         {
            num0 += getChipMustNum(i);
         }
         return num0;
      }
      
      public static function getMust(da0:EquipData) : MustDefine
      {
         var s0:EquipSave = da0.save;
         var ed0:EquipDefine = s0.getDefine();
         if(ed0.isDarkgoldAndMoreB())
         {
            return getDarkgoldMust(da0);
         }
         var d0:MustDefine = new MustDefine();
         d0.lv = 86;
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(d0.lv);
         var thingArr0:Array = [];
         thingArr0.push(s0.getDefine().name + ";" + getChipMustNum(s0.evoLv + 1));
         var mlv0:int = s0.evoLv + 1;
         if(mlv0 >= 7)
         {
            if(s0.partType == EquipType.HEAD || s0.partType == EquipType.PANTS)
            {
               if(mlv0 == 8)
               {
                  thingArr0.push("beadCrossbow;120");
               }
               else
               {
                  thingArr0.push("oracleSuit_head;90");
               }
            }
            else if(mlv0 == 8)
            {
               thingArr0.push("oracleSuit_coat;180");
            }
            else
            {
               thingArr0.push("oracleSuit_belt;90");
            }
         }
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function getDarkgoldMust(da0:EquipData) : MustDefine
      {
         var tname0:String = null;
         var thingsName0:String = null;
         var gemName0:String = null;
         var s0:EquipSave = da0.save;
         var mlv0:int = s0.evoLv + 1;
         if(mlv0 >= madheartLv)
         {
            return getPurgoldMust(da0,mlv0);
         }
         var ed0:EquipDefine = s0.getDefine();
         var d0:MustDefine = new MustDefine();
         d0.lv = 95;
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(d0.lv);
         var thingArr0:Array = [];
         var nameArr0:Array = EquipComposeAgent.MUST_FATHER_ARR;
         var chipArr0:Array = [50,50,60,60,100,100,150,150,100];
         var chipNum0:int = ArrayMethod.getElementLimit(chipArr0,s0.evoLv - 1) as Number;
         for each(tname0 in nameArr0)
         {
            thingsName0 = tname0 + "_" + ed0.type;
            thingArr0.push(thingsName0 + ";" + chipNum0);
         }
         if(mlv0 == 6 || mlv0 == 7)
         {
            if(s0.partType == EquipType.HEAD || s0.partType == EquipType.PANTS)
            {
               thingArr0.push("oracleSuit_head;90");
            }
            else
            {
               thingArr0.push("oracleSuit_belt;90");
            }
         }
         else if(mlv0 == 8 || mlv0 == 9)
         {
            if(s0.partType == EquipType.HEAD || s0.partType == EquipType.PANTS)
            {
               thingArr0.push("beadCrossbow;120");
            }
            else
            {
               thingArr0.push("oracleSuit_coat;180");
            }
         }
         else if(mlv0 >= 10)
         {
            gemName0 = EquipComposeAgent.getMustThingsByType(s0.partType);
            thingArr0.push(gemName0 + ";200");
         }
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function getGemName(partType0:String) : String
      {
         if(partType0 == EquipType.HEAD)
         {
            return "wisdomGem";
         }
         if(partType0 == EquipType.PANTS)
         {
            return "agileGem";
         }
         return "wisdomGem";
      }
      
      private static function getPurgoldMust(da0:EquipData, mlv0:int) : MustDefine
      {
         var ed0:EquipDefine = da0.save.getDefine();
         var type0:String = ed0.type;
         var tarr0:Array = null;
         if(mlv0 < purgoldLv)
         {
            tarr0 = ["madheart;55","demBall;50","demStone;55"];
         }
         else
         {
            tarr0 = ["madheart;80","demBall;80","demStone;80"];
            if(mlv0 == 18)
            {
               if(type0 == EquipType.COAT)
               {
                  tarr0 = ["demBall;150","demStone;150","yaStone;150"];
               }
               else
               {
                  tarr0 = ["demBall;150","demStone;150","yaStone;80","yaRock;80"];
               }
            }
         }
         var d0:MustDefine = new MustDefine();
         d0.lv = 99;
         d0.inThingsDataByArr(tarr0);
         return d0;
      }
   }
}

