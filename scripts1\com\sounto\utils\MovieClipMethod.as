package com.sounto.utils
{
   import flash.display.MovieClip;
   
   public class MovieClipMethod
   {
      
      public function MovieClipMethod()
      {
         super();
      }
      
      public static function mcGoto(mc0:MovieClip, label0:*) : void
      {
         var bb0:Boolean = false;
         if(<PERSON><PERSON>an(mc0))
         {
            bb0 = false;
            if(label0 is String)
            {
               if(label0 != "")
               {
                  bb0 = true;
               }
            }
            else if(label0 is int)
            {
               bb0 = true;
            }
            mc0.visible = bb0;
            mc0.gotoAndStop(bb0 ? label0 : 1);
         }
      }
   }
}

