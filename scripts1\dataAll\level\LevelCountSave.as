package dataAll.level
{
   import com.sounto.utils.ClassProperty;
   import gameAll.level.data.LevelData;
   
   public class LevelCountSave
   {
      
      public static var pro_arr:Array = [];
      
      public var killEnemyNum:int = 0;
      
      public var headshotNum:int = 0;
      
      public var rebirthNum:int = 0;
      
      public var time:Number = 0;
      
      public var coin:Number = 0;
      
      public var exp:Number = 0;
      
      public var orangeNum:Number = 0;
      
      public var armsNum:Number = 0;
      
      public var equipNum:Number = 0;
      
      public var geneNum:Number = 0;
      
      public var bulletNum:Number = 0;
      
      public var hitNum:Number = 0;
      
      public var star:Number = 0;
      
      public function LevelCountSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function initData() : void
      {
         var n:* = undefined;
         var pro0:String = null;
         for(n in pro_arr)
         {
            pro0 = pro_arr[n];
            this[pro0] = 0;
         }
      }
      
      public function inData_byLevelData(levelDat0:LevelData) : void
      {
         this.time = levelDat0.levelTime;
         this.countStar();
      }
      
      private function countStar() : void
      {
         var hitRate0:Number = this.getHitRate();
         this.star = Math.ceil((hitRate0 - 0.3) * 8);
         if(this.star > 5)
         {
            this.star = 5;
         }
         if(this.star < 1)
         {
            this.star = 1;
         }
      }
      
      public function getHitRate() : Number
      {
         if(this.bulletNum == 0)
         {
            return 0;
         }
         return this.hitNum / this.bulletNum;
      }
      
      public function getHeadshotRate() : Number
      {
         if(this.killEnemyNum == 0)
         {
            return 0;
         }
         return this.headshotNum / this.killEnemyNum;
      }
      
      public function addData(s0:LevelCountSave) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         for(n in pro_arr)
         {
            pro0 = pro_arr[n];
            this[pro0] += s0[pro0];
         }
      }
      
      public function toString() : String
      {
         var pro0:String = null;
         var str0:String = "";
         for each(pro0 in LevelCountSave.pro_arr)
         {
            str0 += pro0 + ":" + this[pro0] + "\n";
         }
         return str0;
      }
   }
}

