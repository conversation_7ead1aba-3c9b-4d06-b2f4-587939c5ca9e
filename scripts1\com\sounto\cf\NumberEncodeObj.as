package com.sounto.cf
{
   public class NumberEncodeObj extends NiuBiCF
   {
      
      public function NumberEncodeObj()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         this.saveObj = obj0["saveObj"];
      }
      
      public function set saveObj(obj0:Object) : void
      {
         setObj(obj0);
      }
      
      public function get saveObj() : Object
      {
         return getObj();
      }
      
      public function haveAttribute(varName:String) : Boolean
      {
         return varObj[varName] != null;
      }
      
      override public function setAttribute(varName:String, varValue:Number) : *
      {
         if(varValue == 0)
         {
            varObj[varName] = null;
         }
         else
         {
            super.setAttribute(varName,varValue);
         }
      }
      
      public function addNum(name0:String, v0:Number) : void
      {
         var num0:Number = getAttribute(name0);
         num0 += v0;
         this.setAttribute(name0,num0);
      }
   }
}

