package dataAll.body.attack
{
   import com.sounto.math.IDRect;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.body.define.BodyPart;
   import dataAll.bullet.BulletCritDefine;
   import dataAll.bullet.BulletDefine;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.image.ImageUrlDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.bullet.BulletBody;
   import gameAll.effect.NormalEffectAddit;
   import gameAll.skill.SkillEffectData;
   
   public class HurtData
   {
      
      public static const FROM_ATTACK:String = "attack";
      
      public static const FROM_BULLET:String = "bullet";
      
      public static const FROM_SKILL:String = "skill";
      
      public static const CHILD_BACK_HURT:String = "backHurt";
      
      public static const CHILD_POISON:String = "poison";
      
      public static const CHILD_SPURTING:String = "spurting";
      
      public static const CHILD_CRIT:String = "crit";
      
      public static const CHILD_CURRENT:String = "current";
      
      public static const CHILD_WEAPON:String = "weapon";
      
      public static const CHILD_SELF:String = "self";
      
      public static var pro_arr:Array = [];
      
      private static const _addFlamer_ArmsSkill_link:String = "addFlamer_ArmsSkill_link";
      
      private static const _redFireDem:String = "redFireDem";
      
      private static const _silverScreenBulletHero:String = "silverScreenBulletHero";
      
      private static const _yearCattleSkill:String = "yearCattleSkill";
      
      public var hurtRatio:Number = 1;
      
      public var firstHurt:Number = 1;
      
      public var focoMul:Number = 0;
      
      public var skillAddMul:Number = 1;
      
      public var hurtMul:Number = 0;
      
      public var critExtraMul:Number = 1;
      
      public var critExtraPro:Number = 0;
      
      public var attackType:String = AttackType.DIRECT;
      
      public var skillArr:Array = null;
      
      public var critArr:Array = null;
      
      public var ele:String = "";
      
      public var eleMul:Number = 0;
      
      public var eleArr:Array = null;
      
      public var shakeValue:Number = 0;
      
      public var screenShakeValue:Number = 0;
      
      public var beatBack:Number = 0;
      
      public var hitImg:ImageUrlDefine = null;
      
      public var hitVolume:Number = 1;
      
      public var beatRa:Number = 0;
      
      public var beatX:Number = 0;
      
      public var beatY:Number = 0;
      
      public var targetHurtRect:IDRect = null;
      
      public var producter:IO_NormalBody = null;
      
      public var under:IO_NormalBody = null;
      
      public var from:String = "";
      
      public var fromChild:String = "";
      
      public var fromBullet:BulletBody = null;
      
      public var fromWeapon:WeaponData = null;
      
      public var fromSkill:SkillEffectData = null;
      
      public var fromAttack:BodyAttackDefine = null;
      
      public var addHurtMulB:Boolean = true;
      
      public var hurtNum:int = 1;
      
      private var _saberDartsBullet:String = "saberDartsBullet";
      
      public function HurtData()
      {
         super();
      }
      
      public function setFromObj(obj0:Object) : void
      {
         if(obj0 is BulletBody)
         {
            this.fromBullet = obj0 as BulletBody;
         }
         else if(obj0 is WeaponData)
         {
            this.fromWeapon = obj0 as WeaponData;
         }
         else if(obj0 is BodyAttackDefine)
         {
            this.fromAttack = obj0 as BodyAttackDefine;
         }
      }
      
      public function clone() : HurtData
      {
         var n0:String = null;
         var da0:HurtData = new HurtData();
         for each(n0 in pro_arr)
         {
            da0[n0] = this[n0];
         }
         return da0;
      }
      
      public function cloneNoEffect() : HurtData
      {
         var da0:HurtData = this.clone();
         da0.skillArr = null;
         da0.critArr = null;
         return da0;
      }
      
      public function InDataBullet_byAttack(h0:HurtData) : void
      {
         this.hurtRatio = h0.hurtRatio;
         this.hurtMul = h0.hurtMul;
         this.attackType = h0.attackType;
         this.focoMul = h0.focoMul;
         if(Boolean(h0.skillArr) && h0.skillArr.length > 0)
         {
            this.skillArr = h0.skillArr;
         }
         if(h0.hitImg is ImageUrlDefine)
         {
            if(h0.hitImg.url != "")
            {
               this.hitImg = h0.hitImg;
            }
         }
      }
      
      public function inBeat(h0:HurtData) : void
      {
         this.beatX = h0.beatX;
         this.beatY = h0.beatY;
         this.beatRa = h0.beatRa;
         this.targetHurtRect = h0.targetHurtRect;
      }
      
      public function eleSamePan(ele0:String) : Boolean
      {
         if(ele0 == this.ele)
         {
            return true;
         }
         if(Boolean(this.eleArr))
         {
            if(this.eleArr.indexOf(ele0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function addCritD(critD0:BulletCritDefine) : void
      {
         if(!(this.critArr is Array))
         {
            this.critArr = [];
         }
         this.critArr.push(critD0);
      }
      
      public function randomCritMul() : Number
      {
         var arr0:Array = null;
         var arr_len0:int = 0;
         var i:int = 0;
         var d0:BulletCritDefine = null;
         var pro0:Number = NaN;
         var mul0:Number = 1;
         if(this.critArr is Array)
         {
            arr0 = this.critArr;
            arr_len0 = int(arr0.length);
            for(i = 0; i < arr_len0; i++)
            {
               d0 = arr0[i];
               pro0 = d0.pro + this.critExtraPro;
               if(Math.random() < pro0)
               {
                  mul0 = d0.mul;
               }
            }
         }
         if(mul0 > 1)
         {
            mul0 *= this.critExtraMul;
         }
         return mul0;
      }
      
      public function getHurtPartId() : String
      {
         if(Boolean(this.targetHurtRect))
         {
            return this.targetHurtRect.id;
         }
         return "body";
      }
      
      public function getNoSkillAddHurt() : Number
      {
         if(this.skillAddMul <= 0)
         {
            this.skillAddMul = 1;
         }
         return this.hurtRatio / this.skillAddMul;
      }
      
      public function isBackHurtB(b0:IO_NormalBody) : Boolean
      {
         var rightB0:Boolean = Boolean(b0.getImg().rightB);
         if(rightB0)
         {
            return this.beatX < b0.getMot().x;
         }
         return this.beatX > b0.getMot().x;
      }
      
      public function isHitB() : Boolean
      {
         return this.from == FROM_ATTACK || this.from == FROM_BULLET;
      }
      
      public function isSkillHitB() : Boolean
      {
         return this.from == FROM_SKILL;
      }
      
      public function isWeaponB() : Boolean
      {
         if(Boolean(this.fromBullet))
         {
            if(this.fromBullet.define.name == this._saberDartsBullet)
            {
               return true;
            }
         }
         return this.fromChild == CHILD_WEAPON || Boolean(this.fromWeapon);
      }
      
      public function isHeadB() : Boolean
      {
         if(Boolean(this.targetHurtRect))
         {
            if(this.targetHurtRect.id == BodyPart.head)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getWhippB() : Boolean
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.define.whippB;
         }
         return true;
      }
      
      public function extraShakeValue() : Number
      {
         var d0:ArmsDefine = null;
         if(this.fromBullet is BulletBody)
         {
            if(this.fromBullet.define is ArmsDefine)
            {
               d0 = this.fromBullet.define as ArmsDefine;
               return ArmsType.getDieExtraShake(d0.armsType);
            }
         }
         return 0;
      }
      
      public function getBulletHitNum() : int
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.getHitNum();
         }
         return 0;
      }
      
      public function isBounceBulletB() : Boolean
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.isBounceB();
         }
         return false;
      }
      
      public function isImplodingBulletB() : Boolean
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.define.name.indexOf("imploding") >= 0;
         }
         return false;
      }
      
      public function getBloodMul(target0:IO_NormalBody) : Number
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.define.getBloodMul();
         }
         return 1;
      }
      
      public function getDieBloodMul(target0:IO_NormalBody) : Number
      {
         return 1;
      }
      
      public function canCloseCritB() : Boolean
      {
         var type0:String = null;
         if(this.fromBullet is BulletBody)
         {
            type0 = this.fromBullet.define.armsType;
            return ArmsType.canCloseCritB(type0);
         }
         return true;
      }
      
      public function isFollowBulletB() : Boolean
      {
         var xx0:int = 0;
         if(this.fromBullet is BulletBody)
         {
            if(this.fromBullet.define.followD.noLM == false)
            {
               return this.fromBullet.mot.followVra > 0;
            }
            xx0 = 0;
         }
         return false;
      }
      
      public function getArmsType() : String
      {
         var name0:String = null;
         if(Boolean(this.fromBullet))
         {
            return this.fromBullet.define.armsType;
         }
         if(Boolean(this.fromSkill))
         {
            name0 = this.fromSkill.define.name;
            if(name0 == _redFireDem)
            {
               return ArmsType.rocket;
            }
            if(name0 == _addFlamer_ArmsSkill_link)
            {
               return ArmsType.flamer;
            }
         }
         return "";
      }
      
      public function getArmsData() : ArmsData
      {
         if(Boolean(this.fromBullet))
         {
            return this.fromBullet.define as ArmsData;
         }
         return null;
      }
      
      public function ignoreSensitiveB() : Boolean
      {
         if(Boolean(this.fromBullet))
         {
            if(this.fromBullet.define.name == _silverScreenBulletHero)
            {
               return true;
            }
         }
         if(Boolean(this.fromSkill))
         {
            if(this.fromSkill.define.name == _yearCattleSkill)
            {
               return true;
            }
         }
         return false;
      }
      
      public function ignoreArmsSensitiveB() : Boolean
      {
         if(this.ignoreSensitiveB())
         {
            return true;
         }
         return false;
      }
      
      public function beforeDieEventB() : Boolean
      {
         return true;
      }
      
      public function getHurtDescrp(b1:IO_NormalBody = null) : String
      {
         var s0:String = ComMethod.numberToSmall(this.hurtRatio,"") + "——";
         return s0 + this.getHurtCn(b1);
      }
      
      public function getHurtCn(b1:IO_NormalBody = null) : String
      {
         var s0:String = null;
         if(Boolean(this.fromBullet))
         {
            s0 = this.fromBullet.define.cnName != "" ? this.fromBullet.define.cnName : this.fromBullet.define.name;
         }
         else if(Boolean(this.fromAttack))
         {
            s0 = this.fromAttack.cn != "" ? this.fromAttack.cn : this.fromAttack.imgLabel;
         }
         else if(Boolean(this.fromSkill))
         {
            s0 = this.fromSkill.define.cnName != "" ? this.fromSkill.define.cnName : this.fromSkill.define.name;
         }
         else
         {
            s0 = this.from + "(" + this.fromChild + ")";
         }
         if(Boolean(b1))
         {
            s0 += "【" + b1.getDefine().cnName + "】";
         }
         return s0;
      }
      
      public function getTransBackHurtMul() : Number
      {
         if(Boolean(this.fromBullet))
         {
            return this.fromBullet.define.transBackMul;
         }
         if(Boolean(this.fromAttack))
         {
            return this.fromAttack.transBackMul;
         }
         return 1;
      }
      
      public function setHitImgByBullet(bulletD0:BulletDefine) : void
      {
         this.hitVolume = bulletD0.getHitSoundVolume();
         this.hitImg = bulletD0.hitImg;
      }
      
      public function clearHitImg() : void
      {
         this.hitImg = ImageUrlDefine.ZERO;
      }
      
      public function showHitImg() : void
      {
         var vmul0:Number = this.hitVolume;
         if(Boolean(this.under))
         {
            if(this.under.getData().isNormalUnitB() == false)
            {
               vmul0 *= 0.5;
            }
         }
         if(Boolean(this.fromBullet))
         {
            this.fromBullet.showHitImg(this.hitImg,this.beatX,this.beatY,this.beatRa,vmul0);
         }
         else
         {
            NormalEffectAddit.addEffect(this.hitImg,this.beatX,this.beatY,this.beatRa,true,vmul0);
         }
      }
   }
}

