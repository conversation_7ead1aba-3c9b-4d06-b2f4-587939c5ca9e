package dataAll.level.define
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.level.define.endless.EndlessGradeDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitOrderDefine;
   import gameAll.level._diy.task.CustomTaskLevelDiy;
   
   public class LevelDefineGroup
   {
      
      private static var bossDefineObj:Object = {};
      
      public var obj:Object = {};
      
      private var allObj:Object = {};
      
      public var arrObj:Object = {};
      
      public var randomBossNameArr:Array = [];
      
      private var endlessArr:Array = [];
      
      public function LevelDefineGroup()
      {
         super();
      }
      
      public static function addBossDefine(d0:OneUnitOrderDefine) : void
      {
         if(!bossDefineObj.hasOwnProperty(d0.cnName))
         {
            bossDefineObj[d0.cnName] = d0;
         }
      }
      
      public static function getBossDefine(cn0:String) : OneUnitOrderDefine
      {
         return bossDefineObj[cn0];
      }
      
      public function inData_byXML(xml0:XML, normalFixedB0:Boolean = false) : void
      {
         var i:* = undefined;
         var father_xml2:XML = null;
         var father0:String = null;
         var xmllist2:XMLList = null;
         var n:* = undefined;
         var xml2:XML = null;
         var d0:LevelDefine = null;
         var father_xmllist2:XMLList = xml0.father;
         for(i in father_xmllist2)
         {
            father_xml2 = father_xmllist2[i];
            father0 = father_xml2.@name;
            xmllist2 = father_xml2.gather.level;
            for(n in xmllist2)
            {
               xml2 = xmllist2[n];
               d0 = new LevelDefine();
               d0.inData_byXML(xml2,father0);
               this.addOne(d0,father0);
            }
         }
      }
      
      public function inEndless_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var d0:EndlessGradeDefine = null;
         var xl0:XMLList = xml0.context[0].grade;
         for each(x0 in xl0)
         {
            d0 = new EndlessGradeDefine();
            d0.inData_byXML(x0);
            this.endlessArr.push(d0);
         }
      }
      
      private function addOne(d0:LevelDefine, father0:String) : void
      {
         if(!this.obj.hasOwnProperty(father0))
         {
            this.obj[father0] = {};
         }
         if(!this.arrObj.hasOwnProperty(father0))
         {
            this.arrObj[father0] = [];
         }
         this.obj[father0][d0.name] = d0;
         this.allObj[d0.name] = d0;
         this.arrObj[father0].push(d0);
      }
      
      public function init() : void
      {
         this.normalFixed();
         this.randomBoss();
      }
      
      private function normalFixed() : void
      {
         var d0:LevelDefine = null;
         var targetD0:LevelDefine = null;
         var copyD0:LevelDefine = null;
         var obj0:Object = this.obj["normal"];
         for each(d0 in obj0)
         {
            if(d0.fixed.target != "")
            {
               targetD0 = obj0[d0.fixed.target];
               copyD0 = targetD0.clone();
               d0.fixedBy(copyD0,true);
            }
         }
      }
      
      private function randomBoss() : void
      {
         var d0:LevelDefine = null;
         var arr0:Array = this.arrObj["kingTask"];
         for each(d0 in arr0)
         {
            if(d0.name != "ZombieShell")
            {
               this.randomBossNameArr.push(d0.name);
            }
         }
      }
      
      public function getDefine(name0:String, father0:String = "normal") : LevelDefine
      {
         if(this.obj.hasOwnProperty(father0))
         {
            return this.obj[father0][name0];
         }
         return null;
      }
      
      public function getDefineBy(name0:String) : LevelDefine
      {
         return this.allObj[name0];
      }
      
      public function getDefineUrl(url0:String) : LevelDefine
      {
         var f0:String = null;
         var n0:String = null;
         var index0:int = int(url0.indexOf("/"));
         if(index0 == -1)
         {
            f0 = "normal";
            n0 = url0;
         }
         else
         {
            f0 = url0.substring(0,index0);
            n0 = url0.substring(index0 + 1);
         }
         return this.getDefine(n0,f0);
      }
      
      public function getEndlessGradeDefine(n0:int) : EndlessGradeDefine
      {
         if(n0 < 1)
         {
            n0 = 1;
         }
         if(n0 > this.endlessArr.length)
         {
            n0 = int(this.endlessArr.length);
         }
         return this.endlessArr[n0 - 1];
      }
      
      public function getCloneBossUnitOrderDefine(name0:String) : UnitOrderDefine
      {
         var d0:LevelDefine = this.getDefine(name0,"kingTask");
         if(!(d0 is LevelDefine))
         {
            INIT.showError("找不到定义：LevelDefine" + name0);
         }
         var unit_d0:UnitOrderDefine = d0.unitG.getUnitOrderDefine("enemy1");
         return unit_d0.clone();
      }
      
      public function addCustomTask() : void
      {
         var d0:TaskDefine = null;
         CustomTaskLevelDiy.init();
         var arr0:Array = Gaming.defineGroup.task.getArrByType(TaskType.CUSTOM);
         for each(d0 in arr0)
         {
            this.addCustomTaskOne(d0);
         }
      }
      
      private function addCustomTaskOne(td0:TaskDefine) : void
      {
         var xx0:int = 0;
         var before0:String = null;
         var after0:String = null;
         var skillStr0:String = null;
         var mapD0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(td0.worldMapId);
         var name0:String = "cus" + mapD0.name;
         var haveD0:LevelDefine = this.getDefineBy(name0);
         if(haveD0 != null)
         {
            xx0 = 0;
            return;
         }
         var customD0:LevelDefine = Gaming.defineGroup.level.getDefineUrl("customBase");
         var baseD0:LevelDefine = Gaming.defineGroup.level.getDefineUrl(mapD0.getNormalLevelName());
         var enemyCnArr0:Array = baseD0.unitG.getEnemyCnArr();
         var bossCn0:String = "";
         var bossUD0:OneUnitOrderDefine = baseD0.unitG.getBossOne();
         if(Boolean(bossUD0))
         {
            bossCn0 = bossUD0.cnName;
         }
         else
         {
            bossCn0 = ArrayMethod.getRandomOne(enemyCnArr0) as String;
         }
         var enemy1:String = enemyCnArr0[0];
         var enemy2:String = enemy1;
         if(enemyCnArr0.length >= 2)
         {
            enemy2 = enemyCnArr0[baseD0.info.enemyLv % (enemyCnArr0.length - 1) + 1];
         }
         var xmlStr0:String = customD0.originalXml.toXMLString();
         var beforeArr0:Array = ["战斗僵尸","肥胖僵尸","僵尸王"];
         var afterArr0:Array = [enemy1,enemy2,bossCn0];
         INIT.tempTrace(mapD0.cnName + "-----" + afterArr0);
         for(var i:int = 0; i < beforeArr0.length; i++)
         {
            before0 = beforeArr0[i];
            after0 = afterArr0[i];
            xmlStr0 = xmlStr0.replace(before0,after0);
         }
         var skillArr0:Array = CustomTaskLevelDiy.getBossSkillArr(bossCn0);
         if(Boolean(skillArr0))
         {
            skillStr0 = String(skillArr0);
            skillStr0 = StringMethod.replaceStr(skillStr0,"\"","");
            skillStr0 += ",State_AddMoveValue6";
            xmlStr0 = xmlStr0.replace("State_AddMoveValue6",skillStr0);
         }
         var xml0:XML = new XML(xmlStr0);
         var d0:LevelDefine = new LevelDefine();
         d0.info.enemyLv = baseD0.info.enemyLv;
         d0.inData_byXML(xml0,"customTask");
         d0.name = name0;
         d0.sceneLabel = baseD0.sceneLabel;
         d0.fixedBy(baseD0,true);
         this.addOne(d0,"customTask");
      }
   }
}

