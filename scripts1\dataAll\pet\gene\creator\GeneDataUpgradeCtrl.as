package dataAll.pet.gene.creator
{
   import dataAll._player.base.PlayerBaseData;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.pro.PropertyArrayDefineGroup;
   
   public class GeneDataUpgradeCtrl
   {
      
      public function GeneDataUpgradeCtrl()
      {
         super();
      }
      
      public static function getAfterSave(s0:GeneSave) : GeneSave
      {
         var affter_s0:GeneSave = null;
         var addLv0:int = getAddLevel(s0);
         affter_s0 = s0.clone();
         affter_s0.addLevel += addLv0;
         var d0:GeneDefine = affter_s0.getDefine();
         var obj0:Object = s0.obj;
         if(d0.superB)
         {
            affter_s0.obj = getSuperObj(obj0,s0.getGeneDropLevel() + addLv0);
         }
         else
         {
            affter_s0.obj = getUpgradeObj(obj0,s0.getGeneDropLevel(),addLv0);
         }
         return affter_s0;
      }
      
      private static function getAddLevel(s0:GeneSave) : int
      {
         var affter_lv0:int = s0.getGeneDropLevel() + 5;
         if(affter_lv0 > PlayerBaseData.MAX_LEVEL)
         {
            affter_lv0 = PlayerBaseData.MAX_LEVEL;
         }
         var addLv0:int = affter_lv0 - s0.getGeneDropLevel();
         if(addLv0 == 0)
         {
            addLv0 = 5;
         }
         return addLv0;
      }
      
      private static function getUpgradeObj(obj0:Object, dropLv0:int, addLv0:int) : Object
      {
         var n:* = undefined;
         var v2:Number = NaN;
         var obj2:Object = {};
         for(n in obj0)
         {
            v2 = countUpgradePro(obj0[n],n,dropLv0,addLv0);
            obj2[n] = v2;
         }
         return obj2;
      }
      
      private static function getSuperObj(obj0:Object, lv0:int) : Object
      {
         var n:* = undefined;
         var max0:Number = NaN;
         var dg0:PropertyArrayDefineGroup = Gaming.defineGroup.gene.pro;
         var obj2:Object = {};
         for(n in obj0)
         {
            max0 = dg0.getPropertyValue(n,lv0);
            obj2[n] = max0;
         }
         return obj2;
      }
      
      private static function countUpgradePro(v0:Number, proName0:String, dropLv0:int, addLv0:int) : Number
      {
         var dg0:PropertyArrayDefineGroup = Gaming.defineGroup.gene.pro;
         var d0:PropertyArrayDefine = dg0.getDefine(proName0);
         var cLv0:int = 5;
         var v2:Number = 0;
         var minLv1:int = int((dropLv0 - 1) / cLv0) * cLv0;
         var min1:Number = dg0.getPropertyValue(proName0,minLv1 + cLv0);
         var max1:Number = dg0.getPropertyValue(proName0,minLv1 + cLv0 * 2);
         v2 = v0 + (max1 - min1);
         if(v2 > max1)
         {
            v2 = max1;
         }
         return v2;
      }
      
      public static function getMust(s0:GeneSave) : MustDefine
      {
         var dropLv0:int = s0.getGeneDropLevel();
         var addLv0:int = getAddLevel(s0);
         var colorMul0:Number = getMustColorMul(s0.color);
         var d0:MustDefine = new MustDefine();
         d0.lv = dropLv0 + addLv0;
         d0.coin = Math.ceil(Gaming.defineGroup.normal.getPlayerCoinIncome(d0.lv) / 5 * colorMul0);
         var num0:int = Math.ceil(getConverStone(dropLv0) * colorMul0);
         d0.inThingsDataByArr(["converStone;" + num0]);
         return d0;
      }
      
      private static function getConverStone(dropLv0:int) : int
      {
         var num0:Number = 0;
         for(var i:int = dropLv0 + 1; i <= dropLv0 + 5; i++)
         {
            num0 += Gaming.defineGroup.normal.getPropertyValue("skillStoneDrop",dropLv0) / 10;
         }
         return Math.ceil(num0);
      }
      
      private static function getMustColorMul(color0:String) : Number
      {
         if(color0 == "red")
         {
            return 1.5;
         }
         if(color0 == "orange")
         {
            return 1;
         }
         if(color0 == "purple")
         {
            return 0.85;
         }
         if(color0 == "blue")
         {
            return 0.7;
         }
         return 0.5;
      }
      
      public static function upgradeOne(da0:PetData) : void
      {
         var s0:GeneSave = da0.gene.save;
         var affter_s0:GeneSave = getAfterSave(s0);
         s0.obj = affter_s0.obj;
         s0.addLevel = affter_s0.getTrueLevel() - s0.itemsLevel;
         da0.fleshProData();
      }
   }
}

