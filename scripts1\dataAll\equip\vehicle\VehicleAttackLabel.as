package dataAll.equip.vehicle
{
   import dataAll.arms.define.ArmsType;
   
   public class VehicleAttackLabel
   {
      
      public static const MAIN:String = "main";
      
      public static const SUB:String = "sub";
      
      public static const ATTACK:String = "attack";
      
      public static const arr:Array = [MAIN,SUB,ATTACK];
      
      public static const normalArr:Array = [ATTACK];
      
      public static const shootArr:Array = arr;
      
      public static const dpsTargetObj:Object = {
         "main":ArmsType.rocket,
         "sub":ArmsType.rifle,
         "attack":ArmsType.sniper
      };
      
      private static const cnObj:Object = {
         "main":"主炮",
         "sub":"机枪",
         "attack":"普通攻击"
      };
      
      public function VehicleAttackLabel()
      {
         super();
      }
      
      public static function getCn(type0:String) : String
      {
         return cnObj[type0];
      }
   }
}

