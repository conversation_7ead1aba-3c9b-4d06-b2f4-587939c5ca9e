package com.sounto.cf
{
   public class FixedEncryp
   {
      
      private var a:Number = Math.random();
      
      private var fixed:int = 0;
      
      public function FixedEncryp()
      {
         super();
      }
      
      public function setFixed(f0:int) : void
      {
         this.fixed = f0;
      }
      
      public function encode(v0:Number) : Number
      {
         return v0 / this.a;
      }
      
      public function decode(v0:Number) : Number
      {
         var num0:Number = v0 * this.a;
         var ten0:Number = Math.pow(10,this.fixed);
         var p0:Number = Math.round(num0 * ten0);
         var last0:Number = p0 / ten0;
         var str0:String = last0.toFixed(this.fixed);
         return Number(str0);
      }
   }
}

