package dataAll.arms.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.arms.ArmsData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.ui.tip.CheckData;
   
   public class ArmsStrengthenCtrl
   {
      
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function ArmsStrengthenCtrl()
      {
         super();
      }
      
      public static function get maxAddLevel() : Number
      {
         return CF.getAttribute("maxAddLevel");
      }
      
      public static function set maxAddLevel(v0:Number) : void
      {
         CF.setAttribute("maxAddLevel",v0);
      }
      
      public static function init() : void
      {
         maxAddLevel = 27;
      }
      
      public static function getAfterData(da0:ArmsData) : ArmsData
      {
         var affter_s0:ArmsSave = da0.save.copy();
         ++affter_s0.strengthenLv;
         var affter_da0:ArmsData = new ArmsData();
         affter_da0.inData_bySave(affter_s0,da0.normalPlayerData,true);
         affter_da0.fleshData_byMeEquip();
         return affter_da0;
      }
      
      public static function getStateStr(da0:ArmsData, cData0:ArmsData, beforeB0:Boolean = true) : String
      {
         var s0:ArmsSave = da0.save;
         var lv0:int = s0.strengthenLv;
         var max0:int = maxAddLevel;
         var str0:String = "";
         if(lv0 > max0)
         {
            str0 += ComMethod.color("<b>已强化至最高等级</b>","#00FF00");
         }
         else
         {
            str0 += "<green <b>" + "强化等级" + lv0 + "级</b>/>";
            str0 += "\n单发伤害：<yellow " + da0.hurtRatio + "/>";
         }
         return str0;
      }
      
      public static function strengthenOne(da0:ArmsData, oneB0:Boolean) : void
      {
         var s0:ArmsSave = da0.save;
         if(oneB0)
         {
            ++s0.strengthenLv;
         }
         else
         {
            s0.strengthenLv = getStrengthenLv(s0.strengthenLv);
         }
         s0.fleshSMaxLv();
         da0.fleshData_byMeEquip();
      }
      
      public static function getStrengthenLv(nowLv0:int, max0:int = -1) : int
      {
         if(max0 == -1)
         {
            max0 = maxAddLevel;
         }
         var ran0:Number = Math.random();
         if(ran0 <= 0.5)
         {
            nowLv0 += 27;
         }
         else if(ran0 <= 0.8)
         {
            nowLv0 += 27;
         }
         else
         {
            nowLv0 += 27;
         }
         if(nowLv0 > max0)
         {
            nowLv0 = max0;
         }
         return nowLv0;
      }
      
      public static function downStrengthenOne(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var lv0:int = s0.strengthenLv;
         lv0--;
         if(lv0 < 0)
         {
            lv0 = 0;
         }
         s0.strengthenLv = lv0;
         da0.fleshData_byMeEquip();
      }
      
      public static function getSuccessRate(lv0:int) : Number
      {
         return Gaming.defineGroup.equip.strengthen.getPropertyValue("successRate",lv0 + 1);
      }
      
      public static function panStrengthenB(da0:ArmsData) : CheckData
      {
         var c0:CheckData = new CheckData();
         if(da0.save.getTrueLevel() < 50)
         {
            c0.bb = false;
            c0.info = "<red <b>武器等级必须到达50级。</b>/>";
         }
         else if(da0.save.strengthenLv >= maxAddLevel)
         {
            c0.bb = false;
            c0.info = "<green <b>武器已经强化至最高等级。</b>/>";
         }
         return c0;
      }
      
      public static function canStrengthenB(da0:ArmsData) : Boolean
      {
         var c0:CheckData = panStrengthenB(da0);
         return c0.bb;
      }
      
      public static function canStrengthenMoveB(da0:ArmsData) : Boolean
      {
         if(da0.save.getTrueLevel() < 50)
         {
            return false;
         }
         return true;
      }
      
      public static function getHurtMul(strengthenLv0:int) : Number
      {
         if(strengthenLv0 < 1)
         {
            return 0;
         }
         return Gaming.defineGroup.equip.strengthen.getPropertyValue("addMul",strengthenLv0) * 5;
      }
      
      public static function test() : void
      {
      }
   }
}

