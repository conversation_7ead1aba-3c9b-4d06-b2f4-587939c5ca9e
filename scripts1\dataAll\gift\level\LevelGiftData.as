package dataAll.gift.level
{
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class LevelGiftData
   {
      
      public var name:String = "";
      
      public var define:GiftAddDefineGroup;
      
      public var openB:Boolean = false;
      
      public var getGiftB:Boolean = false;
      
      public var mapStr:String = "";
      
      public function LevelGiftData()
      {
         super();
      }
      
      public function haveInfoB() : Boolean
      {
         return this.define.info != "";
      }
      
      public function getShowB() : Boolean
      {
         return this.getGiftB == false || this.haveInfoB();
      }
   }
}

