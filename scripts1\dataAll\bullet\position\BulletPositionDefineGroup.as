package dataAll.bullet.position
{
   import dataAll.bullet.BulletDefine;
   
   public class BulletPositionDefineGroup
   {
      
      private var arr:Array = [];
      
      private var d:BulletDefine = null;
      
      private var specialType:String = "";
      
      private var tempD0:BulletPositionDefine = new BulletPositionDefine();
      
      public function BulletPositionDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, d0:BulletDefine) : void
      {
         var n:* = undefined;
         var pd0:BulletPositionDefine = null;
         this.d = d0;
         if(!xml0)
         {
            return;
         }
         this.specialType = xml0.@specialType;
         if(this.specialType != "")
         {
            if(this.hasOwnProperty("in_" + this.specialType))
            {
               this["in_" + this.specialType]();
            }
         }
         else if(xml0.point.length() > 0)
         {
            for(n in xml0.point)
            {
               pd0 = new BulletPositionDefine();
               pd0.inData_byXML_BulletDefine(xml0.point[n],d0);
               this.arr.push(pd0);
            }
         }
      }
      
      public function getPosition(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         var len0:int = 0;
         if(this.specialType != "" && this[this.specialType] is Function)
         {
            return this[this.specialType](shootNum0,x0,y0,scaleX0);
         }
         len0 = int(this.arr.length);
         return this.arr[(shootNum0 - 1) % len0];
      }
      
      private function vertical(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         if(shootNum0 == 1)
         {
            shootNum0 = 2;
         }
         var max0:int = this.d.shootNum;
         this.tempD0.shootPoint.x = this.d.shootPoint.x;
         this.tempD0.shootPoint.y = this.d.shootPoint.y + 15 * (2 * shootNum0 / (max0 - 1) - 1);
         this.tempD0.bulletAngle = this.d.bulletAngle;
         return this.tempD0;
      }
      
      private function circle_inward(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         var min0:Number = -Math.PI / 6;
         var max0:Number = -Math.PI / 6 * 5;
         var r0:Number = 900;
         var ra0:Number = min0 + Math.random() * (max0 - min0);
         this.tempD0.shootPoint.x = Math.cos(ra0) * r0;
         this.tempD0.shootPoint.y = Math.sin(ra0) * r0;
         this.tempD0.bulletAngle = ra0 * 180 / Math.PI + 180;
         return this.tempD0;
      }
      
      private function knife_skeleton(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         var n:int = int(shootNum0 / 2) * (shootNum0 % 2 == 0 ? 1 : -1);
         var x1:int = n * 50 + x0;
         var y1:int = Gaming.sceneGroup.map.anyShap.getMinY(x1,y0);
         this.tempD0.shootPoint.x = x1 - x0;
         this.tempD0.shootPoint.y = y1 - y0;
         return this.tempD0;
      }
      
      private function TyphoonWitch_bat(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         this.tempD0.inData_byObj(this.d);
         var x1:int = (0.5 - Math.random()) * 70;
         var y1:int = (0.5 - Math.random()) * 200;
         this.tempD0.shootPoint.x += x1;
         this.tempD0.shootPoint.y += y1;
         return this.tempD0;
      }
      
      private function summonWolf_FightWolf(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         this.tempD0.inData_byObj(this.d);
         var x1:int = 350 + Math.random() * 200;
         var y1:int = -250 + Math.random() * 450;
         this.tempD0.shootPoint.x += x1;
         this.tempD0.shootPoint.y += y1;
         return this.tempD0;
      }
      
      private function anger_FightWolf(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         var n:int = int(shootNum0 / 2) * (shootNum0 % 2 == 0 ? 1 : -1);
         var ran0:Number = this.d.bulletWidth - 10 + 40 * Math.random();
         var x1:int = -n * ran0 + x0;
         var y1:int = Gaming.sceneGroup.map.anyShap.getMinY(-n * scaleX0 * ran0 + x0,y0 - Math.random() * 250 + 20);
         this.tempD0.shootPoint.x = x1 - x0;
         this.tempD0.shootPoint.y = y1 - y0;
         return this.tempD0;
      }
      
      private function minersShakeBullet(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         var n:int = int(shootNum0 / 2) * (shootNum0 % 2 == 0 ? 1 : -1);
         var insideX0:Number = -n * (this.d.bulletWidth + 20) + 20 * Math.random();
         var x1:int = insideX0 + x0;
         var y1:int = Gaming.sceneGroup.map.anyShap.getMinY(insideX0 * scaleX0 + x0,y0 - Math.random() * 250 + 20);
         this.tempD0.shootPoint.x = x1 - x0;
         this.tempD0.shootPoint.y = y1 - y0;
         return this.tempD0;
      }
      
      private function PetBoomSkullS_machine(shootNum0:int, x0:int, y0:int, scaleX0:int) : BulletPositionDefine
      {
         var start0:Number = 180 / 180 * Math.PI;
         var end0:Number = (-180 - 7.5) / 180 * Math.PI;
         var ra0:Number = (end0 - start0) / this.d.shootNum * shootNum0 + start0;
         var len0:int = 156;
         var x1:Number = Math.cos(ra0) * len0;
         var y1:Number = Math.sin(ra0) * len0;
         this.tempD0.shootPoint.x = x1 + this.d.shootPoint.x;
         this.tempD0.shootPoint.y = y1 + this.d.shootPoint.y;
         this.tempD0.bulletAngle = ra0 * 180 / Math.PI;
         this.tempD0.shootAngle = this.d.shootAngle;
         return this.tempD0;
      }
   }
}

