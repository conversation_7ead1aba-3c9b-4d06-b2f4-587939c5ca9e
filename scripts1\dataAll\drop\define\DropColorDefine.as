package dataAll.drop.define
{
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   
   public class DropColorDefine
   {
      
      public var name:Array = [];
      
      public var normalPro:Array = [];
      
      public var superPro:Array = [];
      
      public var bossPro:Array = [];
      
      public var itemsLvRange:Array = [];
      
      public const MAX_LEVEL:int = 99;
      
      public function DropColorDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         var arr0:Array = null;
         var pro_arr0:Array = ["normalPro","superPro","bossPro"];
         for(n in pro_arr0)
         {
            pro0 = pro_arr0[n];
            arr0 = ComMethod.stringToNumberArr(String(xml0[pro0]));
            this.inProArray(pro0,arr0);
            if(this[pro0].length < this.name.length)
            {
               INIT.showError(pro0 + "的长度" + this[pro0].length + "<" + this.name.length);
            }
         }
         this.name = ComMethod.stringToStringArr(String(xml0.name));
         this.itemsLvRange = ComMethod.stringToRangeArr(String(xml0.itemsLvRange));
      }
      
      private function inProArray(pro0:String, arr0:Array) : void
      {
         var v0:Number = NaN;
         var str0:String = null;
         var arr2:Array = [];
         for each(v0 in arr0)
         {
            str0 = Base64.encodeString(String(v0));
            str0 = TextWay.toCode32(str0);
            arr2.push(str0);
         }
         this[pro0] = arr2;
      }
      
      public function getProArray(type0:String) : Array
      {
         var str0:String = null;
         var arr0:Array = this[type0 + "Pro"];
         var arr2:Array = [];
         var newArr0:Array = [];
         for each(str0 in arr0)
         {
            newArr0.push(str0);
            str0 = TextWay.getText32(str0);
            arr2.push(Number(Base64.decodeString(str0)));
         }
         this[type0 + "Pro"] = newArr0;
         return arr2;
      }
      
      public function getOneDrop(type0:String, bodyLv0:int = 0, dropMul0:Number = 0) : String
      {
         var proArr0:Array = this.getProArray(type0).concat([]);
         var arr0:Array = [];
         if(!proArr0)
         {
            INIT.showError("不存在这个类型的数据：" + type0);
         }
         if(bodyLv0 < 50)
         {
            proArr0[4] *= 2;
         }
         if(bodyLv0 < 60)
         {
            proArr0[5] *= 1 + dropMul0;
         }
         proArr0[4] *= 1 + dropMul0;
         var index0:int = ComMethod.getPro_byArrSum(proArr0);
         return this.name[index0];
      }
   }
}

