package dataAll._app.food
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._base.NormalDefineGroup;
   
   public class FoodDefineGroup
   {
      
      public const raw:NormalDefineGroup = new NormalDefineGroup();
      
      public const book:NormalDefineGroup = new NormalDefineGroup();
      
      public function FoodDefineGroup()
      {
         super();
         this.raw.defineClass = FoodRawDefine;
         this.raw.haveCnB = true;
         this.book.defineClass = FoodBookDefine;
      }
      
      public function addDropDefine() : void
      {
         var d0:FoodRawDefine = null;
         for each(d0 in this.raw.arr)
         {
            Gaming.defineGroup.dropItems.inFoodRawDefine(d0);
         }
      }
      
      public function getRandomRawDrop() : FoodRawDefine
      {
         var d0:FoodRawDefine = null;
         var f0:int = 0;
         var proArr0:Array = [];
         for each(d0 in this.raw.arr)
         {
            proArr0.push(d0.dropPro);
         }
         f0 = ArrayMethod.getPro_byArrSum(proArr0);
         return this.raw.arr[f0];
      }
   }
}

