package dataAll._app.achieve
{
   import com.sounto.utils.ClassProperty;
   
   public class AchieveSaveGroup
   {
      
      public static var pro_arr:Array = [];
      
      public var obj:Object = {};
      
      public var onlyNoCompleteB:Boolean = false;
      
      public function AchieveSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],AchieveSave);
      }
      
      public function changeOnlyNoComplete() : Boolean
      {
         this.onlyNoCompleteB = !this.onlyNoCompleteB;
         return this.onlyNoCompleteB;
      }
   }
}

