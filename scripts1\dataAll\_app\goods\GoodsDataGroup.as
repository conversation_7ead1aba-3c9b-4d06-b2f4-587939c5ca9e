package dataAll._app.goods
{
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.PlayerData;
   
   public class GoodsDataGroup
   {
      
      private var fatherObj:Object = {};
      
      public var playerData:PlayerData;
      
      public var save:GoodsSaveGroup;
      
      private var obj:Object = {};
      
      public function GoodsDataGroup()
      {
         super();
      }
      
      public function initData() : void
      {
         var n:* = undefined;
         var arr0:Array = null;
         var i:* = undefined;
         var d0:GoodsDefine = null;
         var da0:GoodsData = null;
         var obj0:Object = Gaming.defineGroup.goods.fatherObj;
         for(n in obj0)
         {
            arr0 = [];
            for(i in obj0[n])
            {
               d0 = obj0[n][i];
               da0 = new GoodsData();
               da0.def = d0;
               da0.playerData = this.playerData;
               arr0.push(da0);
               this.obj[d0.name] = da0;
            }
            this.fatherObj[n] = arr0;
         }
      }
      
      public function inData_bySave(save0:GoodsSaveGroup) : void
      {
         this.save = save0;
      }
      
      public function newDayCtrl() : void
      {
         this.save.newDayCtrl();
      }
      
      public function getData(name0:String) : GoodsData
      {
         return this.obj[name0];
      }
      
      public function getDataArr(type0:String, secLabel0:String) : Array
      {
         var da0:GoodsData = null;
         var arr0:Array = this.fatherObj[type0];
         var newArr0:Array = [];
         for each(da0 in arr0)
         {
            if(da0.getShowB())
            {
               if(da0.def.type == secLabel0 || secLabel0 == "all" || da0.def.otherArr.indexOf(secLabel0) >= 0)
               {
                  newArr0.push(da0);
               }
            }
         }
         return newArr0;
      }
      
      public function getDataArrByNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var da0:GoodsData = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            da0 = this.obj[name0];
            arr0.push(da0);
         }
         return arr0;
      }
      
      public function addBuyNum(name0:String, num0:int) : void
      {
         this.save.addDayBuyNum(name0,num0);
         this.save.addAllBuyNum(name0,num0);
      }
      
      public function getBuyNum(name0:String) : int
      {
         return this.save.getDayBuyNum(name0);
      }
      
      public function getBuyLimitNum(name0:String) : int
      {
         var add0:int = 0;
         var buy0:int = 0;
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine(name0);
         var limit0:int = d0.dayBuyLimitNum;
         if(limit0 == 0)
         {
            return GoodsData.MAX_NUM;
         }
         add0 = this.playerData.vip.def.getBuyLimitNum(name0);
         buy0 = this.getBuyNum(name0);
         return limit0 + add0 - buy0;
      }
      
      public function getBuyLimitAll(name0:String) : int
      {
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine(name0);
         var limit0:int = d0.buyLimitNum;
         if(limit0 == 0)
         {
            return GoodsData.MAX_NUM;
         }
         return limit0 - this.save.getAllBuyNum(name0);
      }
   }
}

