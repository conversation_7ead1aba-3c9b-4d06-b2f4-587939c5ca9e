package dataAll.body.define
{
   import com.sounto.utils.ClassProperty;
   
   public class BodyDropDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var rareSuit:String = "";
      
      public var extraRareArmsArr:Array = [];
      
      public var blackArmsBodyB:Boolean = false;
      
      public function BodyDropDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

