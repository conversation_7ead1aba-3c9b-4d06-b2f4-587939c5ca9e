package dataAll.level.define.drop
{
   import com.sounto.utils.ClassProperty;
   
   public class LevelDropDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var exp:Number = 1;
      
      public var coin:Number = 1;
      
      public var life:Number = 1;
      
      public var charger:Number = 1;
      
      public var equip:Number = 1;
      
      public var arms:Number = 1;
      
      public var skillStone:Number = 1;
      
      public var taxStamp:Number = 1;
      
      public var noB:Boolean = false;
      
      public function LevelDropDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getDropPro(type0:String) : Number
      {
         if(this.hasOwnProperty(type0))
         {
            return this[type0];
         }
         return 1;
      }
   }
}

