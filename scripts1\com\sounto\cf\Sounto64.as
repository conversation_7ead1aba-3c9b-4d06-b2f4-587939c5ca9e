package com.sounto.cf
{
   public class Sounto64
   {
      
      private static var keyObj:Object = {};
      
      private static var codeObj:Object = {};
      
      private static var codeArr:Array = [];
      
      protected static const strArrayString:String = "0123456789.-";
      
      protected static var useString:String = "012345678abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ`~!@#$%^&*()-_=+[]{},.?;:";
      
      private static var useString2:String = useString;
      
      public function Sounto64()
      {
         super();
      }
      
      public static function staticInit() : int
      {
         var s0:String = null;
         var len0:int = strArrayString.length;
         var keynum0:int = useString.length / strArrayString.length;
         for(var i:int = 0; i < len0; i++)
         {
            s0 = strArrayString.charAt(i);
            codeObj[s0] = [];
            randomMore(s0,keynum0);
         }
         var ko:* = keyObj;
         var co:* = codeObj;
         return 0;
      }
      
      private static function randomMore(str0:String, num0:int) : void
      {
         var s2:String = null;
         var num2:int = 0;
         while(num2 < num0)
         {
            s2 = randomOne(str0);
            if(keyObj[s2] == null)
            {
               num2++;
               keyObj[s2] = str0;
               codeObj[str0].push(s2);
            }
         }
      }
      
      private static function randomOne(str0:String) : String
      {
         var index0:int = 0;
         var s2:String = null;
         var len0:int = useString2.length;
         var s0:String = "";
         for(var i:int = 0; i < 1; i++)
         {
            index0 = int(Math.random() * len0);
            s2 = useString2.charAt(index0);
            s0 += s2;
            useString2 = useString2.replace(s2,"");
         }
         return s0;
      }
      
      public static function encode(str0:String) : String
      {
         var arr0:Array = null;
         var c0:String = null;
         if(!str0)
         {
            str0 = "0";
         }
         if(str0 == "")
         {
            str0 = "0";
         }
         var num0:Number = Number(str0);
         if(isNaN(num0))
         {
            str0 = "0";
         }
         var len0:int = str0.length;
         var s0:String = "";
         for(var i:int = len0 - 1; i >= 0; i--)
         {
            arr0 = codeObj[str0.charAt(i)];
            c0 = arr0[int(Math.random() * arr0.length)];
            s0 += c0;
         }
         return s0;
      }
      
      public static function decode(str0:String) : String
      {
         var s2:String = null;
         var s3:String = null;
         if(!str0)
         {
            return "0";
         }
         if(str0 == "")
         {
            return "0";
         }
         var len0:int = str0.length;
         var s0:String = "";
         for(var i:int = len0 - 1; i >= 0; i--)
         {
            s2 = str0.charAt(i);
            s3 = keyObj[s2];
            s0 += s3;
         }
         return s0;
      }
   }
}

