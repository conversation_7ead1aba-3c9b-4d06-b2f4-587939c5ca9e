package dataAll.gift.guoQing
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.StringCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class GuoQingSave
   {
      
      public static var pro_arr:Array = null;
      
      public static var GIFT_NAME:String = "guoQingSign";
      
      private static var codeCF:StringCF = new StringCF();
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var dailyB:Boolean = false;
      
      public var getObj:Object = {};
      
      public var eggObj:Object = {};
      
      public function GuoQingSave()
      {
         super();
         signStart = "2024-9-30";
         signEnd = "2024-10-7";
      }
      
      public static function get signStart() : String
      {
         return codeCF.getAttribute("signStart") as String;
      }
      
      public static function set signStart(str0:String) : void
      {
         codeCF.setAttribute("signStart",str0);
      }
      
      public static function get signEnd() : String
      {
         return codeCF.getAttribute("signEnd") as String;
      }
      
      public static function set signEnd(str0:String) : void
      {
         codeCF.setAttribute("signEnd",str0);
      }
      
      public static function getSignTimeStr() : String
      {
         return "活动时间：" + signStart + " ~ " + signEnd;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function get gn() : Number
      {
         return this.CF.getAttribute("gn");
      }
      
      public function set gn(v0:Number) : void
      {
         this.CF.setAttribute("gn",v0);
      }
      
      public function get en() : Number
      {
         return this.CF.getAttribute("en");
      }
      
      public function set en(v0:Number) : void
      {
         this.CF.setAttribute("en",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.getObj = ClassProperty.copyObj(obj0["getObj"]);
         this.eggObj = ClassProperty.copyObj(obj0["eggObj"]);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.dailyB = false;
         this.eggObj = {};
      }
      
      public function getGiftGetB(dayStr0:String) : Boolean
      {
         return this.getObj[dayStr0];
      }
      
      public function setGiftGetB(dayStr0:String, bb0:Boolean) : void
      {
         this.getObj[dayStr0] = bb0;
      }
      
      public function daily() : void
      {
         if(!this.dailyB)
         {
            this.dailyB = true;
            ++this.num;
         }
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getGiftArr();
         for each(g0 in gArr0)
         {
            gift0.merge(g0);
         }
         return gift0;
      }
      
      private function getGiftArr() : Array
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var giftId0:String = null;
         var getB0:Boolean = false;
         var canB0:Boolean = false;
         var arr0:Array = [];
         var gArr0:Array = this.getDefineGiftArr();
         var anum0:int = this.num;
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            giftId0 = String(must0);
            getB0 = this.getGiftGetB(giftId0);
            canB0 = anum0 >= must0;
            if(!getB0 && canB0)
            {
               arr0.push(g0);
            }
         }
         return arr0;
      }
      
      public function getDefineGiftArr() : Array
      {
         return Gaming.defineGroup.gift.getArrByFather(GIFT_NAME);
      }
      
      public function getGiftEvent() : void
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var giftId0:String = null;
         var canB0:Boolean = false;
         var gArr0:Array = this.getDefineGiftArr();
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            giftId0 = String(must0);
            canB0 = this.num >= must0;
            if(canB0)
            {
               this.setGiftGetB(giftId0,true);
               ++this.gn;
            }
         }
      }
      
      public function getGettedGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var giftId0:String = null;
         var getB0:Boolean = false;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getDefineGiftArr();
         for each(g0 in gArr0)
         {
            giftId0 = String(g0.mustLevel);
            getB0 = this.getGiftGetB(giftId0);
            if(getB0)
            {
               gift0.merge(g0);
            }
         }
         return gift0;
      }
      
      public function getGiftBtnTip() : String
      {
         var g0:GiftAddDefineGroup = null;
         var str0:String = "";
         var gArr0:Array = this.getGiftArr();
         if(gArr0.length == 0)
         {
            str0 = "礼包已领取";
         }
         else
         {
            for each(g0 in gArr0)
            {
               str0 += "\n";
               if(g0.cnName == "")
               {
                  str0 += "<blue " + g0.mustLevel + "天礼包/>";
               }
               else
               {
                  str0 += "<blue " + g0.cnName + "礼包/>";
               }
            }
            str0 = "包含：" + str0;
         }
         return str0;
      }
      
      public function getFillNum(timeStr0:String) : int
      {
         var day0:int = 0;
         var v0:int = 0;
         var maxMust0:int = 0;
         if(this.num >= 8)
         {
            return 0;
         }
         day0 = StringDate.compareDateByStr(signStart,timeStr0) + 1;
         if(!this.dailyB)
         {
            day0 -= 1;
         }
         v0 = day0 - this.num;
         maxMust0 = 30 - this.num;
         if(v0 > maxMust0)
         {
            v0 = maxMust0;
         }
         return v0;
      }
      
      public function fill(num0:int) : void
      {
         this.num += num0;
      }
      
      public function openEggGift() : GiftAddDefine
      {
         var g0:GiftAddDefineGroup = this.getEggGiftAll();
         var d0:GiftAddDefine = g0.getRandomDefine(null);
         ++this.en;
         return d0;
      }
      
      public function getEggGiftAll() : GiftAddDefineGroup
      {
         return Gaming.defineGroup.gift.getOne("guoQingEgg");
      }
      
      public function getEggSurplus(active0:int) : int
      {
         return this.getEggAll(active0) - this.getEggUse();
      }
      
      public function getEggAll(active0:int) : int
      {
         var v0:int = active0 / 20;
         if(v0 > 7)
         {
            v0 = 7;
         }
         return v0 + 1;
      }
      
      public function getEggUse() : int
      {
         var bb0:Boolean = false;
         var num0:int = 0;
         for each(bb0 in this.eggObj)
         {
            if(bb0)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getEggB(index0:int) : Boolean
      {
         return this.eggObj[String(index0)];
      }
      
      public function setEggB(index0:int, bb0:Boolean) : void
      {
         this.eggObj[String(index0)] = bb0;
      }
   }
}

