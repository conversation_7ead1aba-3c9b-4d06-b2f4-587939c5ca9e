package dataAll._app.edit.card
{
   import com.adobe.crypto.MD5;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.edit.boss.BossEditPro;
   import dataAll.body.define.BodyFather;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.pro.dataList.DataListDefine;
   import dataAll.skill.define.SkillDefine;
   
   public class BossCardCreator
   {
      
      private static const highRanArr:Array = [0,0,0,0,0.77,0.2,0.03];
      
      private static const ranArr:Array = [0.4,0.36,0.2,0.03,0.0078,0.002,0.0002];
      
      private static const ranArr2000:Array = [0,0,0.2,0.03,0.0078,0.002,0.0002];
      
      private static const rareArr:Array = [0,0,0,0.3,0.8,0.8,0.8];
      
      private static const dpsArr:Array = [[1,3],[3,9],[5,10],[10,20],[20,30],[30,35],[35,40]];
      
      private static const lifeArr:Array = [[10,30],[30,180],[70,200],[200,500],[500,1000],[1000,1200],[1200,1500]];
      
      private static const skillArr:Array = [4,5,6,6,6,6,6];
      
      private static const rareSkillArr:Array = [0,0,0,1,2,2,3];
      
      private static const allArr:Array = [ranArr,ranArr2000,rareArr,dpsArr,lifeArr,skillArr,rareSkillArr];
      
      private static const allCode:String = "962657b3770328757ef8b20c1e80145f";
      
      private static const sO:Object = {};
      
      private static const idSO:Object = {};
      
      private static const idCnSO:Object = {};
      
      private static const sA:Array = [];
      
      private static const sRareA:Array = [];
      
      private static const sRareIdObj:Object = {};
      
      private static const bossNameArr:Array = [];
      
      private static const rareBossNameArr:Array = ["Sentry"];
      
      public static const niuBossNameArr:Array = ["Madboss","Watchdog","Salamander","IronDog"];
      
      public function BossCardCreator()
      {
         super();
      }
      
      public static function init() : void
      {
         inSkillListBy("bcardSkill",false);
         inSkillListBy("bcardSkillRare",true);
         inBossArr();
      }
      
      public static function panCodeB() : Boolean
      {
         var c0:String = MD5.hash(String(allArr));
         if(c0 == allCode)
         {
            return true;
         }
         return false;
      }
      
      private static function inSkillListBy(listName0:String, rareB0:Boolean) : void
      {
         var str0:String = null;
         var sO1:* = undefined;
         var idSO1:* = undefined;
         var idCnSO1:* = undefined;
         var sRareA0:* = undefined;
         var sA0:* = undefined;
         var f0:int = 0;
         var id0:String = null;
         var name0:String = null;
         var d0:SkillDefine = null;
         var dataList0:DataListDefine = Gaming.defineGroup.dataList.getDefine(listName0);
         var strArr0:Array = dataList0.getTrueValueArr();
         for each(str0 in strArr0)
         {
            if(str0 != "")
            {
               f0 = int(str0.indexOf(":"));
               id0 = str0.substring(0,f0);
               name0 = str0.substring(f0 + 1);
               if(id0 != "" && name0 != "")
               {
                  d0 = Gaming.defineGroup.skill.getDefine(name0);
                  if(Boolean(d0))
                  {
                     sO[id0] = name0;
                     idSO[name0] = id0;
                     idCnSO[d0.cnName] = id0;
                     if(rareB0)
                     {
                        sRareA.push(id0);
                        sRareIdObj[name0] = id0;
                     }
                     else
                     {
                        sA.push(id0);
                     }
                  }
                  else
                  {
                     INIT.showError("bcardSkill不存在技能：" + name0);
                  }
               }
               else
               {
                  INIT.showError("bcardSkill不存在技能和id：" + str0);
               }
            }
         }
         sO1 = sO;
         idSO1 = idSO;
         idCnSO1 = idCnSO;
         sRareA0 = sRareA;
         sA0 = sA;
      }
      
      private static function inBossArr() : void
      {
         var father0:String = null;
         var bossNameArr0:* = undefined;
         var rareBossNameArr0:* = undefined;
         var arr0:Array = null;
         var d0:NormalBodyDefine = null;
         var name0:String = null;
         var fatherArr0:Array = BodyFather.sumArr;
         for each(father0 in fatherArr0)
         {
            arr0 = Gaming.defineGroup.body.getArrByFather(father0);
            for each(d0 in arr0)
            {
               if(d0.showLevel < 9999 && d0.canBossB)
               {
                  name0 = d0.name;
                  if(BodyFather.noSumNameArr.indexOf(name0) == -1)
                  {
                     if(father0 == BodyFather.wilder || d0.showLevel >= 999)
                     {
                        rareBossNameArr.push(name0);
                     }
                     else if(rareBossNameArr.indexOf(name0) == -1)
                     {
                        bossNameArr.push(name0);
                     }
                  }
               }
            }
         }
         bossNameArr0 = bossNameArr;
         rareBossNameArr0 = rareBossNameArr;
      }
      
      private static function panBossNiubi(d0:NormalBodyDefine) : void
      {
         var name0:String = null;
         var sd0:SkillDefine = null;
         var arr0:Array = d0.skillArr.concat(d0.getBossSkillArr(false,false));
         var s0:String = "";
         for each(name0 in arr0)
         {
            if(sRareIdObj.hasOwnProperty(name0))
            {
               sd0 = Gaming.defineGroup.skill.getDefine(name0);
               s0 += "、" + sd0.cnName;
            }
         }
         if(s0 != "")
         {
            INIT.tempTrace(d0.cnName + "【" + s0 + "】");
         }
      }
      
      public static function getStarMax() : int
      {
         return skillArr.length;
      }
      
      public static function getSave(cardNum0:int, highB0:Boolean, hsvMul0:Number) : BossCardSave
      {
         var ranArr0:Array = ranArr;
         if(highB0)
         {
            ranArr0 = highRanArr;
         }
         else if(cardNum0 >= 2000)
         {
            ranArr0 = ranArr2000;
         }
         var index0:int = ArrayMethod.getPro_byArrSum(ranArr0);
         var star0:int = index0 + 1;
         if(hsvMul0 > 4)
         {
            if(star0 >= 6)
            {
               star0 = 5;
            }
         }
         return getSaveStar(star0);
      }
      
      public static function getCardPro(star0:int, highB0:Boolean) : Number
      {
         var ranArr0:Array = ranArr;
         if(highB0)
         {
            ranArr0 = highRanArr;
         }
         return ArrayMethod.getElementLimit(ranArr0,star0 - 1);
      }
      
      public static function getSaveStar(star0:int, excludeArr0:Array = null, setBodyName0:String = "") : BossCardSave
      {
         var maxStar0:int = getStarMax();
         if(star0 > maxStar0)
         {
            star0 = maxStar0;
         }
         var i:int = star0 - 1;
         var dpsA0:Array = dpsArr[i];
         var lifeA0:Array = lifeArr[i];
         var dps0:Number = Math.ceil(NumberMethod.randomInRange(dpsA0[0],dpsA0[1]));
         var life0:Number = Math.ceil(2 * NumberMethod.randomInRange(lifeA0[0],lifeA0[1]));
         var skill0:Number = Number(skillArr[i]);
         var rareSkill0:Number = Number(rareSkillArr[i]);
         var s0:BossCardSave = new BossCardSave();
         if(setBodyName0 == "")
         {
            setBodyName0 = getBodyName(star0,excludeArr0);
         }
         var bodyD0:NormalBodyDefine = Gaming.defineGroup.body.getDefine(setBodyName0);
         var baseSkillArr0:Array = bodyD0.getEditSkillArr();
         var baseSkillIDArr0:Array = getIdArrBySkillNameArr(baseSkillArr0);
         s0.n = bodyD0.name;
         s0.li = life0;
         s0.dp = dps0;
         s0.s = star0;
         s0.sr = getSkillIdArr(skill0,rareSkill0,baseSkillIDArr0);
         s0.o = getAddObj(star0);
         s0.lk = star0 >= 6 ? 1 : 0;
         return s0;
      }
      
      private static function getAddObj(star0:int) : Object
      {
         var obj0:Object = {};
         if(star0 >= 6)
         {
            obj0["dpsAllBlack"] = NumberMethod.toFixed(NumberMethod.getRandom(0.05,0.1),2);
         }
         if(star0 >= 7)
         {
            obj0["hurtAll"] = NumberMethod.toFixed(NumberMethod.getRandom(0.05,0.1),2);
         }
         return obj0;
      }
      
      private static function getBodyName(star0:int, excludeArr0:Array = null) : String
      {
         var rarePro0:Number = Number(rareArr[star0]);
         var bossArr0:Array = bossNameArr;
         if(rarePro0 > 0 && Math.random() < rarePro0)
         {
            bossArr0 = rareBossNameArr;
         }
         if(Boolean(excludeArr0) && excludeArr0.length > 0)
         {
            bossArr0 = ArrayMethod.deductArr(bossArr0,excludeArr0);
         }
         return ArrayMethod.getRandomOne(bossArr0);
      }
      
      public static function getSkillId(name0:String) : String
      {
         if(idSO.hasOwnProperty(name0))
         {
            return idSO[name0];
         }
         return name0;
      }
      
      public static function getSkillName(id0:String) : String
      {
         if(sO.hasOwnProperty(id0))
         {
            return sO[id0];
         }
         return id0;
      }
      
      public static function isRareSkill(name0:String) : Boolean
      {
         if(idSO.hasOwnProperty(name0) == false)
         {
            return true;
         }
         var id0:String = getSkillId(name0);
         return sRareA.indexOf(id0) >= 0;
      }
      
      public static function getSkillNameArrByIdArr(idArr0:Array) : Array
      {
         var id0:String = null;
         var name0:String = null;
         var arr0:Array = [];
         for each(id0 in idArr0)
         {
            name0 = getSkillName(id0);
            if(name0 != null)
            {
               arr0.push(name0);
            }
         }
         return arr0;
      }
      
      public static function getIdArrBySkillNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var id0:String = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            id0 = getSkillId(name0);
            if(id0 != null)
            {
               arr0.push(id0);
            }
         }
         return arr0;
      }
      
      public static function getIdArrBySkillCnArr(cnArr0:Array) : Array
      {
         var cn0:String = null;
         var id0:String = null;
         var arr0:Array = [];
         for each(cn0 in cnArr0)
         {
            id0 = idCnSO[cn0];
            if(id0 != null)
            {
               arr0.push(id0);
            }
            else
            {
               id0 = cn0;
            }
         }
         return arr0;
      }
      
      private static function getSkillIdArr(num0:int, rare0:int, excludeArr0:Array = null) : Array
      {
         var sRareA0:Array = null;
         var rarr0:Array = null;
         var sA0:Array = sA;
         if(Boolean(excludeArr0) && excludeArr0.length > 0)
         {
            sA0 = ArrayMethod.deductArr(sA0,excludeArr0);
         }
         var arr0:Array = ArrayMethod.getRandomArray(sA,num0);
         if(rare0 > 0)
         {
            sRareA0 = sRareA;
            if(Boolean(excludeArr0) && excludeArr0.length > 0)
            {
               sRareA0 = ArrayMethod.deductArr(sRareA0,excludeArr0);
            }
            rarr0 = ArrayMethod.getRandomArray(sRareA,rare0);
            arr0 = rarr0.concat(arr0);
         }
         return arr0;
      }
      
      public static function editToCardSave(e0:BossEditData) : BossCardSave
      {
         var s0:BossCardSave = new BossCardSave();
         s0.n = e0.name;
         s0.s = e0.getStar();
         s0.sr = getIdArrBySkillNameArr(e0.getSkillNameArr());
         s0.li = e0.getValueByDefName(BossEditPro.li);
         s0.dp = e0.getValueByDefName(BossEditPro.dp);
         return s0;
      }
      
      public static function editToCardXML(e0:BossEditData) : String
      {
         var s0:BossCardSave = editToCardSave(e0);
         return s0.getXMLStr();
      }
   }
}

