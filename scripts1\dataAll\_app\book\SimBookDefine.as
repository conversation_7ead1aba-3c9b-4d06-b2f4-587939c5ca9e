package dataAll._app.book
{
   import dataAll.gift.define.GiftAddDefine;
   
   public class SimBookDefine implements IO_BookDefine
   {
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var iconUrl:String = "";
      
      public var imgUrl:String = "";
      
      public var info:String = "";
      
      public var cardB:Boolean = false;
      
      public function SimBookDefine()
      {
         super();
      }
      
      public function getBookIconUrl(w0:int = 0, h0:int = 0) : String
      {
         return this.iconUrl;
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function getBookImgUrl() : String
      {
         return this.imgUrl;
      }
      
      public function getBookInfo() : String
      {
         return this.info;
      }
      
      public function getBookId() : String
      {
         return this.name;
      }
      
      public function haveCardB() : Boolean
      {
         return false;
      }
      
      public function getBookCanGet() : Boolean
      {
         return false;
      }
      
      public function getBookGift() : GiftAddDefine
      {
         return null;
      }
   }
}

