package dataAll.image
{
   import com.sounto.utils.ClassProperty;
   
   public class EffectLineDefine
   {
      
      public static var pro_arr:Array = null;
      
      public var len:int = 0;
      
      public var haveDataB:Boolean = false;
      
      public var waveAn:Number = 0;
      
      public var everFleshB:Boolean = false;
      
      public var time:Number = -1;
      
      public function EffectLineDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.haveDataB = true;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}

