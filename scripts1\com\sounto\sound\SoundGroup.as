package com.sounto.sound
{
   import com.sounto.net.SWFLoaderManager;
   import flash.media.Sound;
   import flash.media.SoundTransform;
   
   public class SoundGroup
   {
      
      public var swfM:SWFLoaderManager = null;
      
      public var obj:Object = {};
      
      public var sceneMiddeX:int = 0;
      
      public var sceneMiddeY:int = 0;
      
      private var temp_sf:SoundTransform = new SoundTransform();
      
      public var nowPlayMusic:SoundData = null;
      
      public var soundOn:Boolean = true;
      
      private var _musicOn:Boolean = true;
      
      public function SoundGroup()
      {
         super();
      }
      
      public function init(swfM0:SWFLoaderManager) : *
      {
         this.swfM = swfM0;
      }
      
      public function inSceneMidde(x0:int, y0:int) : void
      {
         this.sceneMiddeX = x0;
         this.sceneMiddeY = y0;
      }
      
      public function addSoundFull(fullName0:String) : void
      {
         var index0:int = fullName0.indexOf("/");
         var s0:String = fullName0.substring(0,index0);
         var n0:String = fullName0.substring(index0 + 1);
         this.addSound(s0,n0);
      }
      
      private function addSound(father0:String, name0:String) : void
      {
         if(<PERSON>olean(this.getSound(father0,name0)))
         {
            return;
         }
         var sound0:Sound = this.swfM.getResource(father0,name0);
         if(sound0)
         {
            this.addSoundInObj(sound0,father0,name0);
         }
      }
      
      public function addSound_bySwfInside(swfName0:String, labelArr0:Array) : void
      {
         var n:* = undefined;
         var s0:String = null;
         var n0:String = null;
         var sound0:Sound = null;
         for(n in labelArr0)
         {
            s0 = swfName0;
            n0 = labelArr0[n];
            if(!Boolean(this.getSound(s0,n0)))
            {
               sound0 = this.swfM.getResource(s0,n0);
               if(!sound0)
               {
                  INIT.showError("添加音效时找不到资源：" + s0 + "/" + n0);
               }
               else
               {
                  this.addSoundInObj(sound0,s0,n0);
               }
            }
         }
      }
      
      private function addSoundInObj(sound0:Sound, father0:String, label0:String) : SoundData
      {
         var s0:SoundData = null;
         if(!this.getSound(father0,label0))
         {
            s0 = new SoundData(sound0);
            s0.father = father0;
            s0.label = label0;
            if(!this.obj.hasOwnProperty(father0))
            {
               this.obj[father0] = {};
            }
            this.obj[father0][label0] = s0;
            return s0;
         }
         return null;
      }
      
      public function getSoundFull(fullName0:String) : SoundData
      {
         var index0:int = fullName0.indexOf("/");
         var s0:String = fullName0.substring(0,index0);
         var n0:String = fullName0.substring(index0 + 1);
         return this.getSound(s0,n0);
      }
      
      public function getSound(father0:String, label0:String) : SoundData
      {
         var s0:SoundData = null;
         if(this.obj.hasOwnProperty(father0))
         {
            return this.obj[father0][label0];
         }
         return null;
      }
      
      public function clearResourceByArray(arr0:Array) : void
      {
         var n:* = undefined;
         var fullName0:String = null;
         var index0:int = 0;
         var s0:String = null;
         var n0:String = null;
         var su0:SoundData = null;
         var xxx:int = 0;
         for(n in arr0)
         {
            fullName0 = arr0[n];
            index0 = fullName0.indexOf("/");
            s0 = fullName0.substring(0,index0);
            n0 = fullName0.substring(index0 + 1);
            if(n0.indexOf("barrel") >= 0)
            {
               xxx = 0;
            }
            su0 = this.getSound(s0,n0);
            if(su0 is SoundData)
            {
               su0.clear();
               this.obj[s0][n0] = null;
            }
         }
      }
      
      public function playUISound(label0:String) : SoundData
      {
         return this.playSound(SoundLabel.uiSound,label0);
      }
      
      public function playSound(father0:String, label0:String) : SoundData
      {
         if(!this.soundOn)
         {
            return null;
         }
         var s0:SoundData = this.getSound(father0,label0);
         if(s0 is SoundData)
         {
            s0.play();
         }
         else
         {
            "找不到音效：" + father0 + "/" + label0;
         }
         return s0;
      }
      
      public function playSoundFull(fullName0:String, mul0:Number = 1) : SoundData
      {
         if(!this.soundOn)
         {
            return null;
         }
         var s0:SoundData = this.getSoundFull(fullName0);
         if(s0 is SoundData)
         {
            if(mul0 < 1)
            {
               this.temp_sf.volume = mul0;
               this.temp_sf.pan = 0;
               s0.playEffect(1,this.temp_sf);
            }
            else
            {
               s0.play();
            }
         }
         else
         {
            "找不到音效：" + fullName0;
         }
         return s0;
      }
      
      public function playAndAdd(fullName0:String) : SoundData
      {
         if(!this.soundOn)
         {
            return null;
         }
         var s0:SoundData = this.getSoundFull(fullName0);
         if(!s0)
         {
            this.addSoundFull(fullName0);
         }
         return this.playSoundFull(fullName0);
      }
      
      public function playSoundFullGap(fullName0:String, x0:int, y0:int, mul0:Number = 1) : SoundData
      {
         var st0:SoundTransform = null;
         if(!this.soundOn)
         {
            return null;
         }
         var s0:SoundData = this.getSoundFull(fullName0);
         if(s0 is SoundData)
         {
            st0 = this.getGapSoundTransform(x0,y0);
            st0.volume *= mul0;
            if(st0.volume > 0)
            {
               s0.playEffect(1,st0);
            }
         }
         return s0;
      }
      
      private function getGapSoundTransform(x0:int, y0:int) : SoundTransform
      {
         var mx0:int = this.sceneMiddeX;
         var my0:int = this.sceneMiddeY;
         var cx0:int = x0 - mx0;
         var cy0:int = y0 - my0;
         var clong0:int = Math.sqrt(cx0 * cx0 + cy0 * cy0);
         var volume0:Number = 1;
         if(clong0 <= 500 - 100)
         {
            volume0 = 1;
         }
         else
         {
            volume0 = 1 - (clong0 - 500 + 100) / 500;
            if(volume0 < 0.2)
            {
               volume0 = 0.2;
            }
         }
         var panning0:Number = cx0 / 500;
         if(panning0 < -1)
         {
            panning0 = -1;
         }
         if(panning0 > 1)
         {
            panning0 = 1;
         }
         this.temp_sf.volume = volume0;
         this.temp_sf.pan = panning0;
         return this.temp_sf;
      }
      
      public function playMusicAdd(father0:String, label0:String = "s0", volumeMul0:Number = 1) : SoundData
      {
         var s0:SoundData = this.getSound(father0,label0);
         if(!s0)
         {
            this.addSound(father0,label0);
         }
         return this.playMusic(father0,label0,volumeMul0);
      }
      
      public function playMusicUrlAdd(url0:String, volumeMul0:Number = 1) : SoundData
      {
         var index0:int = url0.indexOf("/");
         var father0:String = url0.substring(0,index0);
         var name0:String = url0.substring(index0 + 1);
         return this.playMusicAdd(father0,name0,volumeMul0);
      }
      
      public function playMusic(father0:String, label0:String = "s0", volumeMul0:Number = 1) : SoundData
      {
         var s0:SoundData = this.getSound(father0,label0);
         if(s0 is SoundData)
         {
            if(this.nowPlayMusic is SoundData)
            {
               if(this.nowPlayMusic.father == father0 && this.nowPlayMusic.label == label0)
               {
                  return s0;
               }
               SoundTween.to(this.nowPlayMusic,2);
            }
            this.nowPlayMusic = s0;
            if(!s0.havePlayB())
            {
               s0.play(999999,0);
            }
            s0.setVolumeMul(volumeMul0);
            if(this._musicOn)
            {
               SoundTween.to(this.nowPlayMusic,1,1,0);
            }
            else
            {
               SoundTween.to(this.nowPlayMusic,1,0,0,false);
            }
         }
         return s0;
      }
      
      public function set musicOn(bb0:Boolean) : void
      {
         this._musicOn = bb0;
         if(this.nowPlayMusic is SoundData)
         {
            if(bb0)
            {
               SoundTween.to(this.nowPlayMusic,0.5,1);
            }
            else
            {
               SoundTween.to(this.nowPlayMusic,0.5,0,-1,false);
            }
         }
      }
      
      public function get musicOn() : Boolean
      {
         return this._musicOn;
      }
   }
}

