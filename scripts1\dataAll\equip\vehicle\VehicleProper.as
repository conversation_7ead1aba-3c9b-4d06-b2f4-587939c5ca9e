package dataAll.equip.vehicle
{
   import com.sounto.oldUtils.ComMethod;
   
   public class VehicleProper
   {
      
      public static const EVO:String = "evo";
      
      public static const VEHICLE_TYPE:String = "vehicleType";
      
      public static const BASE_LIFE:String = "baseLife";
      
      public static const NOW_LIFE:String = "nowLife";
      
      public static const SPEED:String = "speed";
      
      public static const DURATION:String = "duration";
      
      public static const CD:String = "cd";
      
      public static const LEVEL:String = "level";
      
      public static const BASE_LIFE_MUL:String = "baseLifeMul";
      
      public static const LIFE_MUL:String = "lifeMul";
      
      public static const MAIN_MUL:String = "mainMul";
      
      public static const SUB_MUL:String = "subMul";
      
      public static const ATTACK_MUL:String = "attackMul";
      
      public static const LIMIT_LIFE:String = "limitLife";
      
      public static const LIMIT_MAIN_HURT:String = "limitMainHurt";
      
      public static const LIMIT_SUB_HURT:String = "limitSubHurt";
      
      public static const LIMIT_ATTACK_HURT:String = "limitAttackHurt";
      
      public static var tipBaseProArr:Array = [EVO,VEHICLE_TYPE,BASE_LIFE_MUL,LIFE_MUL,BASE_LIFE,NOW_LIFE,SPEED,DURATION,CD];
      
      public static var composeBaseProArr:Array = [LEVEL,VEHICLE_TYPE,BASE_LIFE,MAIN_MUL,SUB_MUL,ATTACK_MUL,SPEED,DURATION,CD];
      
      public static var upgradeBaseProArr:Array = [LEVEL,BASE_LIFE,LIMIT_LIFE,LIMIT_MAIN_HURT,LIMIT_SUB_HURT,LIMIT_ATTACK_HURT];
      
      public static var evolutionBaseProArr:Array = [MAIN_MUL,SUB_MUL,ATTACK_MUL];
      
      public static var strengthenBaseProArr:Array = [LIFE_MUL,MAIN_MUL,SUB_MUL,ATTACK_MUL];
      
      public static var hurtProArr:Array = [MAIN_MUL,SUB_MUL,ATTACK_MUL,LIMIT_MAIN_HURT,LIMIT_SUB_HURT,LIMIT_ATTACK_HURT];
      
      public function VehicleProper()
      {
         super();
      }
      
      public static function getLabelByProName(pro0:String) : String
      {
         var type0:String = null;
         var Type0:String = null;
         var arr0:Array = VehicleAttackLabel.arr;
         for each(type0 in arr0)
         {
            Type0 = ComMethod.firstUpperCase(type0);
            if(pro0.indexOf(type0) >= 0 || pro0.indexOf(Type0) >= 0)
            {
               return type0;
            }
         }
         return "";
      }
   }
}

