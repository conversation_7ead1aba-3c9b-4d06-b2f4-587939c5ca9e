package dataAll.gift.school
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class National2017Save
   {
      
      public static var pro_arr:Array = null;
      
      public static const START_TIME:String = "2018-9-30";
      
      public static const END_TIME:String = "2018-10-7";
      
      public static const taskNameArr:Array = ["dayTask","wilder","level","arena"];
      
      public static const dayTaskNameArr:Array = ["dailySign","ask","normalLevel","treasureTask","smelt"];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var dayB:Boolean = false;
      
      public var dropB:Boolean = false;
      
      public var numObj:NumberEncodeObj = new NumberEncodeObj();
      
      public function National2017Save()
      {
         super();
         this.dayNum = 0;
         this.giftNum = 0;
      }
      
      public function get giftNum() : Number
      {
         return this.CF.getAttribute("giftNum");
      }
      
      public function set giftNum(v0:Number) : void
      {
         this.CF.setAttribute("giftNum",v0);
      }
      
      public function get dayNum() : Number
      {
         return this.CF.getAttribute("dayNum");
      }
      
      public function set dayNum(v0:Number) : void
      {
         this.CF.setAttribute("dayNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.dayB = false;
      }
      
      public function getDrawAll() : int
      {
         var day0:int = this.dayNum * 12;
         var sum0:int = this.sumCompleteB() ? 30 : 0;
         return day0 + sum0;
      }
      
      public function getDrawSurplus() : int
      {
         return this.getDrawAll() - this.giftNum;
      }
      
      public function sumCompleteB() : Boolean
      {
         var name0:String = null;
         var now0:int = 0;
         var max0:int = 0;
         for each(name0 in taskNameArr)
         {
            now0 = this.getNow(name0);
            max0 = this.getMax(name0);
            if(now0 < max0)
            {
               return false;
            }
         }
         return true;
      }
      
      public function addNum(name0:String) : void
      {
         this.numObj.addNum(name0,1);
      }
      
      public function getNow(name0:String) : Number
      {
         return this.numObj.getAttribute(name0);
      }
      
      public function getMax(name0:String) : Number
      {
         if(name0 == "dayTask")
         {
            return 10;
         }
         if(name0 == "wilder")
         {
            return 5;
         }
         if(name0 == "level")
         {
            return 20;
         }
         if(name0 == "arena")
         {
            return 10;
         }
         return 999;
      }
      
      public function testSumComplete() : void
      {
         var name0:String = null;
         for each(name0 in taskNameArr)
         {
            this.numObj.setAttribute(name0,99);
         }
      }
      
      public function testSumClear() : void
      {
         this.numObj.clearData();
      }
      
      public function getDayMax(name0:String) : Number
      {
         if(name0 == "normalLevel")
         {
            return 3;
         }
         if(name0 == "treasureTask")
         {
            return 2;
         }
         if(name0 == "smelt")
         {
            return 3;
         }
         return 1;
      }
      
      public function dayCompleteEvent() : void
      {
         if(!this.dayB)
         {
            this.dayB = true;
            ++this.dayNum;
         }
      }
      
      public function getCanSuppleNum(da0:StringDate) : int
      {
         var cday0:int = da0.reductionOneStr(START_TIME);
         var num0:int = cday0 - this.dayNum;
         if(num0 < 0)
         {
            num0 = 0;
         }
         return num0;
      }
      
      public function suppleEvent(num0:int) : void
      {
         this.dayNum += num0;
      }
   }
}

