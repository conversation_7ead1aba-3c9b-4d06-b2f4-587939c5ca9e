package dataAll.level.define.mapRect
{
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   
   public class MapRectGroup
   {
      
      public var obj:Object = {};
      
      public var basinArr:Array = [];
      
      public var enemyRectArr:Array = [];
      
      private var haveDataB:Boolean = false;
      
      public function MapRectGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var xml2:XML = null;
         var d0:MapRect = null;
         if(!xml0)
         {
            return;
         }
         var xmllist2:XMLList = xml0.rect;
         for(n in xmllist2)
         {
            xml2 = xmllist2[n];
            d0 = new MapRect();
            d0.inData_byXML(xml2);
            if(d0.id == "")
            {
               d0.id = String("rect_" + int(n));
            }
            this.obj[d0.id] = d0;
         }
         this.fleshBasinArr();
         this.fleshEnemyArr();
      }
      
      public function inDataBySp(con0:DisplayObjectContainer) : void
      {
         var labelNumObj:Object = null;
         var i:int = 0;
         var mc0:MovieClip = null;
         var r0:MapRect = null;
         if(!con0)
         {
            return;
         }
         if(!this.haveDataB)
         {
            this.haveDataB = true;
            labelNumObj = {};
            for(i = 0; i < con0.numChildren; i++)
            {
               mc0 = con0.getChildAt(i) as MovieClip;
               if(Boolean(mc0))
               {
                  r0 = new MapRect();
                  r0.inDataBySp(mc0,labelNumObj);
                  this.obj[r0.id] = r0;
               }
            }
         }
         this.fleshBasinArr();
         this.fleshEnemyArr();
      }
      
      private function fleshBasinArr() : void
      {
         var n:* = undefined;
         var d0:MapRect = null;
         this.basinArr.length = 0;
         for(n in this.obj)
         {
            d0 = this.obj[n];
            if(d0.label == "addCharger")
            {
               this.basinArr.push(d0);
            }
         }
      }
      
      private function fleshEnemyArr() : void
      {
         var d0:MapRect = null;
         var index0:int = 0;
         this.enemyRectArr.length = 0;
         for each(d0 in this.obj)
         {
            index0 = int(d0.id.substr(1));
            if(index0 > 0)
            {
               this.enemyRectArr.push(d0);
            }
         }
      }
      
      public function getRect(id0:String) : MapRect
      {
         return this.obj[id0];
      }
      
      public function fixedBy(rg0:MapRectGroup, type0:String) : void
      {
         if(type0 == "all")
         {
            this.obj = rg0.obj;
         }
         else if(type0 == "before" || type0 == "affter")
         {
            this.fixedObj(rg0.obj);
         }
         this.fleshBasinArr();
         this.fleshEnemyArr();
      }
      
      private function fixedObj(obj0:Object) : void
      {
         var n:* = undefined;
         for(n in obj0)
         {
            this.obj[n] = obj0[n];
         }
      }
   }
}

