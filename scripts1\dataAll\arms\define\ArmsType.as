package dataAll.arms.define
{
   import com.sounto.utils.StringMethod;
   import dataAll.arms.creator.ArmsSpecial;
   import dataAll.equip.define.EquipColor;
   
   public class ArmsType
   {
      
      public static const LINE:String = "line";
      
      public static const POINT:String = "point";
      
      public static const rifle:String = "rifle";
      
      public static const sniper:String = "sniper";
      
      public static const shotgun:String = "shotgun";
      
      public static const pistol:String = "pistol";
      
      public static const rocket:String = "rocket";
      
      public static const crossbow:String = "crossbow";
      
      public static const flamer:String = "flamer";
      
      public static const howitzer:String = "howitzer";
      
      public static const wavegun:String = "wavegun";
      
      public static const laser:String = "laser";
      
      public static const lightning:String = "lightning";
      
      public static const cutter:String = "cutter";
      
      public static const weather:String = "weather";
      
      public static const energy:String = "energy";
      
      public static const TYPE_ARR:Array = [rifle,sniper,shotgun,pistol,rocket,crossbow,flamer,howitzer,wavegun,laser,lightning,weather,cutter,energy];
      
      private static var CN_ARR:Array = null;
      
      public static const uiArr:Array = [rifle,sniper,shotgun,pistol,rocket,crossbow,flamer];
      
      public static const NORMAL_TYPE_ARR:Array = [rifle,sniper,shotgun,pistol,rocket];
      
      public static const nocomArr:Array = [sniper,pistol,crossbow,howitzer,wavegun,weather,cutter,lightning,flamer,energy];
      
      public static const otherArr:Array = [howitzer,wavegun,lightning,cutter,weather,energy];
      
      public static const onlyOneArr:Array = [lightning,cutter,wavegun];
      
      public static const highSpeedArr:Array = [rifle,pistol,flamer,energy];
      
      public static const penetrationNumMore5Arr:Array = [flamer,wavegun,weather,cutter,laser];
      
      public static const penetrationNumMore5NameArr:Array = ["greedySnake","flySnake"];
      
      public static const armsSpeed_jie:Array = [rocket,crossbow,flamer,sniper,cutter,energy];
      
      public static const armsSpeed_jieHurt:Array = [rocket,crossbow,flamer];
      
      public static const ID_ARR:Array = ["01","02","03","04","05","06","07","08","09","10","11","12","13","14","15"];
      
      public static const CapacityMul_arr:Array = [4,1,1,2,1,2,7,0.1,0.2,0.2,0.2,0.2,0.2,0.2];
      
      public function ArmsType()
      {
         super();
      }
      
      public static function panArmsSpeed_jie(d0:ArmsDefine, typeArr0:Array) : Boolean
      {
         var armsType0:String = null;
         var bb0:Boolean = false;
         if(Boolean(d0))
         {
            armsType0 = d0.armsType;
            if(ArmsType.armsSpeed_jie.indexOf(armsType0) >= 0)
            {
               if(armsType0 == ArmsType.rocket)
               {
                  if(d0.bulletNum == 1)
                  {
                     bb0 = true;
                  }
               }
               else
               {
                  bb0 = true;
               }
            }
         }
         return bb0;
      }
      
      public static function getID_byType(type0:String) : String
      {
         return ID_ARR[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getCapacityMul(type0:String) : Number
      {
         return CapacityMul_arr[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getReloadGapMul(type0:String) : Number
      {
         if(type0 == flamer || type0 == weather)
         {
            return 0.5;
         }
         if(type0 == howitzer)
         {
            return 0.5;
         }
         return 1;
      }
      
      public static function getCnStr(typeArr0:Array) : String
      {
         var cnArr0:Array = getCnArr(typeArr0);
         return StringMethod.concatStringArr(cnArr0,99);
      }
      
      public static function getCnArr(typeArr0:Array) : Array
      {
         var type0:String = null;
         var cn0:String = null;
         var cnArr0:Array = [];
         for each(type0 in typeArr0)
         {
            cn0 = Gaming.defineGroup.armsCharger.getCn(type0);
            cnArr0.push(cn0);
         }
         return cnArr0;
      }
      
      public static function getCnArrStatic() : Array
      {
         if(CN_ARR == null)
         {
            CN_ARR = getCnArr(TYPE_ARR);
         }
         return CN_ARR;
      }
      
      public static function getTrueDpsMul(color0:String, armsType0:String) : Number
      {
         return ArmsColor.getUIDpsMul(color0);
      }
      
      public static function getUIDpsMul(type0:String, color0:String, evoLv0:int, name0:String) : Number
      {
         var purgoldB0:Boolean = false;
         var yagoldB0:Boolean = false;
         var v0:Number = 1;
         if(type0 == crossbow)
         {
            v0 = 2.6;
         }
         else if(type0 == flamer)
         {
            v0 = 2;
         }
         else if(type0 == howitzer)
         {
            v0 = 2;
         }
         else if(type0 == wavegun)
         {
            v0 = 2;
         }
         else if(type0 == laser)
         {
            v0 = 2;
         }
         else if(type0 == lightning)
         {
            v0 = 2;
         }
         else if(type0 == weather)
         {
            v0 = 2;
         }
         else if(type0 == cutter)
         {
            v0 = 2;
         }
         if(color0 == EquipColor.DARKGOLD || color0 == EquipColor.PURGOLD || color0 == EquipColor.YAGOLD)
         {
            purgoldB0 = color0 == EquipColor.PURGOLD;
            yagoldB0 = color0 == EquipColor.YAGOLD;
            if(type0 == sniper)
            {
               if(purgoldB0 || yagoldB0)
               {
                  v0 *= 1.35;
               }
               else
               {
                  v0 *= 1.3;
               }
            }
            else if(type0 == shotgun)
            {
               if(purgoldB0 || yagoldB0)
               {
                  v0 *= 1.23;
               }
               else
               {
                  v0 *= 1.15;
               }
            }
            else if(type0 == rocket)
            {
               if(purgoldB0 || yagoldB0)
               {
                  if(name0 == "rocketCate")
                  {
                     v0 *= 1.12;
                  }
                  else
                  {
                     v0 *= 0.8;
                  }
               }
               else
               {
                  v0 *= 0.95;
               }
            }
            else if(type0 == rifle)
            {
               if(purgoldB0 || yagoldB0)
               {
                  v0 *= 1.08;
               }
            }
            else if(type0 == pistol)
            {
               if(purgoldB0 || yagoldB0)
               {
                  v0 *= 1.03;
               }
            }
            else if(type0 == flamer)
            {
               if(evoLv0 >= 9)
               {
                  v0 *= 0.97;
               }
               if(purgoldB0)
               {
                  v0 *= 0.8;
               }
               else if(yagoldB0)
               {
                  v0 *= 0.75;
               }
            }
            else if(type0 == laser)
            {
               if(evoLv0 >= 9)
               {
                  v0 *= 0.965;
               }
               if(purgoldB0 || yagoldB0)
               {
                  v0 *= 0.83;
               }
            }
            else if(type0 == energy)
            {
               v0 = 1.462;
               if(purgoldB0 || yagoldB0)
               {
                  v0 *= 0.84;
               }
            }
            else if(type0 == lightning)
            {
               if(purgoldB0 || yagoldB0)
               {
                  if(name0 == "extremeGun")
                  {
                     v0 *= 0.84;
                  }
               }
            }
         }
         return v0 * ArmsColor.getUIDpsMul(color0);
      }
      
      public static function getExtraDropPro(type0:String) : Number
      {
         if(type0 == crossbow || type0 == flamer)
         {
            return 0.7;
         }
         return 1;
      }
      
      public static function canCloseCritB(type0:String) : Boolean
      {
         return type0 == shotgun || type0 == rifle || type0 == weather;
      }
      
      public static function getDieExtraShake(type0:String) : int
      {
         if(type0 == sniper)
         {
            return 20;
         }
         if(type0 == rifle)
         {
            return 7;
         }
         return 12;
      }
      
      public static function getVolume(type0:String) : Number
      {
         if(type0 == sniper)
         {
            return 0.35;
         }
         return 0.2;
      }
      
      public static function getSightMul(type0:String) : Number
      {
         if(type0 == sniper || type0 == crossbow)
         {
            return 2;
         }
         if(type0 == howitzer)
         {
            return 1.5;
         }
         return 1;
      }
      
      public static function getLaunchLabel(type0:String) : String
      {
         if(type0 == shotgun || type0 == sniper)
         {
            return type0;
         }
         return "";
      }
      
      public static function getMaxBulletNum(type0:String) : int
      {
         if(type0 == shotgun)
         {
            return 9;
         }
         return 1;
      }
      
      public static function getHurtAdd(type0:String) : Number
      {
         if(type0 == rifle)
         {
            return 0.4;
         }
         if(type0 == sniper)
         {
            return 7;
         }
         if(type0 == shotgun)
         {
            return 3.7 / 7;
         }
         if(type0 == pistol)
         {
            return 1;
         }
         if(type0 == rocket)
         {
            return 7.5;
         }
         if(type0 == crossbow)
         {
            return 7;
         }
         if(type0 == howitzer)
         {
            return 7;
         }
         if(type0 == wavegun)
         {
            return 5;
         }
         if(type0 == cutter)
         {
            return 5;
         }
         if(type0 == laser)
         {
            return 0.3;
         }
         if(type0 == lightning)
         {
            return 3.7 / 7;
         }
         if(type0 == weather)
         {
            return 7;
         }
         if(type0 == flamer)
         {
            return 0.3;
         }
         return 0.5;
      }
      
      public static function getMaxAttackGap(type0:String, d0:ArmsDefine) : Number
      {
         if(type0 == rifle)
         {
            return 0.05;
         }
         if(type0 == sniper)
         {
            return 0.5;
         }
         if(type0 == shotgun)
         {
            return 0.5;
         }
         if(type0 == pistol)
         {
            return 0.12;
         }
         if(type0 == energy)
         {
            return 0.12;
         }
         if(type0 == rocket)
         {
            if(d0.isPianoB())
            {
               return 0.15;
            }
            return 0.5;
         }
         if(type0 == crossbow)
         {
            return 0.5;
         }
         if(type0 == howitzer)
         {
            return 0.5;
         }
         if(type0 == wavegun)
         {
            return 0.35;
         }
         if(type0 == laser)
         {
            return 0.7;
         }
         if(type0 == lightning)
         {
            return 0.2;
         }
         if(type0 == cutter)
         {
            return 0.35;
         }
         if(type0 == weather)
         {
            return 0.7;
         }
         if(type0 == flamer)
         {
            return 0.03;
         }
         return 0.5;
      }
      
      public static function getHitProMul(type0:String) : Number
      {
         var v0:Number = getHurtAdd(type0);
         if(type0 == flamer)
         {
            v0 = 0.1;
         }
         return v0;
      }
      
      public static function getAngleAdd(type0:String) : Number
      {
         if(type0 == shotgun)
         {
            return 0.3;
         }
         return 1;
      }
      
      public static function getAttackGapAdd(type0:String) : Number
      {
         if(type0 == rifle)
         {
            return 0.7;
         }
         if(type0 == flamer)
         {
            return 0.5;
         }
         if(type0 == laser)
         {
            return 0;
         }
         if(type0 == weather)
         {
            return 0.2;
         }
         return 1;
      }
      
      public static function getBulletSpeedMul(type0:String) : Number
      {
         if(type0 == flamer)
         {
            return 0.6;
         }
         return 1;
      }
      
      public static function getLockPro(type0:String) : Array
      {
         if(type0 == flamer)
         {
            return [ArmsSpecial.penetrationNum,ArmsSpecial.penetrationGap];
         }
         if(type0 == howitzer)
         {
            return ArmsSpecial.arr;
         }
         if(type0 == wavegun)
         {
            return ArmsSpecial.arr;
         }
         if(type0 == laser || type0 == lightning || type0 == weather || type0 == cutter || type0 == energy)
         {
            return ArmsSpecial.arr;
         }
         return [ArmsSpecial.penetrationGap];
      }
      
      public static function getShootAngleMul(len0:int) : Number
      {
         var v0:Number = len0 / 200 + 0.15;
         if(v0 > 1)
         {
            v0 = 1;
         }
         else if(v0 < 0.3)
         {
            v0 = 0.3;
         }
         return v0;
      }
   }
}

