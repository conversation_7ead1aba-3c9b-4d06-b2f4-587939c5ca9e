package dataAll.level.modeDiy
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._base.NormalDefine;
   import dataAll.arms.ArmsChargerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.level.define.unit.UnitType;
   import dataAll.things.define.ThingsDefine;
   import dataAll.ui.GatherColor;
   
   public class ModeDiyDefine extends NormalDefine
   {
      
      private static const onlySingleSkillArr:Array = ["revengeGhost","deadlyGhost","MeatySkillBack"];
      
      private static const noPartnerSkillArr:Array = ["FoggyDefence","revengeArrow","deadlyArrow"].concat(onlySingleSkillArr);
      
      public static const superNoSkillArr:Array = ["fightBackBullet","skillCopy_enemy"];
      
      public static const ZERO:ModeDiyDefine = new ModeDiyDefine();
      
      public static const CLOSE:String = "close";
      
      public static const TEAM:String = "team";
      
      public static const PARTNER:String = "partner";
      
      public static const SINGLE:String = "single";
      
      public static const NOCOM:String = "nocom";
      
      public static const DUEL:String = "duel";
      
      public static const USE_ARR:Array = [TEAM,PARTNER,SINGLE,NOCOM,DUEL];
      
      public static const ALL_ARR:Array = [CLOSE,TEAM,PARTNER,SINGLE,NOCOM,DUEL];
      
      public static const SPACE_CRAFT:String = "spaceCraft";
      
      public static const SPACE_SUIT:String = "spaceSuit";
      
      private static const lightCone:String = "lightCone";
      
      private static const rocketCate:String = "rocketCate";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var modeDiy:String = "";
      
      public var craftB:Boolean = false;
      
      public var spaceSuitB:Boolean = false;
      
      public var closeB:Boolean = false;
      
      public var decrip:String = "";
      
      public var color:String = GatherColor.bluenessColor;
      
      public var noBossSkillArr:Array = null;
      
      public var noSkillArr:Array = null;
      
      public var skillArr:Array = [];
      
      public var implodingNoPro:Number = 0;
      
      public var hitMissileNoPro:Number = 0;
      
      public var noBounceB:Boolean = false;
      
      public var rocketCateMul:Number = 1;
      
      public var armsTypeLimit:Array = null;
      
      public var noShootB:Boolean = false;
      
      public var baseChargerMul:Number = -1;
      
      public var capacityMul:Number = 1;
      
      public var caissonMul:Number = 1;
      
      public var allPropsMul:Number = 1;
      
      public var weaponMul:Number = 1;
      
      public var noWeaponB:Boolean = false;
      
      public var noDeviceB:Boolean = false;
      
      public var noVehicleB:Boolean = false;
      
      public var noPartnerB:Boolean = false;
      
      public var noPetB:Boolean = false;
      
      public var noPropsB:Boolean = false;
      
      public var noSumBossB:Boolean = false;
      
      public var mustSingleB:Boolean = false;
      
      public var ruleRangePro:Number = 0;
      
      public var enemyNoLimitB:Boolean = false;
      
      public var onlyBossB:Boolean = false;
      
      public function ModeDiyDefine()
      {
         super();
         this.lifeMul = 1;
         this.dpsMul = 1;
         this.normalLifeMul = 1;
      }
      
      public function get lifeMul() : Number
      {
         return this.CF.getAttribute("lifeMul");
      }
      
      public function set lifeMul(v0:Number) : void
      {
         this.CF.setAttribute("lifeMul",v0);
      }
      
      public function get normalLifeMul() : Number
      {
         return this.CF.getAttribute("normalLifeMul");
      }
      
      public function set normalLifeMul(v0:Number) : void
      {
         this.CF.setAttribute("normalLifeMul",v0);
      }
      
      public function get dpsMul() : Number
      {
         return this.CF.getAttribute("dpsMul");
      }
      
      public function set dpsMul(v0:Number) : void
      {
         this.CF.setAttribute("dpsMul",v0);
      }
      
      public function getWearLabel() : String
      {
         if(name == SPACE_CRAFT || name == SPACE_SUIT)
         {
            return "space";
         }
         return "wear";
      }
      
      public function getBossDropLifeB() : Boolean
      {
         if(this.craftB || this.spaceSuitB)
         {
            return false;
         }
         return true;
      }
      
      public function dealOrderLife(mapMode0:String, unitType0:String, lifeMul0:Number, mapD0:WorldMapDefine, diff0:int) : Number
      {
         var v0:Number = NaN;
         var bossSkillArr0:Array = null;
         if(mapMode0 == MapMode.DEMON)
         {
            v0 = lifeMul0;
            if(unitType0 == UnitType.BOSS)
            {
               v0 = 1.75;
            }
            else if(unitType0 == UnitType.SUPER)
            {
               v0 = 1;
            }
            else if(unitType0 == UnitType.NORMAL)
            {
               v0 = lifeMul0 * 2;
            }
            if(!this.mustSingleB && Boolean(mapD0))
            {
               if(mapD0.demBossSkillArr.indexOf("FoggyDefence") >= 0)
               {
                  if(unitType0 == UnitType.BOSS)
                  {
                     v0 *= 0.6;
                  }
               }
               if(mapD0.demSkillArr.indexOf("FoggyDefence") >= 0)
               {
                  if(unitType0 != UnitType.BOSS)
                  {
                     v0 *= 0.5;
                  }
               }
            }
            if(Boolean(mapD0))
            {
               if(unitType0 == UnitType.BOSS)
               {
                  bossSkillArr0 = mapD0.getDemBossSkillArr(diff0);
                  if(bossSkillArr0.indexOf("everSilenceEnemy") >= 0)
                  {
                     v0 *= 0.5;
                  }
                  if(bossSkillArr0.indexOf("redArmsSensitive") >= 0)
                  {
                     v0 *= 0.1;
                  }
               }
            }
            return v0;
         }
         return lifeMul0;
      }
      
      public function dealOrderDps(mapMode0:String, unitType0:String, dpsMul0:Number, mapD0:WorldMapDefine) : Number
      {
         var v0:Number = NaN;
         if(mapMode0 == MapMode.DEMON)
         {
            v0 = dpsMul0;
            if(unitType0 == UnitType.BOSS)
            {
               v0 = 1.5;
            }
            return v0;
         }
         return dpsMul0;
      }
      
      public function canMapB(mapD0:WorldMapDefine) : Boolean
      {
         if(mapD0.demNoModeArr.indexOf(name) >= 0)
         {
            return false;
         }
         if(Boolean(this.noBossSkillArr))
         {
            if(ArrayMethod.onlyOneElementSamePan(this.noBossSkillArr,mapD0.demBossSkillArr))
            {
               return false;
            }
         }
         if(Boolean(this.noSkillArr))
         {
            if(ArrayMethod.onlyOneElementSamePan(this.noSkillArr,mapD0.demSkillArr))
            {
               return false;
            }
         }
         if(!this.noPartnerB && !this.noPetB && name != CLOSE)
         {
            if(mapD0.name == "SkyScene")
            {
               return false;
            }
         }
         return true;
      }
      
      public function getCharger(da0:ArmsChargerData) : int
      {
         var d0:ArmsChargerDefine = da0.def;
         var v0:Number = d0.baseCharger;
         if(this.baseChargerMul > 0)
         {
            v0 *= this.baseChargerMul;
            if(d0.name == ArmsType.lightning)
            {
               v0 *= 0.4;
            }
            else if(d0.name == ArmsType.rifle || d0.name == ArmsType.sniper || d0.name == ArmsType.pistol)
            {
               v0 *= 1.5;
            }
         }
         return Math.ceil(v0);
      }
      
      public function getCapacityMul(da0:ArmsData) : Number
      {
         var mul0:Number = this.capacityMul;
         if(mul0 < 1)
         {
            if(da0.name == lightCone)
            {
               mul0 *= 0.7;
            }
            else if(da0.name == rocketCate)
            {
               mul0 *= 0.5;
            }
            else if(da0.armsType == ArmsType.lightning)
            {
               mul0 *= 0.5;
            }
            else if(da0.armsType == ArmsType.rifle || da0.armsType == ArmsType.pistol)
            {
               mul0 *= 1.5;
               if(mul0 > 1)
               {
                  mul0 = 1;
               }
            }
         }
         return mul0;
      }
      
      public function getPropsLimit(v0:Number, td0:ThingsDefine) : Number
      {
         if(this.caissonMul == 1 && this.allPropsMul == 1)
         {
            return v0;
         }
         if(td0.name == "caisson")
         {
            v0 = v0 * this.caissonMul - 1e-7;
         }
         return Math.ceil(v0 * this.allPropsMul - 1e-7);
      }
      
      public function getTitle(colorB0:Boolean = true, bracketsB0:Boolean = true) : String
      {
         var c0:String = null;
         var s0:String = "";
         if(this.closeB || !bracketsB0)
         {
            s0 = cnName;
         }
         else
         {
            s0 = "[" + cnName + "]";
         }
         if(colorB0)
         {
            c0 = GatherColor.getColor(this.color);
            s0 = ComMethod.color(s0,c0);
         }
         return s0;
      }
      
      public function getGatherTip() : String
      {
         var s0:String = "<b>" + this.getTitle(true,false) + "模式说明：</b>";
         return StringMethod.addNewLine(s0,this.decrip);
      }
      
      private function toArmsSimple() : void
      {
         this.implodingNoPro = 0.9;
         this.hitMissileNoPro = 0.9;
         this.noBounceB = true;
      }
      
      private function heroNoShootVehicleWeapon() : void
      {
         this.noVehicleB = true;
         this.noWeaponB = true;
         this.noShootB = true;
      }
      
      public function toNoMainAll() : void
      {
         this.noVehicleB = true;
         this.noPartnerB = true;
         this.noPetB = true;
         this.noSumBossB = true;
         this.noWeaponB = true;
         this.noDeviceB = true;
         this.mustSingleB = true;
         this.allPropsMul = 0;
      }
      
      public function setTo_close() : void
      {
         this.closeB = true;
         cnName = "未开放";
         name = CLOSE;
         this.color = GatherColor.gray;
      }
      
      public function setTo_team() : void
      {
         cnName = "团战";
         name = TEAM;
         this.color = GatherColor.blove;
         this.normalLifeMul = 2;
         this.lifeMul = 2;
         this.dpsMul = 1;
         this.toArmsSimple();
         this.noBossSkillArr = onlySingleSkillArr;
         this.noSkillArr = onlySingleSkillArr;
         this.decrip = "没有特别的限制，不过敌人的生命值和伤害相对最高。";
      }
      
      public function setTo_partner() : void
      {
         cnName = "辅助";
         name = PARTNER;
         this.color = GatherColor.purpleness;
         this.normalLifeMul = 2;
         this.lifeMul = 0.6;
         this.dpsMul = 1;
         this.toArmsSimple();
         this.heroNoShootVehicleWeapon();
         this.enemyNoLimitB = true;
         this.noBossSkillArr = noPartnerSkillArr;
         this.noSkillArr = noPartnerSkillArr;
         this.decrip = "主角不能射击，不能使用载具和副手，只能辅助队友。";
      }
      
      public function setTo_single() : void
      {
         cnName = "独战";
         name = SINGLE;
         this.color = GatherColor.redness;
         this.normalLifeMul = 2;
         this.lifeMul = 0.6;
         this.dpsMul = 0.3;
         this.noPartnerB = true;
         this.noPetB = true;
         this.mustSingleB = true;
         this.toArmsSimple();
         this.capacityMul = 0.5;
         this.baseChargerMul = 1;
         this.caissonMul = 0.5;
         this.allPropsMul = 0.6;
         this.enemyNoLimitB = true;
         this.decrip = "主角独身一人参加战斗，并且武器弹药受限。";
      }
      
      public function setTo_nocom() : void
      {
         cnName = "冷门";
         name = NOCOM;
         this.color = GatherColor.greeness;
         this.normalLifeMul = 2;
         this.lifeMul = 1;
         this.dpsMul = 0.6;
         this.toArmsSimple();
         this.capacityMul = 0.7;
         this.baseChargerMul = 1;
         this.allPropsMul = 0.6;
         this.armsTypeLimit = ArmsType.nocomArr;
         this.enemyNoLimitB = true;
         this.noBossSkillArr = onlySingleSkillArr;
         this.noSkillArr = onlySingleSkillArr;
         this.skillArr = ["BallLightningHurt"];
         this.decrip = "主角和队友只能使用以下类型的武器：<blue 狙击枪、手枪、喷火器、弩、榴弹炮、波动枪、气象枪、切割枪、闪电枪、能量枪/>，并且武器弹药受限。";
      }
      
      public function setTo_duel() : void
      {
         cnName = "决斗";
         name = DUEL;
         this.color = GatherColor.orangeness;
         this.lifeMul = 1.1;
         this.dpsMul = 0.15;
         this.noPartnerB = true;
         this.noPetB = true;
         this.mustSingleB = true;
         this.toArmsSimple();
         this.capacityMul = 0.5;
         this.baseChargerMul = 1;
         this.allPropsMul = 0;
         this.enemyNoLimitB = true;
         this.onlyBossB = true;
         this.decrip = "没有队友和小怪，主角直接和首领决斗，不能使用道具，武器弹药受限。";
      }
      
      public function setTo_limit() : void
      {
         this.normalLifeMul = 2;
         cnName = "限弹";
         name = "limit";
         this.color = GatherColor.greeness;
         this.lifeMul = 0.7;
         this.dpsMul = 1;
         this.toArmsSimple();
         this.baseChargerMul = 0.5;
         this.enemyNoLimitB = true;
      }
   }
}

