package dataAll._app.achieve.define
{
   import com.sounto.utils.ClassProperty;
   
   public class AchieveFatherDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public function AchieveFatherDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

