package dataAll.equip.define
{
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._player.role.RoleName;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.body.define.BodySex;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.define.IO_GiftDefine;
   import dataAll.items.define.IO_ItemsDefine;
   import dataAll.items.define.IO_ResolveItemsDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class EquipDefine implements IO_GiftDefine, IO_ItemsDefine, IO_ResolveItemsDefine
   {
      
      public static var FASHION_KEEP_ROLE_HEAD:Boolean = true;
      
      public static var pro_arr:Array = [];
      
      public static const headList:Array = ["head"];
      
      public static const coatList:Array = ["body","arm_left_0","arm_left_1","arm_right_0","arm_right_1","hand_left","hand_right"];
      
      public static const pantsList:Array = ["thigh","leg_left_0","leg_left_1","leg_right_0","leg_right_1","foot_left","foot_right"];
      
      public static const beltList:Array = ["belt"];
      
      public static const upperImgArr:Array = headList.concat(coatList);
      
      public static const lowerImgArr:Array = pantsList.concat(beltList);
      
      public static const allImgArr:Array = upperImgArr.concat(lowerImgArr);
      
      private static const descomposeRareArr:Array = ["ghostDukeSuit","intercessorSuit"];
      
      protected var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var index:int = 0;
      
      public var father:String = "";
      
      public var type:String = "";
      
      public var fashionPartShow:String = "";
      
      public var replaceWeaponType:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var addArmsType:String = "";
      
      public var img:Array = [];
      
      public var imgObj:Object = {};
      
      public var imgSwf:String = "";
      
      public var iconLabel:String = "";
      
      public var blackDropLevelArr:Array = [];
      
      private var _addObjJson:String = "";
      
      protected var _addObj:Object = null;
      
      private var _otherObjJson:String = "";
      
      public var skillArr:Array = [];
      
      public var sex:String = BodySex.MALE;
      
      public var hd:Boolean = false;
      
      public var onlyRole:String = "";
      
      public var description:String = "";
      
      public function EquipDefine()
      {
         super();
         this.addObjJson = "";
         this.otherObjJson = "";
         this.life = 0;
         this.itemsLevel = 1;
         this.composeMustNum = 99999;
      }
      
      public function get itemsLevel() : Number
      {
         return this.CF.getAttribute("itemsLevel");
      }
      
      public function set itemsLevel(v0:Number) : void
      {
         this.CF.setAttribute("itemsLevel",v0);
      }
      
      public function set addObjJson(v0:String) : void
      {
         this._addObjJson = Base64.encodeString(String(v0));
      }
      
      public function get addObjJson() : String
      {
         return Base64.decodeString(this._addObjJson);
      }
      
      public function set otherObjJson(v0:String) : void
      {
         this._otherObjJson = Base64.encodeString(String(v0));
      }
      
      public function get otherObjJson() : String
      {
         return Base64.decodeString(this._otherObjJson);
      }
      
      public function get composeMustNum() : Number
      {
         return this.CF.getAttribute("composeMustNum");
      }
      
      public function set composeMustNum(v0:Number) : void
      {
         this.CF.setAttribute("composeMustNum",v0);
      }
      
      public function get life() : Number
      {
         return this.CF.getAttribute("life");
      }
      
      public function set life(v0:Number) : void
      {
         this.CF.setAttribute("life",v0);
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         var n:* = undefined;
         this.father = father0;
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         var list0:Array = EquipDefine[this.type + "List"];
         if(!this.img)
         {
            this.img = list0.concat([]);
         }
         else if(this.img.length < list0.length)
         {
            for(n in list0)
            {
               if(n > this.img.length - 1)
               {
                  this.img[n] = list0[n];
               }
            }
         }
         if(!this.iconLabel)
         {
            this.iconLabel = this.type + "_icon";
         }
         if(!this.name)
         {
            this.name = father0 + "_" + this.type;
         }
         if(father0 != "")
         {
            this.addSwfFirst(father0);
         }
      }
      
      public function inFashion_byXML(xml0:XML) : void
      {
         var xx0:int = 0;
         var partsArr0:Array = null;
         var parts0:String = null;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         if(this.imgSwf == "")
         {
            this.imgSwf = this.name;
         }
         else
         {
            xx0 = 0;
         }
         this.type = "fashion";
         if(this.fashionPartShow == "")
         {
            this.img = allImgArr.concat([]);
         }
         else
         {
            partsArr0 = this.fashionPartShow.split(",");
            for each(parts0 in partsArr0)
            {
               this.img = EquipDefine[parts0 + "List"].concat(this.img);
            }
         }
         if(this.name == "neZha")
         {
            this.img.push("all");
         }
         if(this.iconLabel == "")
         {
            this.iconLabel = "fashion_icon";
         }
         this.addSwfFirst(this.imgSwf);
      }
      
      private function addSwfFirst(father0:String) : void
      {
         var n:* = undefined;
         var str0:String = null;
         for(n in this.img)
         {
            str0 = this.img[n];
            if(str0.indexOf("/") == -1)
            {
               this.img[n] = father0 + "/" + str0;
            }
            this.imgObj[str0] = this.img[n];
         }
         if(this.iconLabel.indexOf("/") == -1)
         {
            this.iconLabel = father0 + "/" + this.iconLabel;
         }
      }
      
      public function getWeaponSkinD() : ArmsSkinDefine
      {
         return Gaming.defineGroup.armsCharger.getSkin(this.name);
      }
      
      public function getArenaGoodsName() : String
      {
         return this.name + "_arena";
      }
      
      public function objInSaveB() : Boolean
      {
         if(this.type == EquipType.DEVICE || this.type == EquipType.WEAPON || this.type == EquipType.FASHION)
         {
            return true;
         }
         if(this.type == EquipType.VEHICLE)
         {
            return false;
         }
         if(this.addObjJson != "")
         {
            return true;
         }
         return false;
      }
      
      public function haveLevelB() : Boolean
      {
         return this.type != EquipType.FASHION;
      }
      
      public function dealBtnListCn(label0:String) : String
      {
         return "";
      }
      
      public function getFatherDefine() : EquipFatherDefine
      {
         return Gaming.defineGroup.equip.getFatherDefine(this.father);
      }
      
      public function getComposeThingsDefine() : ThingsDefine
      {
         return Gaming.defineGroup.things.getDefine(this.name);
      }
      
      public function getResolveGift() : GiftAddDefineGroup
      {
         var td0:ThingsDefine = null;
         var g0:GiftAddDefineGroup = null;
         var num0:int = 0;
         var f0:EquipFatherDefine = null;
         if(this.canResolveB())
         {
            td0 = this.getComposeThingsDefine();
            g0 = new GiftAddDefineGroup();
            num0 = this.composeMustNum;
            if(num0 == 99999)
            {
               f0 = this.getFatherDefine();
               if(Boolean(f0))
               {
                  num0 = f0.chipNum;
               }
               else
               {
                  num0 = 0;
               }
            }
            if(!this.getComposeProConstB())
            {
               num0 /= 2;
            }
            g0.addGiftByStr("things;" + td0.name + ";" + num0);
            return g0;
         }
         return null;
      }
      
      public function getComposeProConstB() : Boolean
      {
         var f0:EquipFatherDefine = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            return f0.composeProConstB;
         }
         return false;
      }
      
      public function canResolveB() : Boolean
      {
         var f0:EquipFatherDefine = null;
         if(EquipType.canResolveArr.indexOf(this.type) >= 0)
         {
            return this.composeMustNum > 0 && this.composeMustNum != 99999;
         }
         f0 = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            return f0.resolveB;
         }
         return false;
      }
      
      public function canRefiningB() : Boolean
      {
         if(this.canResolveB())
         {
            return false;
         }
         if(this.isCanEvoB())
         {
            return false;
         }
         var f0:EquipFatherDefine = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            if(f0.color == EquipColor.BLACK || f0.color == EquipColor.RED)
            {
               return true;
            }
         }
         return false;
      }
      
      public function canSkillAddMoveB() : Boolean
      {
         var f0:EquipFatherDefine = null;
         if(this.itemsLevel >= 86)
         {
            f0 = this.getFatherDefine();
            if(Boolean(f0))
            {
               return f0.canSkillAddMoveB();
            }
         }
         return false;
      }
      
      public function isBlack90() : Boolean
      {
         return this.isMoreRedB() && this.itemsLevel >= 86;
      }
      
      public function isDarkgoldB() : Boolean
      {
         var f0:EquipFatherDefine = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            return f0.color == EquipColor.DARKGOLD;
         }
         return false;
      }
      
      public function isDarkgoldAndMoreB() : Boolean
      {
         var f0:EquipFatherDefine = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            return EquipColor.moreDarkgoldB(f0.color);
         }
         return false;
      }
      
      public function isPurgoldB() : Boolean
      {
         var f0:EquipFatherDefine = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            return f0.color == EquipColor.PURGOLD;
         }
         return false;
      }
      
      public function isMoreRedB() : Boolean
      {
         var index0:int = 0;
         var f0:EquipFatherDefine = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            index0 = EquipColor.getIndex(f0.color);
            if(index0 >= 5)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isCanEvoB() : Boolean
      {
         var f0:EquipFatherDefine = this.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            return f0.evoB;
         }
         return false;
      }
      
      public function getAddObj() : Object
      {
         var json0:String = null;
         var str0:String = null;
         var obj0:Object = this._addObj;
         if(obj0 == null)
         {
            json0 = this.addObjJson;
            if(json0 != "")
            {
               str0 = TextWay.replaceStr(json0,"\'","\"");
               obj0 = JSON2.decode(str0);
            }
            else
            {
               obj0 = {};
            }
            this._addObj = obj0;
         }
         return obj0;
      }
      
      public function getOtherObj() : Object
      {
         var str0:String = null;
         var obj0:Object = {};
         var json0:String = this.otherObjJson;
         if(json0 != "")
         {
            str0 = TextWay.replaceStr(json0,"\'","\"");
            obj0 = JSON2.decode(str0);
         }
         return obj0;
      }
      
      public function getTrueCnName() : String
      {
         return this.cnName;
      }
      
      public function toString() : String
      {
         return this.cnName;
      }
      
      public function isNormalB() : Boolean
      {
         return EquipType.NORMAL_ARR.indexOf(this.type) >= 0;
      }
      
      public function isDescomposeRareB() : Boolean
      {
         return descomposeRareArr.indexOf(this.father) >= 0;
      }
      
      public function getSortIndex() : int
      {
         return this.index;
      }
      
      public function getImgBodyName() : String
      {
         if(this.getImgSex() == BodySex.FEMALE)
         {
            return RoleName.Girl;
         }
         return RoleName.Striker;
      }
      
      public function keepRoleHeadB() : Boolean
      {
         if(this.getImgSex() != this.getApplySex())
         {
            return false;
         }
         if(this.onlyHeadImgB())
         {
            return false;
         }
         if(this.name == "spaceSuit")
         {
            return false;
         }
         return FASHION_KEEP_ROLE_HEAD;
      }
      
      public function getImgSex() : String
      {
         return this.sex;
      }
      
      public function getApplySex() : String
      {
         if(this.name == "snowShadow" || this.name == "snowShadow2")
         {
            return BodySex.MALE;
         }
         return this.sex;
      }
      
      public function getHDShowUrl() : String
      {
         return this.imgSwf + "HD/equipStand";
      }
      
      public function getMadbossSkinSwf() : String
      {
         if(this.name == "blackGhost")
         {
            return this.imgSwf + "Skin";
         }
         return "";
      }
      
      public function onlyHeadImgB() : Boolean
      {
         return this.fashionPartShow == "head";
      }
      
      public function panRole(roleName0:String) : Boolean
      {
         if(this.onlyRole == "")
         {
            return true;
         }
         return this.onlyRole.indexOf(roleName0) >= 0;
      }
      
      private function getOnlyRoleCn() : String
      {
         var roleD0:NormalBodyDefine = null;
         var cnArr0:Array = null;
         var nameArr0:Array = null;
         var name0:String = null;
         if(this.onlyRole != "")
         {
            roleD0 = Gaming.defineGroup.body.getDefine(this.onlyRole);
            if(Boolean(roleD0))
            {
               return roleD0.cnName;
            }
            cnArr0 = [];
            nameArr0 = this.onlyRole.split(",");
            for each(name0 in nameArr0)
            {
               roleD0 = Gaming.defineGroup.body.getDefine(name0);
               if(Boolean(roleD0))
               {
                  cnArr0.push(roleD0.cnName);
               }
            }
            return StringMethod.concatStringArr(cnArr0,9);
         }
         return "";
      }
      
      public function getFashionGatherTip(surplusDay0:int = -1) : String
      {
         var str0:String = "";
         var life0:int = this.life;
         if(this.onlyRole != "")
         {
            str0 += "专属角色：<orange <b>" + this.getOnlyRoleCn() + "</b>/>";
         }
         else
         {
            str0 += "适用范围：<orange <b>" + BodySex.getCn(this.getApplySex()) + "角色</b>/>";
         }
         if(this.getApplySex() != this.sex)
         {
            str0 += "(" + BodySex.getCn(this.sex) + "外观)";
         }
         str0 += "\n覆盖范围：<green <b>" + EquipType.getCoverCn(this.fashionPartShow) + "</b>/>";
         if(surplusDay0 == -1 || life0 == 0)
         {
            str0 += "\n使用期限：<purple <b>" + (this.life == 0 ? "永久" : this.life + "天") + "</b>/>";
         }
         else
         {
            str0 += "\n剩余天数：<purple <b>" + (surplusDay0 + "天") + "</b>/>";
         }
         return str0;
      }
      
      public function getHDFashionTipAdd() : String
      {
         var s0:String = null;
         if(this.hd && this.type == EquipType.FASHION)
         {
            s0 = "\n\n<purple 这是人物高清时装，在背包中穿上该时装，或者鼠标放置人物身上，即可看到4倍高清效果；战斗中放大游戏画面也能看到高清细节。/>";
            if(this.getMadbossSkinSwf() != "")
            {
               s0 += "\n<green 变身战神时将同时替换战神皮肤。";
            }
            return s0;
         }
         return "";
      }
      
      public function getHeadIconUrl() : String
      {
         return this.imgSwf + "/head_icon";
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function getTypeCn() : String
      {
         return EquipType.getCnName(this.type);
      }
      
      public function getGoodsTip() : String
      {
         var str0:String = "";
         var obj0:Object = this.getAddObj();
         str0 += this.getFashionGatherTip() + "\n";
         if(ObjectMethod.getObjElementNum(obj0) > 0)
         {
            str0 += "\n<i1>|<blue <b>提升：</b>/>";
            str0 += "\n" + EquipPropertyDataCreator.getText_byObj(obj0,null,true);
         }
         if(this.description != "" && !this.isNormalB())
         {
            str0 += "\n<i1>|<blue <b>特殊技能：</b>/>\n" + this.description;
         }
         var skillArr0:Array = this.skillArr;
         if(skillArr0.length > 0)
         {
            str0 += "\n<i1>|<blue <b>技能：</b>/>";
            str0 += "\n" + EquipSkillCreator.getAllSkillTip(skillArr0);
         }
         return str0 + this.getHDFashionTipAdd();
      }
      
      public function getNormalGatherTip() : String
      {
         var str0:String = "";
         var obj0:Object = this.getAddObj();
         if(ComMethod.getObjElementNum(obj0) > 0)
         {
            str0 += "\n<i1>|<blue <b>提升：</b>/>";
            str0 += "\n" + EquipPropertyDataCreator.getText_byObj(obj0,null,true);
         }
         var skillArr0:Array = this.skillArr;
         if(skillArr0.length > 0)
         {
            str0 += "\n<i1>|<blue <b>技能：</b>/>";
            str0 += "\n" + EquipSkillCreator.getAllSkillTip(skillArr0);
         }
         if(this.description != null && this.description != "")
         {
            str0 += "\n\n" + this.description;
         }
         return str0;
      }
      
      public function getGiftCn() : String
      {
         return this.getTrueCnName();
      }
      
      public function getGiftTip() : String
      {
         return this.getGoodsTip();
      }
   }
}

