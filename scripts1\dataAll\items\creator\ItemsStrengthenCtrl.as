package dataAll.items.creator
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsStrengthenCtrl;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.creator.EquipStrengthenCtrl;
   import dataAll.items.IO_StrengthenData;
   import dataAll.must.define.MustDefine;
   import dataAll.ui.tip.CheckData;
   
   public class ItemsStrengthenCtrl
   {
      
      public function ItemsStrengthenCtrl()
      {
         super();
      }
      
      public static function getAfterData(da0:IO_StrengthenData) : IO_StrengthenData
      {
         if(da0 is ArmsData)
         {
            return ArmsStrengthenCtrl.getAfterData(da0 as ArmsData);
         }
         if(da0 is EquipData)
         {
            return EquipStrengthenCtrl.getAfterData(da0 as EquipData);
         }
         return null;
      }
      
      public static function strengthenOne(da0:IO_StrengthenData, oneB0:Boolean) : void
      {
         if(da0 is ArmsData)
         {
            ArmsStrengthenCtrl.strengthenOne(da0 as ArmsData,oneB0);
         }
         if(da0 is EquipData)
         {
            EquipStrengthenCtrl.strengthenOne(da0 as EquipData,oneB0);
         }
      }
      
      public static function downStrengthenOne(da0:IO_StrengthenData) : void
      {
         var lv0:int = int(da0.getStrengthenLv());
         var min0:int = getMinLv(da0);
         if(lv0 > min0)
         {
            if(da0 is ArmsData)
            {
               ArmsStrengthenCtrl.downStrengthenOne(da0 as ArmsData);
            }
            if(da0 is EquipData)
            {
               EquipStrengthenCtrl.downStrengthenOne(da0 as EquipData);
            }
         }
      }
      
      public static function getMinLv(da0:IO_StrengthenData) : int
      {
         var max0:int = int(da0.getSMaxLv());
         return getMinLvByMaxLv(max0);
      }
      
      public static function getMinLvByMaxLv(max0:int) : int
      {
         var min0:int = max0 - 8;
         var sucessLv0:int = getSucessLv();
         if(min0 < sucessLv0)
         {
            min0 = sucessLv0;
         }
         return min0;
      }
      
      public static function getSucessLv() : int
      {
         return 2;
      }
      
      public static function getSuccessRate(da0:IO_StrengthenData, oneB0:Boolean) : Number
      {
         var lv0:int = 0;
         if(oneB0)
         {
            return 1;
         }
         lv0 = int(da0.getStrengthenLv());
         if(da0 is ArmsData)
         {
            return ArmsStrengthenCtrl.getSuccessRate(lv0);
         }
         if(da0 is EquipData)
         {
            return EquipStrengthenCtrl.getSuccessRate(lv0);
         }
         return 0;
      }
      
      public static function panStrengthenB(da0:IO_StrengthenData, oneB0:Boolean) : CheckData
      {
         var c0:CheckData = null;
         if(da0 is ArmsData)
         {
            c0 = ArmsStrengthenCtrl.panStrengthenB(da0 as ArmsData);
         }
         if(da0 is EquipData)
         {
            c0 = EquipStrengthenCtrl.panStrengthenB(da0 as EquipData);
         }
         if(c0.bb)
         {
            if(oneB0)
            {
            }
         }
         return c0;
      }
      
      public static function getMust(da0:IO_StrengthenData, oneB0:Boolean) : MustDefine
      {
         if(oneB0)
         {
            return getOneMust(da0);
         }
         return getNormalMust(da0);
      }
      
      private static function getNormalMust(da0:IO_StrengthenData) : MustDefine
      {
         var chargerD0:ArmsChargerDefine = null;
         var d0:MustDefine = new MustDefine();
         var mustMul0:Number = Number(da0.getStrengthenMustMul());
         d0.lv = 50;
         var num0:int = Gaming.defineGroup.equip.getStrengthenMustNum(da0.getSave().getTrueLevel(),da0.getStrengthenLv());
         var thingArr0:Array = [];
         var armsDa0:ArmsData = da0 as ArmsData;
         if(Boolean(armsDa0))
         {
            chargerD0 = Gaming.defineGroup.armsCharger.getDefine(armsDa0.armsType);
            num0 += chargerD0.strengthenMustAdd;
         }
         num0 = Math.ceil(num0 * mustMul0);
         d0.inThingsDataByArr(["strengthenStone;" + num0].concat(thingArr0));
         return d0;
      }
      
      private static function getOneMust(da0:IO_StrengthenData) : MustDefine
      {
         var chargerD0:ArmsChargerDefine = null;
         var d0:MustDefine = new MustDefine();
         var mustMul0:Number = Number(da0.getStrengthenMustMul());
         d0.lv = 50;
         var sn0:Number = Gaming.defineGroup.equip.strengthen.getPropertyValue("oneNum",da0.getStrengthenLv() + 1);
         var oneNum0:int = Gaming.defineGroup.equip.getStrengthenMustNum(da0.getSave().getTrueLevel(),da0.getStrengthenLv());
         var armsDa0:ArmsData = da0 as ArmsData;
         if(Boolean(armsDa0))
         {
            chargerD0 = Gaming.defineGroup.armsCharger.getDefine(armsDa0.armsType);
            oneNum0 += chargerD0.strengthenMustAdd;
         }
         var stoneNum0:int = Math.round(oneNum0 * sn0 * mustMul0);
         var thingsArr0:Array = ["strengthenStone;" + stoneNum0];
         var obj0:Object = getEchelonThingsName(da0);
         var cardOneNum0:int = getEchelonNum(da0.getStrengthenLv(),da0.getSMaxLv()) * obj0.mul;
         var cardNum0:int = Math.round(cardOneNum0 * sn0 * mustMul0);
         if(cardNum0 > 0)
         {
            thingsArr0.push(obj0.name + ";" + cardNum0);
         }
         d0.inThingsDataByArr(thingsArr0);
         return d0;
      }
      
      public static function getEchelonMust(da0:IO_StrengthenData) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var mustMul0:Number = Number(da0.getStrengthenMustMul());
         var sLv0:int = int(da0.getStrengthenLv());
         var num0:int = getEchelonNum(sLv0,da0.getSMaxLv());
         var obj0:Object = getEchelonThingsName(da0);
         var thingsName0:String = obj0.name;
         num0 = Math.round(num0 * obj0.mul * mustMul0);
         if(num0 > 0)
         {
            d0.inThingsDataByArr([thingsName0 + ";" + num0]);
         }
         return d0;
      }
      
      private static function getEchelonThingsName(da0:IO_StrengthenData) : Object
      {
         var obj0:Object = {};
         var sLv0:int = int(da0.getStrengthenLv());
         var armsB0:Boolean = da0 is ArmsData;
         var thingsName0:String = "";
         var mul0:Number = 1;
         if(sLv0 <= 14)
         {
            thingsName0 = armsB0 ? "armsEchelonCard" : "equipEchelonCard";
         }
         else if(sLv0 <= 20)
         {
            thingsName0 = armsB0 ? "highArmsEchelonCard" : "highEquipEchelonCard";
            mul0 = 1 / 10;
         }
         else if(sLv0 <= ItemsStrengthenCtrl.getEchelonMaxLevel())
         {
            thingsName0 = armsB0 ? "ultiArmsEchelonCard" : "ultiEquipEchelonCard";
            mul0 = 1 / 20;
         }
         obj0.name = thingsName0;
         obj0.mul = mul0;
         return obj0;
      }
      
      public static function canEchelonB(da0:IO_StrengthenData) : Boolean
      {
         return Gaming.PG.save.setting.echelonB && da0.getSave().getStrengthenLv() <= getEchelonMaxLevel();
      }
      
      public static function getEchelonMaxLevel() : int
      {
         return 26;
      }
      
      public static function getEchelonNum(strengthenLv0:int, sMaxLv0:int) : int
      {
         var num0:int = 0;
         var minLv0:int = getMinLvByMaxLv(sMaxLv0);
         if(strengthenLv0 <= getEchelonMaxLevel() && strengthenLv0 > minLv0)
         {
            num0 = Gaming.defineGroup.equip.strengthen.getPropertyValue("echelonNum",strengthenLv0 + 1);
         }
         return num0;
      }
   }
}

