package dataAll.items.creator
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.ArmsData;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.save.ItemsSave;
   import dataAll.must.define.MustDefine;
   
   public class ItemsStrengthenMoveCtrl
   {
      
      public function ItemsStrengthenMoveCtrl()
      {
         super();
      }
      
      public static function canMoveB(b0:IO_ItemsData, a0:IO_ItemsData) : String
      {
         var bs0:ItemsSave = b0.getSave();
         var as0:ItemsSave = a0.getSave();
         var type0:String = b0.getDataType();
         var childType0:String = b0.getSave().getChildType();
         var tip0:String = "";
         if(noStrengthenMoveB(a0) || noStrengthenMoveB(b0))
         {
            if(as0.color != bs0.color)
            {
               tip0 = "都是" + EquipColor.getLongCn(as0.color) + "物品才能强化转移";
            }
         }
         if(tip0 == "")
         {
            if(type0 != a0.getDataType())
            {
               tip0 = "目标物品必须是" + bs0.getChildTypeCnName();
            }
            else if(Math.abs(bs0.getTrueLevel() - as0.getTrueLevel()) > 5)
            {
               tip0 = "两个物品等级差不能超过5级";
            }
            else if(bs0.getStrengthenLv() == as0.getStrengthenLv())
            {
               tip0 = "两个物品强化等级不能一样";
            }
         }
         if(tip0 != "")
         {
            tip0 = ComMethod.color(tip0,"#FF3838");
         }
         return tip0;
      }
      
      private static function noStrengthenMoveB(da0:IO_ItemsData) : Boolean
      {
         var ada0:ArmsData = da0 as ArmsData;
         if(Boolean(ada0))
         {
            if(EquipColor.noStrengthenMoveB(ada0.def.color))
            {
               return true;
            }
         }
         return false;
      }
      
      public static function getMust(b0:IO_ItemsData, a0:IO_ItemsData) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var bs0:ItemsSave = b0.getSave();
         var as0:ItemsSave = a0.getSave();
         var blevel0:int = bs0.getTrueLevel();
         var alevel0:int = as0.getTrueLevel();
         var bstrenLv0:int = bs0.getStrengthenLv();
         var astrenLv0:int = as0.getStrengthenLv();
         var lv0:int = alevel0 > blevel0 ? alevel0 : blevel0;
         var strenLv0:int = astrenLv0 > bstrenLv0 ? astrenLv0 : bstrenLv0;
         var minStrenLv0:int = astrenLv0 < bstrenLv0 ? astrenLv0 : bstrenLv0;
         var stoneNum0:int = Gaming.defineGroup.equip.getSumStrengthenMustNum(lv0,strenLv0,minStrenLv0,true) / 4;
         var echelonNum0:int = getEchelonNum(strenLv0);
         var echelonName0:String = b0.getDataType() + "EchelonCard";
         if(a0.getSave().getChildType() != b0.getSave().getChildType())
         {
            stoneNum0 *= 2;
            echelonNum0 *= 2;
         }
         var thingsArr0:Array = ["strengthenStone;" + stoneNum0];
         if(echelonNum0 > 0)
         {
            thingsArr0.push(echelonName0 + ";" + echelonNum0);
         }
         d0.lv = 50;
         d0.coin = 0;
         d0.inThingsDataByArr(thingsArr0);
         return d0;
      }
      
      private static function getEchelonNum(maxStrenLv0:int) : Number
      {
         if(maxStrenLv0 > 20)
         {
            return 30;
         }
         if(maxStrenLv0 > 15)
         {
            return 10;
         }
         return 0;
      }
      
      public static function move(b0:IO_ItemsData, a0:IO_ItemsData) : void
      {
         var bs0:ItemsSave = b0.getSave();
         var as0:ItemsSave = a0.getSave();
         var bstrenLv0:int = bs0.getStrengthenLv();
         var astrenLv0:int = as0.getStrengthenLv();
         bs0.setStrengthenLvAndMax(astrenLv0);
         as0.setStrengthenLvAndMax(bstrenLv0);
      }
   }
}

