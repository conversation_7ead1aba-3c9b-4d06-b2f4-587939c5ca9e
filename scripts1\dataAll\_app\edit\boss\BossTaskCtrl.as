package dataAll._app.edit.boss
{
   import dataAll.body.define.NormalBodyDefine;
   
   public class BossTaskCtrl
   {
      
      private static var nameIndex:int = 0;
      
      private static const weekFirst:int = 91;
      
      private static const GAP:int = 4;
      
      private static var ARR:Array = null;
      
      private static var OBJ:Object = {};
      
      private static const arr:Array = [];
      
      private static const outArr:Array = [];
      
      public function BossTaskCtrl()
      {
         super();
      }
      
      public static function initData() : void
      {
         var d0:BossTaskDefine = null;
         var start0:String = null;
         var end0:String = null;
         if(ARR == null)
         {
            ARR = [];
            start0 = "";
            end0 = "";
            start0 = "2022-11-7";
            end0 = "2022-11-13";
            d0 = new BossTaskDefine();
            d0.name = "72";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwVwbgYj6b6Z6IieKuWkqSrlubvlhrADbgYbWm9tYmllU29sZGllcgV1ZAYZMzY1MTEyMDYxNF8wBWx2BGMFdmUDBW1wBgtMdlNlbgVkcAQeBXByAwVwZQMFbGkEBQVzawkRAQYJMjFfMgYJNDFfMQYJNDFfNAYJNDJfMwYJMzNfMwYJMjNfMgYJMzRfOQYLMTJfMTEFc2kDBXRtBB4B";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "73";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBB4DbgYTQmxhY2tMYWVyBXNrCQ8BBgsxMl8xNAYLMTJfMTEGCTEyXzkGCTEzXzMGCTExXzEGCTM0XzgGCTMzXzUFdG0EbgV1ZAYXMjYwMjc5MjY1XzAFbGkEhFgFbHYEYwVwYQMFcG4GMeOAkOiZmuepuuS4reeahOW5u+ixoeOAkQVtcAYLRGlYaWEB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "74";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRMBBgk0NF80BgkxMl84BgszM18xMAYJMTNfNAYJMTFfMQYJNDJfNQYJNDNfMQYJMjFfOAYLMTJfMTEFcG4GKeOAjue+iOe1huOAj8K35bGx5rK7A24GE1pvbWJpZUZhdAVwYQMFc2kDBXRtBGoFdWQGGTExNzc1Njk1MjVfMAVwcgIFbXAGC0RpWGlhBXBlAwVkcAQeBWx2BGMFbGkEgXoB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "75";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GD0dhaWFGaXQFdmUDBXNpAwVtcAYRU2t5U2NlbmUFcGUDBXRtBB4FdWQGGTIzNDM3MDAzMjBfMAVsaQSBpAcFc2sJGQEGCTIzXzIGCTMxXzEGCTQzXzEGCTQxXzEGCTIxXzcGCTIyXzcGCTMxXzYGCTM0XzQGCTEzXzUGCTIyXzEGCTQxXzIGCTQyXzMFcG4GI+m+meiInirlpKkq5pyo5pyoBXByAwVkcAQeBXBhAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "76";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwNuBgdCYXQFdWQGGTM1MjY4MDY2ODBfMAVsdgQoBXZlAwVwZQMFbXAGF0ZseVNreVNjZW5lBXNpAwV0bQRaBXBhAwVzawkLAQYJNDRfMwYJNDFfMQYLMzNfMTAGCTQyXzMGCTMxXzUFcG4GDeeOuueOpQVsaQQBAQ==";
            deal2(d0);
            start0 = "2022-11-14";
            end0 = "2022-11-20";
            d0 = new BossTaskDefine();
            d0.name = "77";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBIEWBXNrCQsBBgkxMV8xBgkyMV84BgkxM18zBgsxMl8xMQYJMzVfMQVwbgYp5Ya35b+DwrfjgI7lraTni6zjgI8Fc2kDBWRwBB4DbgYTWW91dGhXb2xmBXBhAwV1ZAYXODk2NzMyODEyXzcFbHYEYwV2ZQMFbXAGDVhpbmdHdQVwcgMFcGUDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "78";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMFc2sJCwEGCTExXzEGCzIxXzEwBgk0Ml81BgszMV8xMAYJMjNfOAV0bQQeBWNuBg3mt7fmsowFc2kDBXBlAwNuBhdMYXN0ZGF5VGFuawVsaQSfIAVwbgYx44CO5YKy5LiW44CP5LiA6aqR57ud5bCYBXZlAwVtcAYTSG9zcGl0YWw1BWRwBB4FcHIDBXVkBhkyNTc5NzYzNDM4XzcFcGEDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "79";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkxMTg2MjE4MzU1XzADbgYLQVRpYW4FcG4GMee6teaoquOAkOKYoOOAkem7keeZvuWQiAVwcgMFc2sJGQEGCTQ0XzIGCTMyXzEGCTMyXzUGCTMyXzIGCTMyXzMGCTMxXzYGCzEyXzEyBgkzNF85BgkxM184BgkxM185BgkzNV8zBgkxM18zBXBhAwVjbgYN5ZKM6LCQBWxpBIUaBXZlAwVzaQMFcGUCBXRtBFoFZHAEHgVtcAYXRmx5U2t5U2NlbmUB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "80";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GGUZpZ2h0U2hvb3RlcgVzaQIFdWQGGTI5OTM2NjM4ODRfMAVsdgRjBXZlAwVwYQMFbXAGEVNreVNjZW5lBWRwBB4FcGUDBXByAwVsaQSDdAVzawkRAQYJMTJfNQYJMTJfNgYLMTJfMTQGCzEyXzEzBgsxMl8xMgYJMTJfMwYJMTJfMQYJMzFfNQVwbgYf6IOh5qGD5pyo5qO65p2QBWNuBg3ni4LmgJIFdG0EgTQB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "81";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhc2MDc4NzYxMzhfMAVwbgYp4pek5byR5p2A4peiwrfojInlspoFdmUDBXNrCQ0BBgkxM180BgkxM18zBgkxM18xBgkzMV84BgkzMl8xBgkxMV8xBXNpAwNuBhVTYWJlclRpZ2VyBXBhAwVtcAYNWGluZ0d1BWxpBIdoBWRwBB4FdG0EHgVwcgIFbHYEYwVwZQMB​";
            deal8(d0);
            start0 = "2022-11-21";
            end0 = "2022-11-27";
            d0 = new BossTaskDefine();
            d0.name = "1";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBh/nlq/ni4LlsaDlsLjmiYsFdWQGGTIxNzQ0ODU3NDBfMgVzawkdAQYJNDRfNQYLMzNfMTIGCTM1XzEGCTExXzEGCzEyXzE0BgsxMl8xMQYJMTJfNQYJMTJfNwYJMjFfMgYJMzFfNgYJNDFfMQYJNDFfMgYJNDFfMwYJNDJfNQV0bQQKBXNpAwVkcAQeBW1wBhVQcmlzb25Eb29yBXBhAwV2ZQMFbHYEYwVwcgMDbgYVUGlwZVpvbWJpZQVwZQMFbGkEho0gAQ==";
            deal1(d0);
            d0 = new BossTaskDefine();
            d0.name = "2";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMFdWQGGTI3MzQzNjYwNDNfMAV2ZQIFaGICBWxpBIJeBWNuBg3lnLDni7EFcGUDA24GEVdhdGNoZG9nBXNrCREBBgkxM184BgsxM18xMAYJMjFfOAYJMzRfOAYJMjJfNwYJMjFfMgYJMjJfNQYJMzFfNQVwYQMFcHIDBW1wBhNIb3NwaXRhbDEFZHAEHgVwbgYx44CQ6KeJ6YaS44CR5YWr6YeN56We5a2QBXNpAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "3";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBIaNIAVjbgYN57q56KGABXBhAwVzawkbAQYJNDRfNQYLMzNfMTEGCTEzXzUGCTEzXzIGCzIyXzEzBgkxMl83BgkyMl84BgkyMV8yBgkyMV80Bgk0Ml81Bgk0MV80BgsyM18xMQYJMTFfMgV0bQQ8BXByAwVkcAQeBXBlAwNuBhVCb29tU2t1bGxTBW1wBhtIb3NwaXRhbFVuZGVyBWx2BGMFdWQGGTE0MjM1NTY1NTRfMQVwbgYTSEZD5LiN5ZemBXZlAwVzaQMB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "4";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwVsdgRjA24GGVR5cGhvb25XaXRjaAVkcAQeBXByAwV0bQQeBW1wBglXb1R1BXBlAwV1ZAYZMzc0MTExODg2MV8wBXBuBg3lkK/mmI4FbGkEAQVzawkRAQYJMTJfMwYLMzNfMTAGCTM1XzEGCTQyXzUGCTEzXzgGCTIxXzEGCTM1XzMGCzEzXzExBXBhAwVzaQMB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "5";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBDwFYXIJAwEGFWZpcnN0UmlmbGUDbgYPWmFuZ1NoaQVwbgYr5ri45L6g44CO6b6N44CP5pyA5by6BXByAwVwZQMFc2sJDwEGCTQzXzEGCTEzXzYGCTEyXzYGCzEyXzEyBgsxMl8xNAYJMTNfNQYJMzNfMgVtcAYNWGluZ0d1BXNpAwVkcAQeBWx2BGMFdWQGGTM2NTYyODk4NzRfMQV2ZQMFcGEDAQ==";
            deal8(d0);
            start0 = "2022-11-27";
            end0 = "2022-12-4";
            d0 = new BossTaskDefine();
            d0.name = "6";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBB4FZHAEHgNuBhlGaWdodFNob290ZXIFbGkEAgVwcgMFcGUDBXBuBhnlhq/omZrlvqHpo44Fc2sJEQEGCTEzXzgGCTEzXzUGCTM0XzEGCTM1XzEGCTM1XzUGCTM0XzQGCTMxXzEGCTM1XzMFbXAGF0ZhbmdaaG91VG9wBXZlAwVsdgRgBXVkBhkzMTgxNzIyNzI2XzEFcGEDBXNpAwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "7";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwVwYQMFc2sJDwEGCTQxXzQGCTMxXzgGCTEzXzgGCzEzXzEwBgkyMV84BgkyMV8yBgk0NF81BWxpBJ8gBW1wBhNIb3NwaXRhbDUFcHIDA24GGVpvbWJpZVNpbHZlcgVwZQMFZHAEHgV0bQQKBWx2BGMFdWQGGTE4MDQzODU1OTBfMAVoYgMFcG4GJeOAjuWNk+i2iuOAj+WGt+mUiwVzaQMB";
            deal1(d0);
            d0 = new BossTaskDefine();
            d0.name = "8";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GH1ZpcnR1YWxTY29ycGlvbgVzaQMFdWQGGTI0ODkxNTY3MzhfMAVsdgRjBXZlAwVtcAYVTWFpblVwbGFuZAVkcAQeBXByAwVsaQSHaAVzawkNAQYJMTJfNwYJMTNfMwYJMjFfNAYJMzRfOQYJMzFfNQYJMzFfOAVwbgYN6ZyN6ZyHBXRtBFAB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "9";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhc4MjQ2MTk0ODdfMAV2ZQMFcG4GKeKXpOW8keadgOKXosK35Y2B5LqUBXBhAwVzaQMFc2sJGQEGCTEyXzIGCTEzXzMGCTEyXzkGCTEyXzMGCTExXzEGCTIzXzYGCTMyXzYGCTMxXzUGCTEyXzUGCTM0XzcGCTMyXzMGCTMxXzQFdG0EHgVwcgMFcGUDBWx2BGMFY24GDeaatOW+kgVkcAQeA24GD0dhaWFGaXQFbGkEh2gFbXAGCVdvVHUB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "10";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GF0FpcmNyYWZ0R3VuBWRwBB4FcHIDBXBlAwVtcAYVUHJpc29uRG9vcgV0bQQoBXZlAwVwYQMFbGkEhFgFbHYEYwVoYgMFc2kDBXBuBg3pmLPlhYkFdWQGGTMzMzg4OTcyODBfMwVjbgYN57ud5ZOuBXNrCR0BBgkyMl80BgsyM18xMQYLMzRfMTQGCzM0XzEzBgkzM18yBgkxMl81BgkyMl82BgkxM180BgkyMV84Bgk0MV81BgsxM18xMAYJMjJfOQYLMTJfMTQGCTEyXzcB";
            deal8(d0);
            start0 = "2022-12-5";
            end0 = "2022-12-11";
            d0 = new BossTaskDefine();
            d0.name = "11";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwVsdgRjBWRwBB4FcHIDBXBlAwVsaQQKBW1wBg9MYW5nWHVlBXVkBhc4OTY3MzI4MTJfNwNuBg1GbHlGaXQFcG4GKeWGt+W/g8K344CO5a2k54us44CPBXNpAwVzawkXAQYJNDFfMgYJNDRfNQYJMjFfMgYJNDFfMQYJMzJfOAYJMTJfNwYJMjJfOAYLMjFfMTAGCTEyXzMGCTEzXzEGCTQxXzUFcGEDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "13";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwVzawkbAQYJNDFfNAYJMzNfMgYJMzJfMgYLMTJfMTEGCTExXzEGCTEyXzIGCzMxXzEyBgkxMl81BgsxMl8xMAYLMTJfMTMGCzEyXzEyBgkxM180Bgk0NF8yA24GD0dhaWFGaXQFbGkEh2gFcHIDBWx2BGMFdWQGGTEzNDg1ODAzNDhfMgVkcAQeBXBlAwVwbgYZ5aKo5p+T57q45rC0BXZlAwV0bQQ8BXNpAwVoYgIFbXAGCVdvVHUB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "14";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwVzaQMFY24GB+mjjgVwbgYr6aOO6aqR44CO5Lm+44CP6LWk6b6NBXNrCRkBBgkxMV8xBgkzM184BgkzMV84BgkzMV8yBgkzMl80Bgk0MV81Bgk0Ml8xBgk0M18xBgk0NF8zBgk0MV8xBgk0MV8yBgszNF8xNAVsaQQKBW1wBglXb1R1A24GDUZseUZpdAVwcgMFcGUDBWx2BGMFdWQGGTM2NDMxMjI4NDlfMAV1ZQMB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "15";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCQ8BBgkzM185BgkxMV8xBgkyMV82BgsxMl8xNAYJMTJfNQYJMzVfNgYJNDJfMwVwbgYl44CO5rC45oGS44CP5aSc6LevA24GE0JsYWNrTGFlcgVwcgMFbHYEYwV1ZAYZMTAxMDUyMDI1OV8wBXZlAwVtcAYLRGlYaWEFZHAEHgVwYQIFY24GDeawuOaBkgVsaQSePAE=";
            deal8(d0);
            start0 = "2022-12-12";
            end0 = "2022-12-18";
            d0 = new BossTaskDefine();
            d0.name = "16";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBB4DbgYRUGhhbnRvbVgFcHIDBWxpBIMaBXBlAwVtcAYTSGFuR3VhbmcyBXZlAwVzawkPAQYJMzNfOAYJMTNfMQYJMjJfOAYJMTNfOAYJMzFfOAYJMjFfNwYJMjFfOAVzaQMFcG4GJeOAjuerpeivneOAj+WtpOW9sQVsdgRjBXVkBhkyOTkyOTc0ODY2XzAFZHAEHgVwYQMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "17";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBDwFZHAEHgNuBhlUeXBob29uV2l0Y2gFbGkEho0gBXNpAwVwZQMFbHYEYwVwYQMFc2sJEwEGCTEzXzEGCTEzXzIGCTEzXzMGCTEzXzQGCTEzXzUGCTQ0XzUGCzMzXzEyBgk0Ml81BgkxMV8xBXBuBiPpvpnoiJ4q5aSpKuW5u+WGsAVtcAYJV29UdQVwcgMFdmUDBXVkBhkzNjUxMTIwNjE0XzAB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "18";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwVsdgRjA24GGVpvbWJpZUJhdHRsZQVwYQMFbGkEho0gBXRtBDIFZHAEHgVwbgYl44CO5pel6Zmo44CP54Gr5pifBXByAwVwZQMFbXAGCVdvVHUFaGIDBXNrCRUBBgkxMV8xBgk0NF81BgsyMl8xMgYLMjJfMTMGCzMzXzEzBgszM18xNAYLMTJfMTEGCTQyXzUGCzMxXzEzBgkxNF81BXVkBhkxNDc5ODExNDYxXzAFc2kDAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "19";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwNuBhdCbGFja1RpdGFucwV1ZAYZMTU0NjQwNzUyOF8wBXByAwVwZQMFZHAECgV2ZQMFbHYEYwVsaQRGBW1wBh1GYW5nWmhvdVNlY29uZAVzaQMFc2sJEQEGCzEzXzExBgkxM18xBgkxM18zBgkxMl8zBgkxMl81BgkxMV8xBgkxMV8zBgkzMl8xBXBuBhUyMjc1MzQ0NTYxAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "20";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwVwZQMDbgYTR2hvc3REdWtlBXVkBhkxOTAxMjUxNTUyXzAFc2sJEQEGCTIxXzgGCTMxXzUGCzEzXzEwBgkyMl83BgsyM18xMQYJMTNfNQYJMzFfOAYJMzRfNAVzaQMFdG0EgRYFbGkEgRYFbXAGDVhpbmdHdQVwYQMFdmUDBWx2BGMFZHAEDwVwbgYZ5pe25LmL5a2k5b+GAQ==";
            dealNormal(d0);
            start0 = "2022-12-19";
            end0 = "2022-12-25";
            d0 = new BossTaskDefine();
            d0.name = "21";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBinil6TlvJHmnYDil6LCt+WkqeaikwVtcAYRU2t5U2NlbmUFbHYEYwVkcAQKBXBhAwVzaQMFbGkEpwgDbgYPV2FycmlvcgVzawkPAQYJNDRfMQYJNDFfMgYJNDFfMQYLMzRfMTAGCzIzXzEwBgkyMl83BgkzNF80BXRtBB8FcGUDBXVkBhkxMTE2OTQzMDg1XzQFdmUDBXByAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "22";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAgNuBgtBVGlhbgVkcAQPBXBlAwV2ZQMFdWQGGTE0MDUwMjk1ODlfMAVwcgMFY24GDeaXtuepugVzaQIFbHYEYwVwbgYl5a+S5pum4oC75pq06aOO6ZuqBWxpBIEWBWFyCQMBBg1hcmNIb3cFc2sJHQEGCzMxXzEyBgkzMl84BgkzMl8yBgkyMl85BgkxM184BgkyM18zBgk0Ml81BgkzNF80BgkzMV84BgkyMl82BgkxMl8zBgkzMV8zBgkzMl83BgkxMl80BXRtBIIsBW1wBhtIb3NwaXRhbFVuZGVyAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "23";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBB4Fc2sJEwEGCzEzXzEwBgkxNF8xBgkxMl8yBgsxMl8xMgYLMTJfMTMGCTEyXzkGCzEzXzExBgkzNF83BgkzMV81A24GDU1hZG1hbgVwbgYr54us5p2l44CO5r2H44CP6JGt6I6pBWxpBIN0BWFyCQMBBhlleHRyZW1lTGFzZXIFZHAEHgVjbgYN5rWp5YqrBW1wBg1aaHVUb3UFbHYEYwV1ZAYXNjU3Nzg0ODYxXzAB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "24";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBB4FbXAGCVdvVHUDbgYNRmx5Rml0BXBlAwVsaQSBUgVwcgMFY24GDeeLguaAkgVwbgYl5Yaw5qKm44CO5oSa6ICF44CPBXNpAwVzawkZAQYJMTFfMQYJMTJfMwYJMTJfNQYLMTJfMTMGCTEyXzEGCzEyXzEwBgkxMl82BgkyMV83BgsxMl8xMQYJMzVfMQYJMTJfOQYJMzRfNQV2ZQMFcGEDBWRwBB4FdWQGFzcwNTU0OTk3Ml8wAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "25";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwVkcAQeA24GE0dob3N0RHVrZQVsdgRVBXRtBF8FdWQGGTE2OTk3NjA0NThfMAVsaQSFbgVwZQMFc2sJDQEGCTM0XzcGCTMxXzYGCTIxXzgGCTMxXzMGCTM1XzMGCTMzXzEFcG4GGeS4g+aciOS4juaeqwVtcAYbUHJpc29uT3V0c2lkZQE=";
            dealNormal(d0);
            start0 = "2022-12-26";
            end0 = "2022-12-31";
            d0 = new BossTaskDefine();
            d0.name = "26";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBIE0BWRwBBQDbgYPS25pZ2h0cwVzaQMFbGkEho0gBXVkBhkxOTk5OTU1MzM4XzAFcGEDBXBuBh/ojaPlvZLimIbpo5jpm7YFc2sJDwEGCTQ0XzUGCTIxXzIGCTEyXzMGCTQxXzQGCTQyXzUGCTIyXzcGCzMzXzEyBXByAwVtcAYXRmFuZ1pob3VUb3AFdmUDBWx2BGMFcGUDAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "27";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GF0JsYWNrVGl0YW5zBXNpAwV2ZQMFdWQGGTI2NTczNjM3ODNfMgVsdgRjBXByAwVwZQMFbGkEUAVwYQMFcG4GH+m4rem4reaYr+S4quWxkQVzawkLAQYJMzNfMgYJMzJfMQYJNDFfNAYJMTFfMQYJMjJfNAVtcAYdRmFuZ1pob3VTZWNvbmQB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "28";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhNIb3NwaXRhbDEFdWQGGTMwODQyMTQ4MzFfMAVsdgRjA24GEVdhdGNoZG9nBWxpBAUFc2kDBWRwBB4FcGEDBWNuBg3msLjmgZIFcGUDBXBuBglERkZEBXNrCRMBBgkxM18zBgkxM181BgkxMl8yBgsxMl8xMQYLMzRfMTQGCTMyXzEGCTMzXzEGCzIzXzExBgkzNF85AQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "29";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwNuBhNHaG9zdER1a2UFZHAEHgVwZQIFdWQGGTE4MzA2MzY0MTVfMwVwcgMFdmUCBWxpBIJeBXNpAwVtcAYTSG9zcGl0YWw1BXBuBi3jgJDmoqbojrnjgJHnoqfok51Y5pifBWFyCQMBBhdyaWZsZUhvcm5ldAVzawkRAQYJMTJfOQYJMzFfOAYJMTJfNQYLMTJfMTEGCTEyXzMGCTEyXzEGCTEyXzYGCzEyXzEyBWNuBg3ku7Loo4EB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "30";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwNuBhdBaXJjcmFmdEd1bgVwYQMFbGkElzgFbXAGCVdvVHUFcG4GMea1iumFkuOAjuOBgeOAj+S5iOS5iOm4oQVsdgRjBXBlAwVoYgMFc2sJGQEGCTExXzEGCTIxXzIGCTM0XzQGCTM0XzcGCzM0XzE0BgkzNV8xBgkzNF8yBgk0M18xBgkxMl8xBgkxMl8zBgkxMl81BgkxM180BWNuBg3njovlh4QFcHIDBXVkBhkyNDk4NTcwMzI4XzAFZHAEHgVzaQMB";
            deal8(d0);
            start0 = "2023-1-1";
            end0 = "2023-1-8";
            d0 = new BossTaskDefine();
            d0.name = "31";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwVtcAYNWGluZ0d1BWx2BGMFdmUDBXVkBhkzNTk0MjI5NTQ1XzEFcGEDBXBuBg3ntIXpvo0DbgYXQmxhY2tUaXRhbnMFaGIDBXBlAgVjbgYN54uC5oCSBXNrCREBBgkzNV8xBgkyMV8yBgkxMV8xBgkzMV85BgkzMV84Bgk0Ml81BgkzNF85BgkzNF80BWxpBJgjBXByAgV0bQSCLAVkcAQBAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "32";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBgtTaGFNbwV2ZQMFcG4GDeaZg+aZgwVwYQMFcHIDBWx2BGMFcGUDA24GE0ZpZ2h0V29sZgV0bQQ8BXNrCQ8BBgkyMV8yBgkzMV81BgszM18xMAYJMzRfMQYJMzVfMQYJNDJfMwYJNDFfMQV1ZAYXNjU4ODA2NTU2XzEFc2kDBWxpBIFhAQ==";
            deal6(d0);
            d0 = new BossTaskDefine();
            d0.name = "33";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwVsaQQCBXBlAwNuBhNWYW5pdHlLZXIFcG4GHeOAjmtpdmVu44CP6Km5BXNrCQ8BBgkzNF8xBgkzNF80BgkzMV8xBgkyMV8yBgkzNV8zBgkyMV85BgkyMl84BXNpAwVtcAYLWHVXdTEFZHAEHgVwYQMFdWQGGTI3ODgyMzg1NTJfMAV0bQRaBXZlAwVsdgRjAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "34";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAgNuBhlab21iaWVCYXR0bGUFZHAEHgVzaQMFbXAGE0hvc3BpdGFsNQV0bQR9BXZlAgVwYQMFY24GDemtguelngV1ZAYZMjgxMTMzOTA3M18wBXBuBhUyNTM4MDI4MzE3BWxpBAoFcGUDBXNrCRUBBgkxM184BgkxM185BgkxMV8xBgkzMl8xBgkzMV81BgkyMV83BgsyMl8xMQYLMzFfMTIGCTEzXzEGCzIyXzEyBWx2BGMB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "35";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMDbgYRV2F0Y2hkb2cFc2sJEwEGCTEzXzQGCTEzXzEGCTQxXzEGCTQxXzIGCTQxXzMGCTQxXzQGCTQxXzUGCTE0XzUGCTQzXzEFbXAGE0hvc3BpdGFsMQVkcAQeBXVkBhkzNzY5OTY1ODM1XzAFcG4GKeOAjue+iOe1huOAj8K35piO5pyIAQ==";
            dealNormal(d0);
            start0 = "2023-1-9";
            end0 = "2023-1-15";
            d0 = new BossTaskDefine();
            d0.name = "36";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwV0bQRkBXByAwV1ZAYZMjE3MzQ0MTAxN18wBWxpBIaNIAVtcAYTSG9zcGl0YWw1BWx2BGMDbgYZWm9tYmllQmF0dGxlBWRwBB4Fc2sJEQEGCTIzXzcGCTQ0XzEGCTQ0XzIGCzMxXzEwBgkzMl8xBgsxM18xMAYJMzFfNQYJMzRfNAVwbgYN5bCP54aKAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "37";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBAEFc2kDBW1wBglGdU11BWx2BCgFdWQGGTI4NzQwMjk5NzRfMAV2ZQMFcGEDBXBuBjHjgJDnpZ7pvpnop4nphpLjgJHlpKnpnLgDbgYHQmF0BWxpBIaNIAVwcgMFcGUDBXNrCQ8BBgk0NF8zBgk0Ml8zBgszM18xMAYJMzVfMQYJNDFfMQYJMzFfNgYJMzFfNQV0bQQeAQ==";
            deal2(d0);
            d0 = new BossTaskDefine();
            d0.name = "38";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GE1pvbWJpZUZhdAVwYQMFcG4GGeaXtuS5i+Wcn+ixhgV1ZAYZMjM2NTAzMTI2MV8xBWxpBAUFcHIDBXBlAwVsdgRjBW1wBglXb1R1BXZlAgV0bQQtBXNrCR0BBgkxNF8xBgsxM18xMQYJMTNfMQYLMzFfMTIGCTMyXzEGCTM1XzEGCTM0XzEGCTEzXzYGCTMxXzUGCzMzXzExBgkzNF8yBgkyMV8yBgkyMl81BgkxMV8xBWRwBB4Fc2kDAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "39";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBB4DbgYZWm9tYmllQmF0dGxlBWxpBAUFdWQGGTIzNjUwNjE1ODRfMQVwZQMFY24GDeWSjOiwkAVzaQMFc2sJFQEGCTEzXzEGCTQxXzEGCTQxXzQGCzEyXzExBgk0MV81Bgk0MV8yBgsxM18xMQYLMzNfMTMGCTMzXzYGCTEzXzQFcGEDBW1wBhNIb3NwaXRhbDUFdmUDBWx2BGMFcG4GLzc55bKB5Y2W5o6J6KOk5a2Q5LiK572RBXByAwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "40";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBDwFdmUDA24GEVNrZWxldG9uBWxpBIdoBW1wBhNIb3NwaXRhbDMFcGEDBXBuBinjgI7nvojntYbjgI/Ct+WxseayuwVsdgRjBXNrCRMBBgkxM18zBgsxMl8xMQYJNDFfMQYJNDRfNQYJNDFfMgYJMTFfMQYLMTNfMTAGCTQyXzUGCTM1XzEFcHIDBXNpAwVwZQMFZHAEHgV1ZAYZMTE3NzU2OTUyNV8wAQ==";
            dealNormal(d0);
            start0 = "2023-1-16";
            end0 = "2023-1-22";
            d0 = new BossTaskDefine();
            d0.name = "41";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwVsaQQFBXBlAwNuBhVTcGlkZXJLaW5nBXBuBhvml6Dlj4wt5qyi5ZacBXNrCREBBgszM18xNAYJMjFfNwYJMTNfOQYJNDJfMwYJMzFfNQYLMzRfMTIGCTIxXzgGCzEzXzExBWx2BGMFc2kDBXVkBhkxNjkyODM4MjYyXzAFdmUDBXBhAwVtcAYRSG9zcGl0YWwFdG0EDwVkcAQeAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "42";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCREBBgkxMl8zBgkyMV8yBgkyMV8xBgk0NF81BgkzM181Bgk0Ml81BgkxM18xBgkzNF85BXNpAwVtcAYLRGlYaWEFcG4GK+OAjuWwj+WPr+eIseOAj+WQrOivtANuBh1Jcm9uWm9tYmllS2luZwVwYQMFdWQGGTE1NzcxNTYyNjhfMAVwcgMFcGUDBWxpBIFhBXZlAwVkcAQeBWx2BGMFdG0EUAE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "43";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GEVNrZWxldG9uBXVkBhkzMjE2MjcyNDc3XzAFcGEDBXByAwVtcAYNQmFpU2hhBXBlAwVsaQSfIAVzawkTAQYJMjFfMgYJMjJfNQYJMTJfMwYJMzVfMQYJNDJfNQYJNDFfNQYJMjFfNgYJMzJfOAYLMjFfMTAFY24GDemtlOeOiwVsdgRVBXNpAwV0bQSCLAVwbgYT5b6q5bm75bCYBWRwBAEB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "44";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBAoFcGUDA24GC0FUaWFuBWxpBIN0BXNpAwV2ZQIFcG4GK+OAjuelnueVjOOAj+i3r+S6uueUsgVwYQMFc2sJGQEGCTMzXzkGCTQxXzEGCTQxXzIGCTQxXzQGCTQxXzUGCTQzXzEGCTQ0XzMGCTM0XzIGCTM0XzcGCTM0XzQGCTM0XzgGCTIzXzgFY24GB+S6oQVtcAYNWGlGZW5nBXByAwVkcAQeBWx2BGMFdWQGGTI1MzY5ODE1MTVfMAE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "45";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkzNTU2MzYzOTc4XzADbgYZWm9tYmllQmF0dGxlBWx2BCgFcG4GIXllNuaLpeS9j+WNv+WwmF8Fc2sJBwEGCTQ0XzEGCTQ0XzIGCTQ0XzQFbGkEAQVtcAYJV29UdQE=";
            deal1(d0);
            d0 = new BossTaskDefine();
            d0.name = "46";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBM4QA24GGVR5cGhvb25XaXRjaAVzaQMFcG4GK+OAjuWwj+WPr+eIseOAj+avgeautwVwYQMFbHYEYwVoYgIFbXAGE0hvc3BpdGFsNQVkcAQeBXByAwVwZQMFdWQGGTMwMjQ2NDczNjVfMQV0bQQeBXZlAwVzawkNAQYJNDFfNAYLMjJfMTMGCTMxXzEGCzMxXzEyBgkyMV84BgkyMV83AQ==";
            deal8(d0);
            start0 = "2023-1-23";
            end0 = "2023-1-29";
            d0 = new BossTaskDefine();
            d0.name = "47";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBB4FbHYEYwNuBhNEYXlzcHJpbmcFbXAGFVByaXNvbkRvb3IFcHIDBXRtBGQFcGUDBWxpBGQFcG4GHemcnuWFicK357695Lq6BXVkBhkzNzg1NjUyOTQwXzEFdmUDBXBhAwVzaQMFc2sJEQEGCTEyXzEGCTEyXzIGCTM0XzQGCTM0XzUGCTM0XzYGCTQzXzEGCTIyXzkGCTIyXzUFY24GDeWSjOiwkAE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "48";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwV0bQQeBXBlAwNuBg1XZW5KaWUFbGkEho0gBWFyCQMBBhNsaWdodENvbmUFbXAGFUJpbmdLdURlZXAFdmUDBWx2BGMFc2kDBXNrCR0BBgk0MV8xBgk0NF81Bgk0Ml81BgkzMV8xBgkxMV8yBgkxMV8xBgkxMl8zBgkxM181BgkxM180BgsxM18xMAYJMTJfOQYJMTNfMQYLMjFfMTAGCTIxXzIFcG4GK+iLpeawtOOAjumTg+OAj+ihjOiInwVwYQMFdWQGGTI1NTI2Nzk0NzJfNwVkcAQeAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "49";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWNuBg3pl6rnlLUFc2kDA24GEUZpcmVXb2xmBXZlAwVwbgYr44CQ5a6d5a6d44CR6JC95bCP5Y+2BXBhAwVsdgRjBWxpBC8FZHAEHgVwcgMFcGUDBXNrCRMBBgkxM18zBgszNF8xMAYJMzNfOQYJMzRfNAYJMjFfMgYJNDRfMgYJMzVfMQYLMzRfMTQGCzM0XzExBW1wBg9ZYW5KaXVBBXVkBhcyNTU4OTY3ODdfMAV0bQQUAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "50";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwVsdgRjBWRwBB4FcGEDBXRtBIIsBXByAwVoYgMFbXAGF0hhbkd1YW5nU3ViA24GF0xhc3RkYXlUYW5rBXVkBhkxNjM4NDY4MDg0XzAFcGUDBXBuBivjgI7lsI/lj6/niLHjgI/muq/mtYEFc2kDBWxpBIaNIAVzawkPAQYJNDRfNQYJNDJfNQYJMTJfMwYJMTFfMQYJMjFfNAYJMjFfMgYJMTJfOQE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "51";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBHgFbHYEYwNuBhVCb29tU2t1bGxTBXNrCRcBBgkxMl8yBgkxMl8zBgkxMl82BgkxMV8xBgkxM184BgsxM18xMAYJMzFfMQYJMjFfNgYJMzJfMQYJMzFfOAYJMzFfNQVwbgYp4pek5byR5p2A4peiNDLjga7ni5cFbXAGF0hhbkd1YW5nU3ViBWRwBB4FdWQGGTE5NDM5MjUzODFfMAVoYgMFbGkESwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "52";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwVsaQT1GANuBhdCYXR0bGVNdW1teQV2ZQMFcG4GK+eLrOadpeOAjuS9s+OAj+Wxv+mjjgVwZQMFc2kDBXRtBAoFdWQGGTEzOTk0ODc4NDZfMAVwYQMFbXAGDVhpbmdHdQVsdgRaBWhiAwVzawkLAQYJMTNfOAYJMjFfMgYJMTNfNAYJMzRfOAYJMjFfNgE=";
            dealNormal(d0);
            start0 = "2023-1-30";
            end0 = "2023-2-5";
            d0 = new BossTaskDefine();
            d0.name = "53";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBAoFcHIDA24GC01vY2hhBXBlAwVsaQSDGwVhcgkDAQYZY2hyaXN0bWFzR3VuBXZlAwVzawkdAQYJMTNfMQYJMTNfOAYLMTNfMTEGCTIxXzcGCTIyXzgGCTMxXzUGCTMyXzEGCTM0XzcGCTM0XzQGCTMxXzgGCTMxXzkGCTEyXzMGCTQxXzQGCzEyXzE0BXNpAwVjbgYN5Luy6KOBBXBuBjHjgI7lgrLkuJbjgI/ln47ml6XlsrjkuYUFbXAGF0ZseVNreVNjZW5lBXBhAwVkcAQeBWx2BGMFdWQGGTI1MjQ4ODc2MDNfMAE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "54";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBEYDbgYVQm9vbVNrdWxsUwV1ZAYZMjM0ODUxMTUyOF8wBXNpAwVwbgYr44CQ6L2u5Zue44CR6bG8546E5py6BXNrCRsBBgkxMl81BgkxMl8zBgkxM18xBgsxMl8xMQYJMTJfMgYLMTNfMTAGCTEyXzkGCTEyXzYGCzEyXzEwBgkxM185BgkxM184BgkxM183BgkxM182BXBhAwVtcAYPTGFuZ1h1ZQV2ZQMFbHYEYwVwcgIFaGIDBXBlAwV0bQQPBWRwBB4B";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "55";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkyNjA0MDg2NjI0XzAFcHIDBXBlAwVhcgkDAQYTbGlnaHRDb25lBW1wBhFTa3lTY2VuZQVzawkXAQYJMTJfMgYLMTJfMTEGCzEyXzEwBgkxMl8zBgsxMl8xMgYJMzJfMQYJNDNfMQYJNDJfNQYJMzFfOQYJMjNfMQYJMjNfMgVsaQSCIANuBh1ab21iaWVBaXJib3JuZQV0bQQeBWx2BGMFc2kDBWRwBB4FdmUDBXBhAwVwbgYl44CO56We55WM44CP5rWF5b+1BWhiAwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "56";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBglXb1R1BWxpBIF7BXNrCQ8BBgk0Ml81BgkzMV82BgkyMV83BgkzMl81BgkzMl83Bgk0MV8xBgkzNV81BXVkBhc5MjAyMjM1NThfMAVwbgYN5rih5pe2A24GD1FpSHVhbmcFZHAEAQVsdgRjAQ==";
            deal6(d0);
            d0 = new BossTaskDefine();
            d0.name = "57";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwVwZQMFdWQGGTI5NDQ4ODAxODdfMQVsaQSNCwNuBg9YaWFvTWVpBXNpAwVsdgRjBW1wBhFEb25nRmVuZwVwYQMFdmUDBXRtBAoFc2sJEwEGCTEyXzIGCTEzXzgGCTMxXzEGCTMxXzgGCTQxXzUGCTQyXzUGCTIxXzQGCTMxXzYGCTIyXzcFZHAEHgVwbgYx44CQ5Y2T6LaK44CR5p6X5rex6KeB6bm/AQ==";
            deal8(d0);
            start0 = "2023-2-6";
            end0 = "2023-2-12";
            d0 = new BossTaskDefine();
            d0.name = "58";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMFdmUDBXVkBhkyNTc5NzYzNDM4XzcFbGkEnHQFc2kDBXRtBAoFc2sJBwEGCTQ0XzEGCTQ0XzIGCzIzXzExBWNuBg3ljZHlvq4DbgYXTGFzdGRheVRhbmsFZHAEHgVwbgYx44CO5YKy5LiW44CP5LiA6aqR57ud5bCYBXBhAwVwcgMFbXAGE0hvc3BpdGFsNQVwZQMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "59";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMFdWQGGTE5MzQwNDM3OTlfNQVwcgMFcGUDBWNuBg3njI7njosFc2sJDwEGCzIyXzEzBgkyMV82BgkyMV8xBgszMV8xMQYJMTJfNQYJMzRfNAYJMTNfMQVsaQSBFgVtcAYNWGluZ0d1A24GD0tuaWdodHMFZHAEHgVwbgYf5L2O6LCD4pye6buR5bCYBXBhAwVzaQMFdmUDAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "60";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBFoFcHIDA24GF1BvaXNvbkRlbW9uBWxpBGQFcG4GF+iNo+W9kuKYhmRiBWNuBg3lpI3ku4cFc2sJDQEGCTIxXzIGCTM1XzEGCzM0XzE0BgsxM18xMAYLMjNfMTEGCTMzXzMFbXAGEVBpYW9NaWFvBXBhAwVkcAQZBWx2BGMFdWQGGTIwMTUwNDYxMjRfMAE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "61";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBIIsA24GD0dhaWFGaXQFcHIDBXBlAwVzawkXAQYJMTJfNAYJMjFfNQYJMjFfOQYLMjFfMTAGCTIyXzEGCTIzXzMGCTM0XzMGCzM0XzExBgkzNF8yBgkzMV85BgkzMl8xBXBuBhvml6Dlj4wt5Lmx5paXBXVkBhkxMjUwNDE3NDQzXzAFZHAEHgVtcAYJV29UdQV0bQQ8BXZlAwVwYQMFc2kDAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "62";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwVwbgYl4pek5Luf5bGx4pei5aSp5769BXByAwVjbgYH54GrBXBlAwVzawkdAQYJMTNfOAYLMTNfMTEGCzEzXzEwBgkxM18yBgsyM18xMQYJMjNfMwYLMjJfMTEGCTMyXzgGCTMyXzUGCzMxXzEzBgkzNF83Bgk0MV8yBgkzMV85BgkyMl85A24GF0JsdWVNb3RvU2VjBXNpAwV0bQQeBWRwBB4FbGkEQQVtcAYJV29UdQVsdgRjBXVkBhkyNjk0NjQ5NjgyXzMFcGEDAQ==";
            dealNormal(d0);
            start0 = "2022-9-5";
            end0 = "2022-9-11";
            d0 = new BossTaskDefine();
            d0.name = "21";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBAIFbHYEVwVwZQMFc2sJDwEGCTMzXzYGCTM0XzEGCTIxXzIGCTIyXzcGCTM0XzQGCTMzXzcGCTMzXzUFdmUDBXNpAwNuBhdMaW5nU2hvb3RlcgVwcgMFdG0EHgVkcAQeBXBhAwVtcAYJV29UdQV1ZAYZMjA2MTI0MTk1MF8xBXBuBiXjgI7lpKnnqbrjgI/kupHlhL8B";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "22";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBIJrBXNrCREBBgk0NF8zBgk0M18xBgk0MV8xBgk0MV8yBgk0MV81BgszMV8xMAYLMzNfMTAGCTMyXzIFY24GDeeLguaAkgVzaQMFdG0EeAVwbgYx57q15qiq44CQ4pig44CR5qKm5pif6Iy2BXBhAwV1ZAYZMTE5MjAxODYyNV8wA24GE0ZpZ2h0S2luZwVwcgMFbHYEYwVkcAQeBXZlAwVtcAYJV29UdQVwZQMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "23";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRkBBgk0Ml8zBgkzNV8xBgkzNF80BgkzMl8xBgkzM18xBgkzMV8xBgkzNV81BgszMV8xMQYJMzJfNwYJNDJfNQYJMzRfOQYJMjFfO A V 2ZQMDbgYPR2FpYUZpdAVtcAYTSG9zcGl0YWwxBWRwBAIFcG4GK+WXnOihgOOAjOa4iuOAjeW9kuWinwV0bQR4BWNuBgfpo44FdWQGGTMyNjQ3MDM2MzFfMAVsaQR4AQ==";
            dealNormal(d0);
            start0 = "2022-9-12";
            end0 = "2022-9-18";
            d0 = new BossTaskDefine();
            d0.name = "24";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhVQcmlzb25Eb29yBXNpAwVwbgYp4pek5byR5p2A4peiwrflpKnmopMFZHAEHgVwYQMFdWQGGTExMTY5NDMwODVfNAVjbgYN5oGQ5oOnA24GF05pYW5Nb25zdGVyBWxpBIN0BXByAwVwZQMFdmUDBWx2BGMFdG0EHgVzawkNAQYLMjJfMTEGCTM1XzUGCTIxXzIGCTQyXzMGCTQxXzQGCTMxXzEB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "25";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBlAwNuBhVIdWdlUG9pc29uBWxpBIUKBXNrCREBBgkxM18zBgkxM184BgkzMV84Bgk0MV80BgkzMl8yBgkyMl83BgkxMV8xBgsxMl8xMQVwbgYf5LiN6ZyA55So5oOL5oOcBXRtBDIFcGEDBXNpAwVsdgRjBXZlAwVtcAYVUHJpc29uRG9vcgV1ZAYZMzI3OTIxNDIyOF8wBWRwBAoFcHIDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "26";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBlAwNuBhVab21iaWVXb2xmBWxpBBYFc2sJDQEGCTIyXzgGCTIxXzQGCTMzXzIGCTQxXzMGCTIyXzUGCTQzXzEFcG4GH+m4rem4reaYr+S4quWxkQVzaQMFcGEDBWx2BGMFdmUDBW1wBhNIYW5HdWFuZzUFdWQGGTI2NTczNjM3ODNfMgVkcAQeBXByAwE=";
            dealNormal(d0);
            start0 = "2022-9-19";
            end0 = "2022-9-25";
            d0 = new BossTaskDefine();
            d0.name = "27";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBB4Fc2kDBXBuBinil6TlvJHmnYDil6LCt+W/hueRtgV1ZAYZMjIwMTQ2NDM1N18wA24GGVNrZWxldGFsTWFnZQVkcAQeBWx2BGMFcHIDBXZlAwVwZQMFbXAGF0ZseVNreVNjZW5lBWxpBLBqBXNrCRMBBgkxM18xBgkxM18yBgkxM180BgkxM181BgkxM184BgsxM18xMAYJNDNfMQYJMjJfOAYLMjFfMTAB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "28";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GIUdhc0RlZmVuc2Vab21iaWUFdG0EHgVwcgMFcGUCBXBuBhPlkoznkp7puKIFY24GDeawuOS4lgVsaQSHaAVzawkRAQYJMTNfOAYLMTNfMTAGCTExXzEGCTIxXzIGCTMyXzEGCTMxXzQGCTIxXzgGCTIyXzUFdWQGGTI4MjkyMTA5NTBfMAVsdgRjBXZlAwVtcAYNWGluZ0d1BWRwBB4B";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "29";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GGVpvbWJpZUJhdHRsZQVtcAYLU2hhTW8Fc2kDBWxpBAEFc2sJDwEGCTQ0XzMGCTQ0XzQGCzMzXzEwBgk0MV8xBgk0MV8yBgk0Ml8zBgkzNV8xBWx2BGMFcGEDBXBuBiXjgI7lpKnnv7zjgI/lhYnlhYMFdG0EPAV2ZQMFcHIDBXVkBhkxNjY5MzA5NDY3XzAFcGUDAQ==";
            deal6(d0);
            start0 = "2022-9-26";
            end0 = "2022-10-2";
            d0 = new BossTaskDefine();
            d0.name = "30";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWNuBg3prZTnjosDbgYPWGluTGluZwVzaQMFdG0EHgV1ZAYZMjg3NDAyOTk3NF8wBXBhAwVtcAYJTHVZdQVkcAQeBXZlAgVwcgIFcGUDBWxpBIFIBXNrCRkBBgkxMV8xBgkzNV8xBgkzMl8xBgkzMV8xBgkyMV80BgsyMl8xMQYJMjFfOAYJMjNfMQYJMjFfMgYJMzVfNgYLMjFfMTAGCTIzXzkFcG4GMeOAkOelnum+meiniemGkuOAkeWkqemcuAE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "31";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkxNjIyMzg2ODQxXzADbgYXWm9tYmllU2hvb3QFcGEDBWx2BGMFZHAEHgV2ZQMFbXAGFVByaXNvbkRlZXAFcHIDBWxpBItcBXNrCRMBBgkxMV8xBgkxMl8yBgkxMl82BgkzMl8xBgkzM18zBgk0Ml80BgkyM18xBgkxMl83BgkyMl84BXBuBiPpvpnoiJ4q5aSpKuaso+aZqAVjbgYN5Zmp5qKmBXNpAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "32";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCQ0BBgk0NF8yBgsxM18xMAYJMTNfMQYJMTNfOAYJMTNfNAYJMTNfNQVwbgYP6L+f5piOLgNuBhlab21iaWVTaWx2ZXIFcHIDBXBlAwV0bQQeBXVkBhc2MDc4NzYxMzhfMAVsdgRjBXZlAwVtcAYNWGluZ0d1BWxpBHgFc2kDBXBhAwVkcAQeAQ==";
            dealNormal(d0);
            start0 = "2022-10-3";
            end0 = "2022-10-9";
            d0 = new BossTaskDefine();
            d0.name = "41";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwV2ZQMFcGUDBXBuBiPpvpnoiJ4q5aSpKumjmOmbtgNuBg9NYWRib3NzBWx2BGMFc2sJCwEGCTMxXzYGCTEzXzUGCTEzXzgGCzEzXzEwBgkzNF80BXBhAwV1ZAYZMTk5OTk1NTMzOF8wBWxpBIEbBW1wBg1YaW5nR3UFZHAEHgVwcgMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "42";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMDbgYTRmlnaHRXb2xmBXVkBhkzMDY1NTQwNTI0XzAFbGkEho0gBXNrCQ8BBgkxNF8yBgsxMl8xMQYLMTJfMTQGCTM0XzkGCTM1XzEGCTMyXzUGCTMxXzEFcG4GMemZjemtlOOAjuaal+OAj+m4veWtkOWuhwVtcAYTSG9zcGl0YWw1BWNuBg3mt7fmsowFZHAEHgE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "43";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwVwYQIFZHAEAQVjbgYN5aSN5LuHBXByAwVsaQSucAVwbgYZ5aSp5aSp5ZCR5LiKA24GDVdlbkppZQV1ZAYXNzY1MjU1NjI3XzAFdmUCBXBlAgVzawkZAQYJMTFfMQYJMTJfNQYJMTJfOAYLMTJfMTQGCTIxXzEGCTEyXzMGCTExXzMGCTIzXzMGCTMxXzUGCTEyXzIGCTE0XzIGCTE0XzQFbXAGFU1haW5VcGxhbmQFbHYEYwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "44";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBglXb1R1A24GDU1hZG1hbgV1ZAYZMTg1MTA4NjU0Nl8wBWxpBGUFY24GB+W5uwVwbgYTMjI3MTU3NTEwBWRwBB4FbHYEYwVzawkPAQYJMTFfMQYJMjNfNQYJMjJfOAYLMjFfMTAGCTEyXzMGCTEyXzIGCTEzXzgB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "45";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBglXb1R1BXNrCREBBgk0NF8yBgk0NF8zBgk0Ml8zBgk0MV81BgszM18xMAYJNDFfMQYLMzRfMTMGCTM1XzEFcG4GJeeou+Wmu+esrOS4gOa3seaDhQVzaQMFbHYEKAVsaQQBBXVkBhkxOTk2OTY5NTQ2XzAFcGEDA24GGVpvbWJpZUJhdHRsZQV2ZQMFcHIDBXBlAwV0bQQeAQ==";
            deal6(d0);
            start0 = "2022-10-10";
            end0 = "2022-10-16";
            d0 = new BossTaskDefine();
            d0.name = "51";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkxMzE1ODIwNDM0XzAFdmUDBW1wBhNIb3NwaXRhbDIFcG4GMeOAkOWuneWuneOAkemtlOazleWTiOWTiAVzawkRAQYJMzFfMwYJMjNfMwYJMTNfMQYJMTNfOAYJNDFfNAYJMjFfMgYJMzRfOQYJMzFfNQVwZQMFcHIDBWRwBB4DbgYhR2FzRGVmZW5zZVpvbWJpZQVzaQMFbGkEhzYFbHYEYwVjbgYN57yg57uVBXBhAwE=";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "52";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwVsaQSVDAVsdgRjBXNpAwVzawkRAQYJMTNfNQYJMTNfOAYJMTFfMQYLMTJfMTIGCTEyXzIGCTEyXzMGCTMyXzYGCTEyXzkFdmUDBXVkBhkyNTY2NzQwMjMxXzAFcGEDA24GG1pvbWJpZVNvbGRpZXIFdG0EHgVkcAQeBW1wBg1CZWlEb3UFcGUDBXBuBi/il6TlvJHmnYDil6LCt+eZveS6huWNoQE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "53";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRcBBgk0MV8xBgk0MV80Bgk0MV81Bgk0NF8zBgk0NF8yBgszM18xMQYJMzVfNQYJMzRfMgYJMzRfNwYJNDFfMwYJNDFfMgVwbgYZVEQuRk9Y54ix552hA24GC01vY2hhBXNpAwVwYQMFY24GB+mSogVsdgRbBXZlAgVtcAYRU2h1YW5nVGEFcHIDBXBlAwV0bQQeBXVkBhc1NjEzMzU2NjJfMAE=";
            deal6(d0);
            start0 = "2022-10-17";
            end0 = "2022-10-23";
            d0 = new BossTaskDefine();
            d0.name = "61";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkyNTUyNjc5NDcyXzcDbgYPU2hhcGVycwVsdgRjBWRwBBIFdmUDBW1wBglXb1R1BXBhAwVsaQSBegVzawkPAQYJMTFfMgYJMTNfMQYJMTNfMwYJMTNfNAYJMTNfNQYJMTJfMgYJMTNfNgV0bQQoBXBuBivoi6XmsLTjgI7pk4PjgI/ooYzoiJ8FcGUDAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "62";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GFUZpcmVEcmFnb24FdWQGGTM4MzA1NTgyOTBfMAVsaQQeBXNrCQ0BBgkyMl83BgkzNV8xBgkzNV8zBgk0MV8zBgk0Ml81BgszNF8xMAVsdgRjBW1wBhNIb3NwaXRhbDUFcG4GE+a7kea7keibiwE=";
            deal6(d0);
            d0 = new BossTaskDefine();
            d0.name = "63";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBC0FbGkEZAVwYQMFZHAEHgVwcgMDbgYVRHJhd1pvbWJpZQVwZQMFY24GDeaWreeUtQVwbgYhTlJELuWlpeS5ieWGjeingQVtcAYbSG9zcGl0YWxVbmRlcgV2ZQMFdWQGGTE2MDY5MTg2MTlfMAVzawkZAQYJNDNfMQYJNDNfMgYJNDNfMwYJNDJfNQYJMzNfMQYJMjNfNwYJMTRfNAYJMTRfMQYJMTRfMgYJMTJfMgYLMTJfMTEGCTE0XzMFc2kDAQ==";
            dealNormal(d0);
            start0 = "2022-10-24";
            end0 = "2022-10-30";
            d0 = new BossTaskDefine();
            d0.name = "64";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBAoDbgYVSXJvbkNoaWVmUwVwYQMFc2kDBWxpBGQFc2sJEQEGCTIyXzEGCTIxXzQGCTIxXzIGCzM0XzEzBgk0MV8xBgkyMl83BgkyMl8zBgkyMl81BXBuBhVOUkQu56Gs57OWBXBlAwV0bQQ8BXByAwV1ZAYZMjIyNDExMTYzNV8wBXZlAwVtcAYTSG9zcGl0YWw1AQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "65";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwVzaQMFbGkEukwFbHYEYwVwbgYj6b6Z6IieKuWkqSrml6Dlv7UFY24GDee7neeBrQVtcAYJV29UdQV2ZQMDbgYNQXJ0aHVyBXNrCQsBBgkxMV8xBgkyM18xBgkxM184BgkyMV8yBgk0Ml81BWRwBB4FcHIDBXVkBhkxMDI0MDUyMDc1XzMFcGUDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "66";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwNuBhlEaWdnaW5nQmVhc3QFcG4GH+S9juiwg+KcnumAhumcnAVzawkRAQYJMzFfNQYJMzRfMQYJMTFfMQYJNDJfMwYJMTNfOAYJMTNfOQYJMzVfMQYJMTJfNAVsdgRjBWNuBgfliqsFbGkEMgV1ZAYZMzA4MDQzODI3OV8wBXZlAwVwZQMFZHAEHgVzaQMFcGEDBXRtBDwFbXAGEUhvc3BpdGFsAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "67";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwV0bQQeBWx2BGMFc2kDBXVkBhkxMzcyMzUyNzc3XzAFcGUDBXZlAwNuBg9TaGFwZXJzBWxpBAUFbXAGDUhvdUppbgVwcgMFZHAEHgVzawkRAQYJNDRfMwYJNDFfMQYJMjNfMwYJMzRfMQYJMzRfNwYJMjJfNwYJMjFfMQYJNDJfMwVwbgYr44CO5bCP5Y+v54ix44CP5rKn5rW3AQ==";
            dealNormal(d0);
            start0 = "2023-3-27";
            end0 = "2023-4-2";
            d0 = new BossTaskDefine();
            d0.name = "64";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBAoDbgYVSXJvbkNoaWVmUwVwYQMFc2kDBWxpBGQFc2sJEQEGCTIyXzEGCTIxXzQGCTIxXzIGCzM0XzEzBgk0MV8xBgkyMl83BgkyMl8zBgkyMl81BXBuBhVOUkQu56Gs57OWBXBlAwV0bQQ8BXByAwV1ZAYZMjIyNDExMTYzNV8wBXZlAwVtcAYTSG9zcGl0YWw1AQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "65";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwVzaQMFbGkEukwFbHYEYwVwbgYj6b6Z6IieKuWkqSrml6Dlv7UFY24GDee7neeBrQVtcAYJV29UdQV2ZQMDbgYNQXJ0aHVyBXNrCQsBBgkxMV8xBgkyM18xBgkxM184BgkyMV8yBgk0Ml81BWRwBB4FcHIDBXVkBhkxMDI0MDUyMDc1XzMFcGUDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "66";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwNuBhlEaWdnaW5nQmVhc3QFcG4GH+S9juiwg+KcnumAhumcnAVzawkRAQYJMzFfNQYJMzRfMQYJMTFfMQYJNDJfMwYJMTNfOAYJMTNfOQYJMzVfMQYJMTJfNAVsdgRjBWNuBgfliqsFbGkEMgV1ZAYZMzA4MDQzODI3OV8wBXZlAwVwZQMFZHAEHgVzaQMFcGEDBXRtBDwFbXAGEUhvc3BpdGFsAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "67";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwV0bQQeBWx2BGMFc2kDBXVkBhkxMzcyMzUyNzc3XzAFcGUDBXZlAwNuBg9TaGFwZXJzBWxpBAUFbXAGDUhvdUppbgVwcgMFZHAEHgVzawkRAQYJNDRfMwYJNDFfMQYJMjNfMwYJMzRfMQYJMzRfNwYJMjJfNwYJMjFfMQYJNDJfMwVwbgYr44CO5bCP5Y+v54ix44CP5rKn5rW3AQ==";
            dealNormal(d0);
            start0 = "2023-4-3";
            end0 = "2023-4-9";
            d0 = new BossTaskDefine();
            d0.name = "1";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkyNTc5NzYzNDM4XzcDbgYXTGFzdGRheVRhbmsFcGUDBXNrCQsBBgkxMV8xBgszNF8xMwYJMjNfOAYJMTRfNAYJMTJfOQVwYQMFc2kDBXByAwVsaQRaBXRtBHgFcG4GMeOAjuWCsuS4luOAj+S4gOmqkee7neWwmAVtcAYTSG9zcGl0YWw1BXZlAwVkcAQKAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "2";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhNIYW5HdWFuZzIDbgYRUGhhbnRvbVgFc2sJDwEGCTEzXzEGCTIxXzgGCzIxXzEwBgkyM18yBgkzMV84BgkyMV83BgkzNF84BWNuBg3mtanliqsFZHAEDwV2ZQMFcGUDBXByAwVwYQMFbGkEfQVwbgYp5Ya35b+DwrfjgI7lraTni6zjgI8FdWQGFzg5NjczMjgxMl83BXNpAwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "3";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBAsFdG0EgTQFc2sJCwEGCTQzXzEGCTEzXzgGCzEyXzExBgsxMl8xNAYLMTJfMTMFbXAGEUhvc3BpdGFsBXBuBiXjgI7lgrLkuJbjgI/pm6jlvbEFdmUDBXBhAwV1ZAYXNDgyMjI3NDU5XzAFY24GDeWNkeW+rgNuBhdNZWF0eVpvbWJpZQVsaQRVBXNpAwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "4";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhNIYW5HdWFuZzUDbgYRU2tlbGV0b24FcG4GE+Wwj+Wwj0RPUAVzawkRAQYJNDFfMwYJMzVfMQYJMTJfOAYJMjFfNwYJMzNfNQYJMjFfOAYJMjJfMwYJMjJfMQVjbgYN54uC54OtBXVkBhkzNjQ4NDEyNzg1XzAFZHAEHgVwZQMFcGEDBWxpBIE4BXRtBHgFbHYEXAV 2ZQMFc2kDBXByAwE=";
            dealNormal(d0);
            start0 = "2023-4-10";
            end0 = "2023-4-16";
            d0 = new BossTaskDefine();
            d0.name = "5";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwV0bQSBNAVzawkZAQYJMzRfNwYJMzVfMwYJNDFfNAYJMjFfMQYJNDJfNQYJMjJfNQYJNDFfNQYJMzRfMgYJMzRfNQYJNDJfMQYJMjJfNgYJMzRfMwVsaQSFPAVsdgRjBW1wBhVQcmlzb25Eb29yBXBuBinil6TlvJHmnYDil6LCt+eMueiMtgVkcAQeBXNpAwV1ZAYZMTM5ODM4MTUyNl80BWNuBgflubsDbgYXQWlyY3JhZnRHdW4FcGUDBXByAgV2ZQMB";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "6";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwNuBhlUeXBob29uV2l0Y2gFc2sJDwEGCTExXzEGCzEzXzEwBgkyM18zBgkyMV82BgkxMl84BgsxMl8xMQYJMTJfNQVwYQMFZHAEHgVwcgMFbXAGCVdvVHUFbGkEg4c0BXRtBB4FcG4GKem+meiInirlpKkq5qKm5Lit5p6XBXZlAwV1ZAYXMzcwMDc5ODI4XzAFcGUDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "7";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BF8DbgYdWm9tYmllRm9vdGJhbGwFdWQGGTExODYyMTgzNTVfMAVtcAYTSG9zcGl0YWw1BWxpBJIdBXRtBDwFcG4GMee6teaoquOAkOKYoOOAkem7keeZvuWQiAVkcAQeBWNuBg3lkozosJAFc2sJEwEGCTMzXzIGCTM0XzgGCzM0XzEzBgkxMl80BgkxMl84Bgk0NF8yBgkxNF8yBgsxMl8xMwYJMzFfNQE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "8";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBlAwNuBhVDaGVldGFoQ2FyBXVkBhkxNTQyNzIzMjg2XzAFZHAEAQVjbgYN5ZKM6LCQBW1wBhVNYWluVXBsYW5kBXZlAgVsaQSFRgVwbgYp4pek5byR5p2A4peiwrfkuZ3lvrcFcHIDBXNrCQ8BBgkzMl8xBgkzNV8xBgk0MV80BgkxM184BgsxM18xMAYJMzRfOQYJMjFfOAE= ";
            dealNormal(d0);
            start0 = "2023-4-17";
            end0 = "2023-4-23";
            d0 = new BossTaskDefine();
            d0.name = "9";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GGVpvbWJpZUJhdHRsZQVtcAYJV29UdQVkcAQUBXNrCQ0BBgkxMl82BgkxMl83Bgk0M18xBgkxMV8xBgkzMl8xBgsyMV8xMAVwbgYT6IKd5Yiw5bqVBXVkBhkyOTA1NTEyMjAxXzAFbGkEZAE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "10";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwVwYQMDbgYXRm9nZ3lab21iaWUFZHAEAQV1ZAYZMzA2OTg0NTcxMF8xBW1wBhFTa3lTY2VuZQVsaQQIBXByAwVwZQMFdmUDBXNrCQ8BBgkyMV8yBgkzMV84BgkzMl8xBgk0Ml81BgkyM18yBgkzMl81BgkzM182BXRtBB4FcG4GGemsvOiInuWwmOW/gwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "11";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwNuBhNDaGFzdGVuZXIFZHAECgVtcAYdRmFuZ1pob3VTZWNvbmQFY24GB+mbtwVwbgYp4pek5byR5p2A4peiwrfliJ3muZsFcHICBXBlAwV1ZAYXNjA3ODc2MTM4XzAFbHYEUAVsaQSBFgVzaQMFdG0EHgVzawkZAQYJNDFfMwYJNDFfNAYLMTNfMTAGCTExXzMGCTExXzEGCTQzXzEGCzMzXzExBgkxMl8zBgkxMl8xBgk0Ml8xBgk0MV81BgkxM18yAQ==";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "12";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwVzawkPAQYJMTNfOAYLMTNfMTAGCTEyXzMGCTMyXzEGCTMxXzUGCTQyXzEGCTQyXzQFdG0EHgVtcAYLU2hhTW8FcGEDBWxpBDIFcG4GK+mZjemtlOOAjue1kOOAj+e0q+iLjwV2ZQMFdWQGFzg2MTkyODgzOV8wA24GGVR5cGhvb25XaXRjaAVkcAQBBXBlAwVwcgMFY24GDeWkjeS7hwE=";
            dealNormal(d0);
            start0 = "2022-10-31";
            end0 = "2022-11-6";
            d0 = new BossTaskDefine();
            d0.name = "68";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMFbGkEg3QFcHIDBWRwBB4DbgYZT2ZmaWNlWm9tYmllBW1wBg1YaW5nR3UFc2kDBXBhAwVwbgYf55av54uC5bGg5bC45omLBXNrCRkBBgkyMV8yBgkzNV8xBgkzNV8zBgkzNF81Bgk0Ml81BgkzMV8xBgk0NF80BgszMV8xMAYJMzJfMgYJMzJfMwYJMzJfMQYJMjJfNwV0bQRyBXVkBhkyMTc0NDg1NzQwXzIFcGUDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "69";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBinjgI7nvojntYbjgI/Ct+aYjuaciANuBhNGaWdodEtpbmcFdWQGGTM3Njk5NjU4MzVfMAVtcAYTSGFuR3Vhbmc1BXNrCREBBgkxMV8xBgsxMl8xMgYJMjJfMQYJMzFfNQYJMzVfMwYJMTNfNQYJMTNfNAYJMzFfOAVkcAQBBWNuBg3kuqHlkb0FbGkECgV0bQQ8BXBhAwVwcgMFbHYEYwVwZQMFdmUDBXNpAwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "70";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwVzawkNAQYLMTJfMTIGCTEzXzgGCTEyXzEGCTM1XzIGCzEzXzEwBgsyMV8xMANuBhNHaG9zdER1a2UFcG4GI+m+meiInirlpKkq5pif6IqSBXRtBIEQBWxpBIElBWx2BGMFdmUDBW1wBhlQcmlzb25FeHBvcnQFcHIDBXBlAwV1ZAYXODM2ODU3NzQ4XzAFZHAEHgVjbgYN5aSN5LuHBXBhAwE=";
            dealNormal(d0);
            d0 = new BossTaskDefine();
            d0.name = "71";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBIJeA24GFUJvb21Ta3VsbFMFdWQGFzIyNDI2ODYwMF8wBXBuBjHil6TlvJHmnYDil6Lku4rmmJTkvZXmmJQFZHAEHgVzawkVAQYJMTJfMwYJMTJfNQYJMTJfNgYLMTNfMTAGCTEzXzgGCTMxXzUGCTMxXzkGCTMyXzEGCTExXzEGCTMxXzgFbHYEYwVjbgYN5oOn6aKFBW1wBhdIYW5HdWFuZ1N1YgE=";
            dealNormal(d0);
            start0 = "2023-5-29";
            end0 = "2023-6-4";
            d0 = new BossTaskDefine();
            d0.name = "1";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBAEDbgYVQ2hlZXRhaENhcgVzawkPAQYJMzJfMQYJMzVfMQYJMTNfOAYJMTFfMQYJMjFfNAYJMzFfOAYJMzFfMQV0bQQPBWxpBIaNIAV1ZAYZMTU0MjcyMzI4Nl8wBW1wBhVNYWluVXBsYW5kBXByAwVwZQMFdmUCBXBuBinil6TlvJHmnYDil6LCt+S5neW+twVjbgYN5rKJ552hAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "2";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBAoDbgYXQm9uZUJyZWFrZXIFbGkElzgFbXAGC0Rhb1RhBXVkBhc5MDkyMzYxMzhfMAVwbgYp4pek5byR5p2A4peiwrfliJ3muZsFc2sJGwEGCTEyXzYGCTEzXzMGCTEzXzUGCTEzXzgGCTEzXzkGCTExXzEGCTExXzUGCzMzXzE0Bgk0M18xBgk0MV80Bgk0Ml8yBgkxMV83BgkzMl8xBXRtBDwFdmUDBXByAwVsdgQ8BXBlAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "3";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWFyCQMBBhlleHRyZW1lTGFzZXIFZHAEHgNuBg9NYWRib3NzBXByAwVwZQMFbGkEAQVtcAYTSG9zcGl0YWw1BWx2BGMFdWQGGTI2OTI4OTA2NDRfMgVzaQMFdmUDBXBuBivlgL7kuJbjgI7lpKnjgI/mmJ/ovrAFc2sJDwEGCTExXzEGCTEyXzMGCTEyXzYGCzEyXzExBgszMV8xMgYLMzNfMTQGCTQxXzUFcGEDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "4";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBIIsBWRwBB4FbGkEkh0FbXAGDVhpbmdHdQVwZQMFcHIDBXNrCR0BBgsyMl8xMQYLMzNfMTEGCTQ0XzYGCzMxXzExBgkxMl83BgkyM180BgkyM181BgkyM18yBgkyM18xBgkyM185BgkyMl81BgkyMV8yBgkzNF8zBgk0Ml81BXBuBh/nlq/ni4LlsaDlsLjmiYsFdWQGGTIxNzQ0ODU3NDBfMgVzaQMDbgYTWm9tYmllRmF0BWx2BGMFcGECBXZlAgE=";
            deal8(d0);
            start0 = "2023-6-5";
            end0 = "2023-6-11";
            d0 = new BossTaskDefine();
            d0.name = "5";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWNuBg3mlq3mmrQFZHAEHgVwYQMFcG4GMemZjemtlOOAjuauh+OAj+mBguW3ne+8gQVtcAYNWGluZ0d1BWhiAwVzawkRAQYLMTJfMTQGCTEzXzUGCTEzXzgGCTExXzEGCTExXzcGCTEzXzkGCTM1XzEGCTMzXzgFYXIJAwEGGWV4dHJlbWVMYXNlcgVwcgMFcGUDA24GE0dob3N0RHVrZQV0bQQLBWx2BGMFdmUDBXVkBhkxNjE1NjAzMzE2XzAFc2kDBWxpBDwB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "6";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwV2ZQMDbgYTSG9va1dpdGNoBXBuBiXlr5Lmm6bigLvmmrTpo47pm6oFY24GDeWvkuWKqwVwZQMFZHAEHgVzawkTAQYJMTFfMQYJMTFfNwYJMTNfMwYJMTNfOQYJMTNfOAYJNDJfMwYJMzJfMQYLMTNfMTAGCTMxXzUFc2kDBWxpBGQFdWQGGTE0MDUwMjk1ODlfMAVwYQMFdG0ECgVtcAYRRG9uZ0ZlbmcFbHYEYwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "7";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwV1ZAYXNDQyODE1ODgyXzQDbgYTWm9tYmllRmF0BXRtBAoFcGEDBWxpBAEFbXAGF0ZseVNreVNjZW5lBWx2BEYFcGUDBXZlAwVwcgMFcG4GK+OAjuWwj+WPr+eIseOAj+mck+mbqAVzawkdAQYJMzNfMQYJMzNfMgYJMzNfMwYJMzNfNAYJMzNfNQYJMzRfMQYJMzVfMQYJMzRfNAYJMzFfNAYJMzFfOAYJMzVfMwYJMzFfNQYJMzNfNwYJMzFfNgE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "8";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwNuBg9YaW5MaW5nBXVkBhkzMjE2MjcyNDc3XzAFdmUDBXBhAwVzawkdAQYLMTJfMTEGCTIxXzgGCzMxXzEyBgszM18xMQYJMzVfMQYJMzJfMQYJNDJfNgYJMTFfMQYLMzNfMTQGCTM0XzIGCzIxXzExBgsyMl8xMQYLMjJfMTMGCTEyXzMFZHAEHgVwcgMFbHYEYwVtcAYPWWFuSml1QQVwbgYr5rWK6YWS44CO5b6q44CP5bm75bCYBWNuBg3lhYnokL0FbGkEho0gBWFyCQMBBhNsaWdodENvbmUFdG0ECgE=";
            d0.noSkillArr = ["vehicleSensitive","handSensitive","redArmsSensitive"];
            dealMax(d0);
            start0 = "2023-6-12";
            end0 = "2023-6-18";
            d0 = new BossTaskDefine();
            d0.name = "9";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBg1YaW5nR3UFZHAEHgNuBhdNZWF0eVpvbWJpZQV1ZAYZMzM2NDA1OTU5OF8wBXBuBhPmoqbop4HnvooFbHYEYwVzawkPAQYJNDRfMwYJNDRfNQYJNDFfMQYJNDFfMwYLMjJfMTMGCzIxXzEyBgk0M18xBXByAwV0bQR4BXZlAwVwZQMFbGkEho0gBXNpAwVoYgMFY24GDeeLguaAkgVwYQMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "10";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRkBBgkzNF8zBgkzMV8xBgszM18xMgYLMjNfMTEGCTQxXzEGCTQxXzIGCTQzXzEGCTM0XzQGCTQ0XzYGCzIyXzEzBgkxMl84BgsxMl8xMQV0bQQKBWxpBIRoBXBhAwVkcAQeBWNuBg3mgJLml6UFbXAGEUhvc3BpdGFsBWx2BGIFcHIDBXBlAgNuBhdMYWJvclpvbWJpZQV2ZQMFcG4GK+eCq+mjjuOAjuWwmOOAj+iQpOaDkQV1ZAYZMTc5NzM1MjgzM18wBXNpAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "11";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBB4DbgYZWm9tYmllQmF0dGxlBXByAwV0bQQZBWx2BGMFdmUDBXNpAwVsaQSXOAVtcAYTSG9zcGl0YWw1BXBhAwV1ZAYZMTkxMTg5MTcwOV8xBXBuBhPov7fkurrlpJwFc2sJFQEGCTMyXzEGCzMxXzEyBgsxM18xMAYJMTNfOAYJMTNfNgYJMTNfMwYLMjFfMTAGCTMzXzYGCTIxXzIGCTQ0XzIFcGUDAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "12";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRkBBgkzMl80BgkzNF80BgkzM184BgkxMV8zBgkxMV8xBgkxM184BgsxM18xMAYJMzRfOQYJMjFfNgYLMTJfMTEGCTEzXzIGCTEzXzUDbgYLV2FuZGEFYXIJAwEGGXJvY2tldF9maWdodAVkcAQeBXBuBivjgI7nq6Xor53jgI/or7rlsrfpo44FbGkEhiAFbXAGCVdvVHUFbHYEYwV1ZAYZMzY0Njk3OTg5MV8wAQ==";
            deal8(d0);
            start0 = "2023-6-19";
            end0 = "2023-6-25";
            d0 = new BossTaskDefine();
            d0.name = "13";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GDVdlbkppZQV0bQSCLAVhcgkDAQYZZXh0cmVtZUxhc2VyBXBuBhlURC5GT1jovbvor60FbHYEYwVwYQMFbGkEgl4FZHAEHgVwcgMFcGUDBXVkBhkzNzEyODY4NjY0XzEFc2sJFQEGCTQxXzUGCzEyXzEwBgkyM185BgszM18xMQYJMzRfNgYJNDRfNgYLMzFfMTEGCzM0XzE0BgszNF8xMAYJMzRfOAVtcAYNWGluZ0d1AQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "14";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBiXmpaDnnrPimYLlsI/nvr3kuroDbgYTRGF5c3ByaW5nBWNuBg3ml6XlnLAFc2kDBXBhAwVkcAQBBXNrCR0BBgk0NF80BgkyMV8yBgkzNF80BgszNF8xNAYLMTNfMTAGCzEyXzExBgkzM184Bgk0Ml8xBgk0Ml8yBgk0Ml8zBgk0Ml81Bgk0Ml80Bgk0Ml82Bgk0M18xBWxpBAoFdWQGGTM5MTQyMjI5MTVfMQVwcgMFcGUDBXRtBAoFbHYEYwVtcAYNWGluZ0d1BXZlAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "15";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBiPpvpnoiJ4q5aSpKuW5u+WGsAV1ZAYZMzY1MTEyMDYxNF8wBWxpBK5wBXBhAwVzaQMFcHIDBWRwBAEFc2sJDwEGCTQxXzEGCTQxXzIGCTQxXzMGCTQxXzUGCTQ0XzMGCzMzXzEwBgkzNV8xA24GFUNoZWV0YWhDYXIFdG0EgiwFbHYEWgV2ZQMFbXAGE0hvc3BpdGFsNQVwZQMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "16";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCREBBgk0NF8zBgk0MV8yBgk0MV8xBgk0MV81Bgk0Ml8zBgszM18xNAYJMzFfMQYJMzVfMQV0bQQKA24GD1JlZEdhaWEFdWQGGTE1ODc0NjQwOThfMgVwYQMFc2kDBW1wBglXb1R1BWx2BFEFdmUDBXByAwVwbgYx44CQ56We6b6Z6KeJ6YaS44CR6ZKi54u8BXBlAwVjbgYN54uC5oCSBWRwBB4FbGkECgE=";
            deal8(d0);
            start0 = "2023-6-26";
            end0 = "2023-7-2";
            d0 = new BossTaskDefine();
            d0.name = "17";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBEYDbgYXTGFzdGRheVRhbmsFcGUDBXBuBhcxMzQ1NTM2NTAwOQV1ZAYZMzI2MTgxMjMyNF8wBWxpBIaNIAVwYQIFc2kDBXByAwVzawkPAQYJNDRfNQYLMTJfMTEGCTEzXzMGCTQxXzEGCTIxXzcGCTQ0XzMGCzIxXzEzBWRwBBQFbHYEYwVoYgMFbXAGEURvbmdTaGFuAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "19";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBC0Fc2sJEwEGCzIxXzExBgkyMV8yBgsxMl8xMQYJNDFfMgYJNDRfNQYJNDJfNQYJMTFfNwYJMzJfOAYLMzRfMTEDbgYRUGhhbnRvbVgFc2kDBXBuBinlhrflv4PCt+OAjuWtpOeLrOOAjwVwZQMFbGkEAQVwYQMFZHAEHgV2ZQMFcHIDBW1wBhNIYW5HdWFuZzIFdWQGFzg5NjczMjgxMl83BWx2BFAB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "20";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkxOTAyMDYwOTc3XzADbgYVSXJvbkNoaWVmUwV0bQQeBWxpBIaNIAVzaQMFcG4GK+eCq+mjjuOAjuWFruOAj+WXn+WPuQVwYQMFbXAGEVNreVNjZW5lBWx2BGMFdmUDBXByAwVwZQMFc2sJFQEGCTExXzEGCzEyXzExBgkyMl8yBgkyMl8xBgkyMl83BgkyMV82Bgk0NF81Bgk0Ml81Bgk0MV80BgsyMV8xMgE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "21";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhdGbHlTa3lTY2VuZQVkcAQUA24GIUdhc0RlZmVuc2Vab21iaWUFdWQGGTM4NDcyNTQ0MDJfMAVsdgRjBXNrCRUBBgkxMV8xBgsyMl8xMwYLMjFfMTIGCTQxXzQGCTEzXzMGCTEzXzEGCzIyXzExBgszM18xMgYLMTJfMTQGCTQ0XzUFcGEDBXRtBBQFcGUDBXByAwVsaQSGjSAFc2kDBXZlAwVwbgYl44CO54Kr6aOO44CP6bi95a2QAQ==";
            dealMax(d0);
            start0 = "2023-7-3";
            end0 = "2023-7-9";
            d0 = new BossTaskDefine();
            d0.name = "23";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GC1dhbmRhBXByAwVwZQMFbXAGE0hvc3BpdGFsMwVsdgRjBWNuBg3nga3kuqEFbGkElzgFdmUDBXBuBiPpvpnoiJ4q5aSpKuapmeWtkAVzaQIFZHAEAQVzawkVAQYJNDNfMQYJMTFfNwYJMzJfOAYJMTFfMQYJMTNfOAYJMzNfOAYJMzRfNQYJMzRfOQYJNDRfMwYJNDRfMgV0bQRkBWFyCQMBBhliZWFkQ3Jvc3Nib3cFdWQGGTIyOTYxNDE4NjdfMAE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "24";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAgNuBhVab21iaWVMaW5nBXBuBiXmsoHlm63mmKXkuKjprZTnjosFdWQGFzcwMDY1NTYyNV8wBXNpAwVwcgMFc2sJEwEGCTQ0XzUGCTEzXzgGCTQyXzUGCTMyXzEGCzEzXzExBgsxMl8xMQYJMTFfNwYJMTFfMgYJNDRfNgVwYQMFbGkEho0gBWRwBB4FY24GDemtlOeOiwVsdgRjBXRtBDIFbXAGE0hvc3BpdGFsNQVwZQIB";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "25";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCQ8BBgk0NF82BgsxMl8xNAYJMTFfMQYJMzFfOQYLMTJfMTEGCTEyXzQGCTM0XzQFcGEDA24GD01hZGJvc3MFbGkEh2gFbHYEYwV2ZQMFcHIDBXBlAwV0bQR4BW1wBg1YaW5nR3UFcG4GK+eCq+mjjuOAjuWFruOAj+WXn+WPuQVzaQMFYXIJAwEGFXJvY2tldENhdGUFdWQGGTE5MDIwNjA5NzdfMAVkcAQFAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "26";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwVwYQMFbGkEAQV0bQSCLANuBhlEaWdnaW5nQmVhc3QFdmUDBXVkBhkzMDgwNDM4Mjc5XzAFc2sJFQEGCTMxXzUGCTEzXzgGCTEzXzkGCzIzXzExBgszM18xNAYJNDRfNgYJMzVfMQYJMzJfMQYLMjFfMTEGCTExXzcFcHIDBW1wBhFIb3NwaXRhbAVwZQMFY24GB+WKqwVsdgRaBWRwBB4FcG4GH+S9juiwg+KcnumAhumcnAE=";
            d0.noSkillArr = ["everSilenceEnemy"];
            deal8(d0);
            start0 = "2023-7-10";
            end0 = "2023-7-16";
            d0 = new BossTaskDefine();
            d0.name = "27";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCREBBgkzNV8xBgkzMl8xBgszMV8xMgYJMjFfNwYLMzRfMTQGCTEzXzgGCTM0XzkGCzIxXzEzBXRtBAoDbgYbQmFsbExpZ2h0bmluZwVkcAQeBXBhAwVzaQMFbGkEZAVtcAYTSG9zcGl0YWwzBXVkBhkxNjY5MzA5NDY3XzAFcHIDBXZlAwVwZQMFcG4GJeOAjuWkqee/vOOAj+WFieWFgwVsdgRjAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "28";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkxNDIzNTU2NTU0XzEFbHYEYwVsaQSGjSAFZHAEAQVzaQMFY24GDeedoeWcowNuBhdNZWF0eVpvbWJpZQVwYQMFbXAGD0xhbmdYdWUFdG0EPAVzawkPAQYJNDRfNgYLMzFfMTIGCzIzXzExBgsyMV8xMgYLMzNfMTEGCTExXzEGCTIxXzIFcG4GE0hGQ+S4jeWXpgVwcgMFdmUDBXBlAwE=";
            d0.noSkillArr = ["vehicleSensitive"];
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "29";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCR0BBgszNF8xNAYLMjNfMTEGCTM1XzEGCTQyXzUGCTM1XzYGCTMyXzEGCTIxXzIGCTMzXzMGCTM0XzkGCTEyXzIGCTMxXzUGCTEzXzgGCzIyXzEzBgszNF8xMwVkcAQeA24GCUdpcmwFcGEDBXBlAwVzaQMFbXAGG0hvc3BpdGFsVW5kZXIFdG0EOwV1ZAYZMTQ2NjIzMzcxMV83BWxpBIGTSAVsdgRQBWFyCQMBBhNsaWdodENvbmUFcHIDBXZlAwVwbgYl5be056yR55m944Gu5Zue5b2SBWNuBg3njovnnaEB";
            d0.noSkillArr = ["strongLing_10","cantMove"];
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "30";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBgtEYW9UYQVkcAQeA24GH0Zyb3plbkRyYWdvbkFpcgV1ZAYZMjM0MjExMjY4NV8xBXNrCRsBBgkzM182BgsxMl8xMQYJNDJfNQYJMjJfNAYJMjJfNQYJMjJfNgYLMTNfMTEGCTE0XzEGCTM1XzIGCTM0XzcGCTM0XzkGCTM1XzUGCTQxXzEFbHYEYwV0bQSCLAVwYQMFcGUDBWhiAwVsaQSLXAVzaQMFcHICBXZlAwVwbgYV5Lmd6ImyRGVlcgE=";
            deal8(d0);
            start0 = "2023-7-17";
            end0 = "2023-7-23";
            d0 = new BossTaskDefine();
            d0.name = "31";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GFUZpcmVEcmFnb24FaGIDBXNrCREBBgkyMV8yBgsyMV8xMQYJMjJfOAYLMzNfMTQGCTQ0XzUGCTIxXzgGCTQyXzUGCTM1XzEFZHAEHgV2ZQMFcGEDBXNpAwVsaQSGjSAFdWQGGTI2MjkxOTY4MDhfMAVwbgYj6KW/5YyXKueLvCrliJ3op4EFdG0EgUgFbHYEYwVwcgMFbXAGF0ZseVNreVNjZW5lBXBlAwE=";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "32";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwVsaQQFBXVkBhkxMzcyMzUyNzc3XzAFZHAEBQVzawkTAQYJMTNfOAYJMTNfMwYJMTJfOQYLMjNfMTEGCTIxXzkGCTIxXzIGCTMyXzEGCzM0XzE0BgsxMl8xMQVwZQMFcG4GK+OAjuWwj+WPr+eIseOAj+ayp+a1twNuBg9YaWFvTWVpBXRtBDwFbHYEYwVoYgIFc2kDBW1wBhtIb3NwaXRhbFVuZGVyBXByAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "33";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBjHjgJDop4nphpLjgJHlhavph43npZ7lrZAFcHIDBWRwBB4Fc2kDBWxpBIJAA24GEVdhdGNoZG9nBXBhAwV1ZAYZMjczNDM2NjA0M18wBXRtBIFcBWNuBg3njovlvq4FbHYEYwVoYgMFcGUDBXNrCRMBBgkxM184BgsxM18xMAYJMjFfMgYJMzNfMQYLMjNfMTEGCTM1XzEGCTM0XzQGCTMxXzUGCzEyXzExBXZlAwVtcAYTSG9zcGl0YWwxAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "34";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBB4DbgYdSXJvblpvbWJpZUtpbmcFc2kDBXByAwV0bQSBFgVzawkRAQYJMTNfOQYJMTNfOAYLMjNfMTEGCTMyXzgGCzIxXzEzBgsxMl8xMgYJNDRfNgYJNDRfNQVwbgYx6Z2S5aO244CO5qmZ44CP6YeN5pyo6ZiZBWx2BGMFcGEDBW1wBgtEaVhpYQVwZQIFY24GDeWkjeS7hwV2ZQMFdWQGGTI0Nzk5OTA0MzJfMAVsaQSuDAE=";
            deal8(d0);
            start0 = "2023-7-24";
            end0 = "2023-7-30";
            d0 = new BossTaskDefine();
            d0.name = "35";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRUBBgkxMV8xBgkxM185BgsxMl8xMQYLMTJfMTAGCzEyXzEyBgsxMl8xMwYJMjFfNwYJMjFfNQYJMjNfNgYJMzFfNgV0bQSCLAVsaQQKBW1wBgtMdlNlbgV1ZAYZMzgwMDE5MTY3OF8xBXBuBiFDbG92ZXJ0ZWFt5LyK5qKmBWNuBg3nvKDnu5UFbHYEYwNuBhlGaWdodFNob290ZXIFYXIJAwEGF2dyZWVkeVNuYWtlBWRwBAEB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "36";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNpAwVkcAQeA24GDVNlbnRyeQV0bQQyBXBhAwVsaQSBFgV1ZAYZMjM4MTA2ODM0NV8xBWNuBg3lnLDpm7cFcHIDBWx2BGMFcGUDBWhiAwV2ZQMFbXAGDVhpYW5RdQVwbgYN6I2G6L2yBXNrCRUBBgkxMl81BgkxMl8zBgk0MV8xBgkxM18xBgkxM184BgkyMl8zBgkyMl84BgkzNF8xBgkxMV8yBgk0Ml8zAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "37";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhNIYW5HdWFuZzUFbHYEYwVwZQMDbgYNQXJ0aHVyBXZlAwVwcgMFcG4GKU1D44CO5ryg44CP5Y+N5p+Q5Lq6BXNpAwVhcgkDAQYZZXh0cmVtZUxhc2VyBWRwBA8Fc2sJDwEGCTMzXzUGCTIzXzMGCTEzXzQGCTEyXzMGCzEzXzEwBgkxM18xBgkxM18zBXRtBA8FcGEDBWxpBIMQBXVkBhkxMTk5Nzk3MjY3XzAFY24GDeelnueLsQE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "38";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhdGYW5nWmhvdVRvcAVkcAQeA24GD0tuaWdodHMFbHYEYwVzawkPAQYJMjFfMgYJMTJfMwYJMzFfNQYJMzJfOAYJMTNfMwYLMTNfMTEGCTMxXzYFdG0ECgV1ZAYZMjQ1NzQzMTg3Ml8wBWxpBIaNIAVwcgMFc2kDBXZlAwVwYQMFcG4GA2EFY24GDeawuOWvkgE=";
            d0.noSkillArr = ["lockLife"];
            dealMax(d0);
            start0 = "2023-7-31";
            end0 = "2023-8-6";
            d0 = new BossTaskDefine();
            d0.name = "39";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBB4FbHYEYwVtcAYNS3VSb25nA24GF0xhc3RkYXlUYW5rBXNpAwVwbgYr5rKB5Zut5pil5Lio6Zuq546755KDBXVkBhkyNzE0NjQxNzc5XzEFcGEDBXZlAwVjbgYN6Zeq55S1BWxpBIaNIAVzawkPAQYJMTNfMwYJMTFfNwYJNDRfNQYJMTFfMQYJMjFfMgYJNDFfMQYJMjNfMwVwcgMFZHAEHgVwZQMB";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "40";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRUBBgkzNF8xBgkzNF80BgkzNV8xBgkzMl8yBgkzMl8zBgk0NF8yBgkzMl84BgsyM18xMQYLMjNfMTAGCzIxXzEyBXRtBCsDbgYZWm9tYmllQmF0dGxlBXVkBhU4NjE4NzA5M18xBXBuBhnnjovniYzljZrlk6UFcGECBW1wBhNIb3NwaXRhbDUFbHYEVQVoYgMFdmUDBWxpBJc4BXByAwVwZQMFZHAEHgE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "41";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBAYDbgYPU2hhcGVycwVwbgYr6ZmN6a2U44CO5pit44CP5YWw556zBWRwBB4Fc2kDBXBhAwV0bQQeBWx2BGMFdmUDBW1wBg1RaWFuWWUFdWQGGTIyOTkwNzkzNTZfMAVjbgYN5pe26buRBXBlAwVzawkVAQYJMTNfNAYJMTNfNQYJMTNfOAYLMTNfMTAGCTMzXzgGCzM0XzE0BgsyMV8xMwYJMTFfNwYJMTNfNgYJMzJfOAVwcgMB​";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "42";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwVzawkRAQYJNDJfNQYJMTNfOAYLMjNfMTEGCzM0XzE0BgkzNF80BgkyMV8yBgkzNF85BgsyMV8xMwVsdgRjBXByAwVwZQMFbGkEZAVhcgkDAQYZc25pcGVyQ2ljYWRhBWRwBB4FY24GDeS4luWKqwNuBg9Eb2N0b3IyBXBuBhvpsrLpuY8u6aKc5ZueBXNpAwVtcAYNWGluZ0d1BXBhAwV0bQSCLAV1ZAYZMjc1MTE0MzU3MF8wAQ==";
            d0.noSkillArr = ["summonShortLifeMax"];
            deal8(d0);
            start0 = "2023-8-7";
            end0 = "2023-8-13";
            d0 = new BossTaskDefine();
            d0.name = "43";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCR0BBgkxM184BgsxM18xMAYJMzRfNAYJMzVfMwYJMzFfOQYJNDFfMQYJMjFfOAYJMzRfOQYJMTJfNAYJMjJfNQYLMjJfMTAGCTMzXzEGCzIxXzEzBgk0M18xBXRtBHgFbGkEgl4FcGEDBWx2BGMDbgYVU2lsdmVyU2hpcAVtcAYJV29UdQV1ZAYZMzgwMTA3ODIyNl8wBXByAwVwZQMFdmUDBXBuBiXjgI7muKHpuKbjgI/okIzmlrAFZHAEHgVzaQMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "44";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwNuBhNZb3V0aFdvbGYFcHIDBXBlAwV1ZAYXNzAwNjU1NjI1XzAFc2sJDwEGCTQyXzUGCTQyXzMGCTQ0XzUGCTEzXzUGCTEzXzMGCTExXzcGCTExXzEFbGkEho0gBXNpAwVwbgYl5rKB5Zut5pil5Lio6a2U546LBWx2BGMFdG0EFAVwYQMFbXAGDVhpbmdHdQE=";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "45";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwNuBg9RaUh1YW5nBXByAwVwbgYl5rKB5Zut5pil5Lio6a2U546LBWFyCQMBBh9BcnRodXJSb2NrZXQxODAFdG0EEwVsdgRjBXZlAgV1ZAYXNzAwNjU1NjI1XzAFc2sJEwEGCTMzXzkGCTQxXzEGCTEyXzMGCzEyXzEyBgsyMV8xMwYJMzFfOAYJMjNfMgYJMjFfMQYJMjFfMgVsaQSBUgVjbgYN6LWk54uxBW1wBhNIb3NwaXRhbDUFcGUDBXNpAwVkcAQFAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "46";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBivnpo3msLTjgI7pk4PjgI/lsrjnjpYDbgYVS2luZ1JhYmJpdAVkcAQeBXBhAwV2ZQMFc2kDBXByAwVwZQMFbHYEYwVzawkTAQYJMzJfMgYJMTNfOAYJMjFfMgYLMjNfMTEGCTM1XzYGCTMxXzMGCTQ0XzYGCTEzXzUGCTM0XzcFbXAGEVNreVNjZW5lBXVkBhkyNjI5ODQxMzIzXzAFbGkEho0gBXRtBAoFY24GDeWvkum+mQE=";
            dealMax(d0);
            start0 = "2023-8-14";
            end0 = "2023-8-20";
            d0 = new BossTaskDefine();
            d0.name = "47";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBuBhXlvIDlv4Mx54K5BWRwBB4DbgYbQmFsbExpZ2h0bmluZwV0bQQkBWx2BGMFc2kDBXVkBhkxNDg4OTAzNTIzXzEFY24GDeWZqeaipgVwYQMFbXAGE0hhbkd1YW5nMgV2ZQMFbGkEgTkFcHIDBXBlAwVzawkRAQYJMTNfMwYJMTNfOAYJMTFfMQYJMzJfOAYJMzFfOAYJMjFfMgYJNDFfNAYJNDJfNQE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "48";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCR0BBgkzMV8xBgkzNF85BgszNF8xNAYJMzNfNAYJMzVfMQYJNDRfNgYJNDRfMgYJMzVfMwYJMzRfNwYJMzFfOQYJMjJfNgYJMjJfNAYLMzRfMTIGCzIzXzExBXRtBAoDbgYZT2ZmaWNlWm9tYmllBXVkBhkxMjg1NTM3NzUzXzAFY24GB+S6oQVzaQMFbXAGEVNreVNjZW5lBWx2BGMFcGEDBXZlAwVsaQSLKgVwbgYVTlJELuWtpuW+kgVwcgMFZHAEHgVwZQMB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "49";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GF1RodW5kZXJib2x0BXByAwVwbgYPTeaXoOelngVjbgYN54ux6ZmQBXNrCREBBgkxM184BgkzMV8xBgkxM181BgkxMl8zBgsxMl8xMwYJMTJfMgYJMzJfOAYJMzFfNQVkcAQeBXZlAwV1ZAYZMzMwNzU5MTEzNl8wBW1wBglXb1R1BWx2BGMFdG0EHgVsaQRfAQ==";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "50";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXByAwV2ZQMDbgYZU2tlbGV0YWxNYWdlBXBuBiPjgI7llJDpl6jjgI/jg4TOsgVwZQMFZHAEBQVzawkXAQYJMzFfNQYJMzJfMQYJMTNfOAYJMTNfMwYJMzFfOAYJMzFfMQYJMTFfMQYLMzFfMTIGCTMxXzkGCzIzXzEwBgkzNF80BXRtBBQFbGkEgxAFdWQGGTMwNjgzOTU3MTlfMAVjbgYN5Zyj5ZG9BXBhAwVzaQMFbXAGEVpob25nWGluBWx2BGMB";
            deal8(d0);
            start0 = "2023-8-21";
            end0 = "2023-8-27";
            d0 = new BossTaskDefine();
            d0.name = "51";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAwV2ZQMDbgYRU2tlbGV0b24FcG4GG+i2hee6p+mdk+S7lGcFcHIDBWRwBAUFc2sJEwEGCTExXzEGCTExXzcGCTQxXzEGCTQxXzIGCTQyXzUGCTMyXzEGCTMyXzgGCTMxXzIGCTIxXzYFbGkEgXoFcGUDBXNpAwVtcAYNQmFpU2hhBWx2BGMFdWQGGTIzMjAzNjMwODdfMAE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "52";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBB4DbgYRV2F0Y2hkb2cFdG0EgiwFbGkEHgV1ZAYZMjUzMjAwODI5OF8wBWx2BGMFcGEDBXNpAwVtcAYTSG9zcGl0YWwxBXByAwV2ZQMFcGUDBXBuBg9UaWZmYW55BXNrCRMBBgkzNF80BgkxM184BgkxM185BgsyMV8xMgYLMjNfMTEGCzEzXzEwBgkzM184BgkxMV8xBgkxM18zAQ==";
            d0.noSkillArr = ["everNoSkillEnemy"];
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "53";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXRtBCEFc2sJEQEGCTQxXzEGCTM1XzEGCTExXzcGCTEzXzUGCzMzXzEyBgk0NF81Bgk0NF82BgsyMV8xMgVzaQMDbgYZVHlwaG9vbldpdGNoBXZlAwVsdgRjBXBhAwVsaQSGjSAFZHAEHgVwZQMFdWQGFzIwNDE3ODIyMF8wBXBuBhXku7p+5Ya36ZuoBXByAwVtcAYLRGFvVGEB";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "54";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBlAwNuBhVQaXBlWm9tYmllBXRtBBoFbHYEYwVtcAYVTWFpblVwbGFuZAV2ZQMFZHAEHgVzaQMFY24GB+i2hQVzawkdAQYJMTFfMQYJMTNfMQYJMTNfOAYJMTNfOQYJMjFfMgYLMjFfMTAGCzIxXzExBgsyMV8xMwYLMzFfMTEGCTMyXzgGCzMzXzE0BgkzNV8zBgk0MV80Bgk0Ml81BWxpBAMFcG4GH+WcsOeFnu+8muS9muWQjQVwYQMFdWQGGTI1OTYzMTMyMDNfMAVwcgMB";
            d0.noSkillArr = ["handSensitive"];
            deal8(d0);
            start0 = "2023-8-28";
            end0 = "2023-9-3";
            d0 = new BossTaskDefine();
            d0.name = "55";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCQ8BBgkxMV83BgkxM180BgkyMl83BgkxMV8xBgsxMl8xNAYJNDRfNQYJNDJfNQV0bQQoA24GB0JhdAVwYQMFY24GDeayieedoQVsdgRjBWxpBIaNHwVtcAYJV29UdQVwcgMFcGUDBXZlAwVwbgYx44CQ6KGA5Z+f44CR5bCP6JCd6I6J5o6nBXVkBhkxNDEwNjIwNDM3XzcFc2kDAQ==";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "56";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhkzNzgyODY1NDQxXzAFcGUDBWxpBIhMBXBuBivjgI7npZ7pvpnop4nphpLjgI/nhK8FZHAEHgVzawkVAQYLMTJfMTEGCTEzXzQGCTEyXzUGCzEyXzEzBgkzMl84BgkzNV8xBgk0Ml81BgkxMl83BgkxMl8zBgkzMV81BXBhAwVzaQMDbgYTRmlnaHRLaW5nBXRtBB4FbHYEYwV2ZQMFcHIDBW1wBhdGbHlTa3lTY2VuZQVjbgYN54uC5oCSAQ==";
            deal8(d0);
            start0 = "2023-9-4";
            end0 = "2023-9-10";
            d0 = new BossTaskDefine();
            d0.name = "57";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXNrCRkBBgk0NF82BgkzMl84BgsxMl8xMAYJMTNfOQYJMjJfOQYJMjFfNwYJMTFfNwYJMzNfNgYLMTNfMTAGCzEyXzE0BgkyMV84BgkzNF80BXRtBDwFbGkEgXoFdWQGGTE0NzYyODA4NzBfMQVwYQMFc2kDBW1wBglXb1R1BWx2BGMFdmUDBXByAwVwbgYv44CO5oOK6Jm544CPwrfms6rmmpfmu7QFcGUDBWFyCQMBBhV5ZWFyRHJhZ29uBWRwBB4DbgYLV2FuZGEB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "58";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXZlAwNuBh1ab21iaWVGb290YmFsbAVsaQSjIgVwZQMFY24GDee8oOmZkAVtcAYNWGluZ0d1BXVkBhkyNDE1NTI0NzMyXzAFcGEDBXBuBg13eW41NDgFc2kDBXRtBFsFc2sJFwEGCTIxXzIGCTQ0XzYGCzMxXzEyBgk0MV80BgszNF8xMAYJMzJfMgYLMzFfMTEGCTM0XzQGCzIzXzExBgkxMV8yBgkzMV8xBWRwBB4FcHIDBWx2BGMB";
            deal8(d0);
            start0 = "2023-9-11";
            end0 = "2023-9-17";
            d0 = new BossTaskDefine();
            d0.name = "59";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWx2BGMFcGUDBW1wBhNIb3NwaXRhbDIFbGkEho0gBXVkBhkxMjg0MDcwMDc2XzAFcGEDBXBuBinil6TlvJHmnYDil6LCt+eliOaEvwV0bQQ8A24GG0JhbGxMaWdodG5pbmcFc2sJEQEGCTM1XzEGCTEzXzMGCTEzXzQGCTIyXzcGCTQyXzUGCTQyXzYGCTExXzEGCTQ0XzUFcHIDBXNpAwVkcAQeBXZlAwE=";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "60";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWFyCQMBBhlleHRyZW1lTGFzZXIFZHAEHgNuBhNHaG9zdER1a2UFdG0EPAVsaQSBegVwYQMFc2kDBW1wBhNIb3NwaXRhbDUFdWQGGTE0MjgyMDg5NDBfMAVwcgMFcGUDBXZlAwVwbgYx44CO5pGG54OC44CP6L6+55Om6YeM5rCPBXNrCREBBgkxM184BgkxM185BgkzMV81BgkzNV8xBgkyMV83BgkxMV8xBgkxMV83Bgk0M18xAQ==";
            deal8(d0);
            start0 = "2023-9-18";
            end0 = "2023-9-24";
            d0 = new BossTaskDefine();
            d0.name = "61";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBhAgVzaQIDbgYPUWlIdWFuZwVwbgYl44CO5LqU5bm044CP5bCP5piOBWRwBBQFcGUCBXRtBIEgBWx2BGMFaGIDBW1wBglXb1R1BXVkBhkzODMzMTI2NjcyXzAFY24GDeaAkuWQvAV2ZQIFYXIJAwEGFWZpcnN0UmlmbGUFc2sJFQEGCTEyXzgGCTEyXzUGCTEzXzgGCTEzXzkGCTQzXzEGCTQxXzIGCTIyXzMGCTIyXzEGCTIyXzIGCTE0XzUFbGkEgSoB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "62";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWNuBg3lvJHnpZ4DbgYdWm9tYmllQWlyRm9yY2UFdWQGGTM3MjIzOTg2MDZfMAVsaQQBBXNrCREBBgkxMl8zBgsxMl8xMQYJMTNfMwYJMTNfNQYJMjFfMgYJMjFfNgYJMjNfMQYLMzNfMTIFZHAEHgVsdgQoBWFyCQMBBhNsaWdodENvbmUFbXAGDVhpbmdHdQVwbgYr5oiR6Ieq5qiq5YiA5ZCR5aSp56yRAQ==";
            deal8(d0);
            start0 = "2023-9-25";
            end0 = "2023-10-1";
            d0 = new BossTaskDefine();
            d0.name = "63";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBB4FbXAGEVNodWFuZ1RhA24GDUljZU1hbgV1ZAYXNzA1NTQ5OTcyXzAFdmUCBXByAwVwbgYl5Yaw5qKm44CO5oSa6ICF44CPBXBlAwVsaQSBPgV0bQQQBWx2BGMFc2sJEwEGCzMzXzExBgsyMV8xMQYJMzFfMQYJMzFfMgYJMzVfNAYLMzFfMTEGCTQyXzYGCTExXzEGCTMyXzQB";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "64";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBB4DbgYRU25vd0dpcmwFc2kDBXNrCRMBBgkxMV8xBgk0MV81BgszMV8xMgYLMjJfMTIGCTIyXzcGCTExXzcGCTQ0XzUGCTMyXzIGCTMyXzYFdG0EgXAFbGkEh2gFdWQGFzg2MTkyODgzOV8wBXBhAwVjbgYN5peg6ZmQBW1wBg1Eb25nV3UFbHYEYwVwcgIFcGUDBXZlAgVwbgYr6ZmN6a2U44CO57WQ44CP57Sr6IuPBWhiAgE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "65";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBW1wBhNIb3NwaXRhbDUFZHAEHgNuBhVTb3VyY2VDb2RlBXVkBhkyMjEzOTEwODM3XzEFc2sJHQEGCzEyXzEwBgsxMl8xMwYJMTNfOAYJMTJfNQYJMTJfOQYJMTNfOQYJMTJfMgYJMTJfMwYJMzFfNAYJMzFfOAYJMzFfNQYJMzRfNAYLMzRfMTQGCTIxXzgFcGEDBXRtBDIFc2kDBXByAwVsaQSDdAVwZQMFdmUDBWx2BGMFcG4GLeOAjuWJp+e7iOOAj8K354uCwrfkuIkFY24GDeWgleeOiQE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "66";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBA24GF1pvbWJpZVNob290BXBuBivmtYXlv4bjgI7mmq7jgI/oirHovp4FdWQGGTI0OTA1NDAyNzRfMAVzawkTAQYJMTNfOAYLMzFfMTEGCTMxXzgGCTMxXzUGCTMyXzEGCTQzXzEGCTM1XzEGCTMyXzIGCTMzXzIFbGkEj1AFbXAGD1lhbmdNZWkFdG0EHgVsdgRjAQ==";
            deal8(d0);
            start0 = "2023-10-2";
            end0 = "2023-10-8";
            d0 = new BossTaskDefine();
            d0.name = "67";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBlAwVhcgkDAQYVcm9ja2V0Q2F0ZQVkcAQeA24GC1dhbmRhBXNpAwV0bQQKBWxpBIaNIAV1ZAYZMzY2NzQ2NTM4Ml8wBXBhAwVtcAYTSGFuR3Vhbmc1BWx2BGMFaGIDBXByAwV2ZQMFcG4GGee0q+WmluahheiTnQVzawkZAQYJMzFfMgYJMTJfMwYJMTFfNQYJMTNfMwYJMTNfNAYJMTRfNQYLMTJfMTAGCzEzXzExBgsxMl8xMwYLMTJfMTEGCTMzXzgGCTQ0XzUB";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "68";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWRwBBQFc2kDBWx2BGMFdmUDBXByAwV1ZAYZMzkwOTQ1NjkxMF8xA24GD0tuaWdodHMFcGEDBXBuBiXjgI7npZ7nlYzjgI/ng6fnlLcFc2sJDwEGCTIxXzIGCTQ0XzUGCTEyXzMGCTQxXzQGCTQyXzUGCTIyXzcGCzMzXzEyBXBlAwVsaQSGjSAFbXAGF0ZhbmdaaG91VG9wAQ==";
            dealMax(d0);
            d0 = new BossTaskDefine();
            d0.name = "69";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBlAgV2ZQMDbgYTSG9va1dpdGNoBWhiAwVkcAQeBXNpAwVzawkRAQYLMzNfMTIGCTE0XzIGCTM1XzEGCzEyXzExBgkzNF85BgsyMl8xMwYJMTRfNAYJMTJfOQVsaQQPBXBhAgVsdgRjBWNuBg3prZTnjosFdG0ECgVtcAYTSGFuR3VhbmczBXVkBhkyMjU0NzY4MjgwXzAFcHIDBXBuBivmlZHotY7jgI7miJjjgI/lpKnkvb8B";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "70";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBWxpBIaNIAVtcAYXRmx5U2t5U2NlbmUFcGUDBXNrCRMBBgk0Ml81Bgk0NF81BgkxMV83BgkxM181BgkyMV8yBgsyM18xMQYJMzVfMQYJMjJfOQYLMjJfMTAFZHAEHgVwcgMFdG0EKANuBhtab21iaWVDbGVhdmVyBWx2BGMFdWQGFzcyNDg4MTgzMV8wBXNpAwV2ZQMFcG4GE+Wkp+WKm+WPsgVwYQMB";
            dealMax(d0);
            start0 = "2023-10-9";
            end0 = "2023-10-15";
            d0 = new BossTaskDefine();
            d0.name = "71";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXBlAwV2ZQIDbgYRU3dpbUtpbmcFcG4GJea4heatjOaXoOaJgOeVj+aDpwVkcAQeBXNpAwVzawkTAQYLMzNfMTEGCTM1XzEGCTIyXzYGCTEzXzUGCzIzXzExBgkyMl81Bgk0MV81Bgk0NF8zBgkyMl85BWxpBIQmBXBhAgVjbgYN6Ie05ZG9BXRtBFoFbXAGE0hvc3BpdGFsNQVsdgRjBXVkBhkxNzk1OTQ1Njg1XzEFcHIDBWhiAwE=";
            deal8(d0);
            d0 = new BossTaskDefine();
            d0.name = "72";
            d0.start = start0;
            d0.end = end0;
            d0.o = "CgsBBXVkBhc3MTc0ODE2MDhfMAVtcAYNWGluZ0d1BXNpAwVzawkXAQYJMTFfMQYJMTFfNwYLMTJfMTQGCTQxXzEGCTIxXzYGCTEyXzMGCTMyXzEGCTIxXzMGCTQ0XzMGCTQ0XzYGCzM0XzExBXBuBivmtYXlv4bjgI7lsprjgI/lpI/nrJkFcGECBWRwBAEFdG0EgTQDbgYZU2tlbGV0YWxNYWdlBWNuBg3moqbms6oFcHIDBXBlAgVsaQRkBXZlAwVsdgRjAQ==";
            deal8(d0);
         }
      }
      
      private static function addDefine(d0:BossTaskDefine, outB0:Boolean = false) : void
      {
         var obj0:Object = null;
         var xx0:int = 0;
         if(outB0)
         {
            outArr.push(d0);
         }
         else
         {
            d0.name = "0" + nameIndex;
            d0.start = "";
            d0.end = "";
            d0.authorMul = 1;
            arr.push(d0);
            ++nameIndex;
         }
         ARR.push(d0);
         if(OBJ.hasOwnProperty(d0.name))
         {
            INIT.showError("BossTaskDefine已经包含name：" + d0.name);
         }
         else
         {
            OBJ[d0.name] = d0;
         }
         if(Gaming.testCtrl.canCheatingB())
         {
            obj0 = BossEditMethod.codeToObj(d0.o);
            if(obj0.n == "GasDefenseZombie")
            {
               xx0 = 0;
            }
         }
      }
      
      public static function getDefine(name0:String) : BossTaskDefine
      {
         return OBJ[name0];
      }
      
      public static function getARR() : Array
      {
         return ARR;
      }
      
      public static function getArrWeek(weekIndex0:int) : Array
      {
         var arr0:Array = null;
         var c0:int = 0;
         var a2:Array = null;
         var maxLen0:int = 0;
         var first0:int = 0;
         var i:int = 0;
         var index0:int = 0;
         var ri0:int = 0;
         var d0:BossTaskDefine = null;
         arr0 = [];
         c0 = weekIndex0 - weekFirst;
         if(c0 >= 0)
         {
            a2 = [];
            maxLen0 = int(arr.length);
            first0 = c0 * GAP;
            for(i = 0; i < GAP; i++)
            {
               index0 = first0 + i;
               ri0 = index0 % maxLen0;
               d0 = arr[ri0];
               a2.push(d0);
            }
            arr0 = arr0.concat(a2);
         }
         return arr0.concat(outArr);
      }
      
      private static function deal6(d0:BossTaskDefine) : void
      {
         d0.giftCnArr = GET_GIFT(6);
         d0.lifeArr = BossTaskDefine.SIMPLE_LIFE;
         addDefine(d0);
      }
      
      private static function deal2(d0:BossTaskDefine) : void
      {
         d0.giftCnArr = GET_GIFT(2);
         d0.lifeArr = BossTaskDefine.SIMPLE_LIFE2;
         addDefine(d0);
      }
      
      private static function deal1(d0:BossTaskDefine) : void
      {
         d0.giftCnArr = GET_GIFT(1);
         d0.lifeArr = BossTaskDefine.SIMPLE_LIFE1;
         addDefine(d0);
      }
      
      private static function dealNormal(d0:BossTaskDefine, outB0:Boolean = false) : void
      {
         d0.giftCnArr = GET_GIFT(8);
         addDefine(d0,outB0);
      }
      
      private static function deal8(d0:BossTaskDefine, outB0:Boolean = false) : void
      {
         d0.giftCnArr = GET_GIFT(8);
         d0.lifeArr = BossTaskDefine.HIGH_LIFE_1000;
         d0.dpsArr = BossTaskDefine.HIGH_DPS_ARR;
         addDefine(d0,outB0);
      }
      
      private static function dealMax(d0:BossTaskDefine, outB0:Boolean = false) : void
      {
         d0.giftCnArr = GET_GIFT(8);
         d0.lifeArr = BossTaskDefine.HIGH_LIFE_MAX;
         d0.dpsArr = BossTaskDefine.HIGH_DPS_ARR;
         addDefine(d0,outB0);
      }
      
      private static function GET_GIFT(num0:int) : Array
      {
         var giftArr0:Array = null;
         giftArr0 = [];
         giftArr0.push("纪念币*12、强化石*20、血石*20、69级零件箱*3");
         if(num0 >= 1)
         {
            giftArr0.push("纪念币*13、强化石*20、血石*20、周年碎片*50");
         }
         if(num0 >= 2)
         {
            giftArr0.push("纪念币*13、神武碎片*15、神护碎片*15、镭晶*40");
         }
         if(num0 >= 3)
         {
            giftArr0.push("纪念币*14、神武碎片*20、神护碎片*20、武器宝石箱*6");
         }
         if(num0 >= 4)
         {
            giftArr0.push("纪念币*15、武器宝石箱*6、装备宝石箱*6");
         }
         if(num0 >= 5)
         {
            giftArr0.push("纪念币*16、武器宝石箱*6、装备宝石箱*6、竞技宝箱*1");
         }
         if(num0 >= 6)
         {
            giftArr0.push("纪念币*16、武器宝石箱*8、装备宝石箱*8、竞技宝箱*1");
         }
         if(num0 >= 7)
         {
            giftArr0.push("纪念币*16、武器宝石箱*8、装备宝石箱*8、竞技宝箱*1");
         }
         return giftArr0;
      }
      
      public static function toText() : void
      {
         var d0:BossTaskDefine = null;
         var obj0:Object = null;
         var name0:String = null;
         var skillArr0:Array = null;
         var bd0:NormalBodyDefine = null;
         var cn0:String = null;
         var skillCnArr0:Array = null;
         var s0:String = null;
         INIT.tempTrace("----------------------------------------------------");
         for each(d0 in ARR)
         {
            if(Boolean(d0.lifeArr))
            {
               obj0 = BossEditMethod.codeToObj(d0.o);
               name0 = obj0.n;
               skillArr0 = obj0.sk;
               bd0 = Gaming.defineGroup.body.getDefine(name0);
               cn0 = bd0.cnName;
               skillCnArr0 = Gaming.defineGroup.skill.getCnArrByNameArr(BossEditSkill.getSkillArrByIdArr(skillArr0));
               s0 = cn0 + "  " + d0.lifeArr[0] + "  " + obj0.dp + "  " + obj0.li + "  " + skillCnArr0;
               INIT.tempTrace(s0);
            }
         }
         INIT.tempTrace("----------------------------------------------------");
      }
   }
}

