package dataAll._app.goods.define
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.ui.GatherColor;
   
   public class PriceType
   {
      
      public static var COIN:String = "coin";
      
      public static var MONEY:String = "money";
      
      public static var SCORE:String = "score";
      
      public static var ARENA_STAMP:String = "arenaStamp";
      
      public static var EXPLOIT_CARDS:String = "exploitCards";
      
      public static var TAX_STAMP:String = "taxStamp";
      
      public static var ZONGZI:String = "zongzi";
      
      public static var ANNI_COIN:String = "anniCoin";
      
      public static var PUMPKIN:String = "pumpkin";
      
      public static var DEMBALL:String = "demBall";
      
      public static var TENCOIN:String = "tenCoin";
      
      public static var PARTSCOIN:String = "partsCoin";
      
      public static var typeArr:Array = [COIN,MONEY,SCORE,ARENA_STAMP,EXPLOIT_CARDS,TAX_STAMP,ZONGZI,ANNI_COIN,PUMPKIN,DEMBALL,TENCOIN,PARTSCOIN];
      
      public static var coinCnName:String = "银币";
      
      public static var moneyCnName:String = "黄金";
      
      public static var scoreCnName:String = "积分";
      
      public static var arenaStampCnName:String = "优胜券";
      
      public static var exploitCardsCnName:String = "军队功勋牌";
      
      public static var taxStampCnName:String = "商券";
      
      public static var zongziCnName:String = "粽子";
      
      public static var anniCoinCnName:String = "纪念币";
      
      public static var demBallCnName:String = "万能球";
      
      public static var tenCoinCnName:String = "十年币";
      
      public static var partsCoinCnName:String = "零件券";
      
      public static var pumpkinCnName:String = "桂花糕";
      
      public static const pumpkinIconMcLabel:String = "guiCake";
      
      private static var exchangeArr:Array = [ARENA_STAMP,EXPLOIT_CARDS,ZONGZI,ANNI_COIN,PUMPKIN,DEMBALL,TENCOIN,PARTSCOIN];
      
      public static var thingsArr:Array = [ARENA_STAMP,EXPLOIT_CARDS,TAX_STAMP,ZONGZI,DEMBALL];
      
      public static var defineAutoBackArr:Array = [EXPLOIT_CARDS,ANNI_COIN,ZONGZI,PUMPKIN,DEMBALL,TENCOIN,PARTSCOIN];
      
      public function PriceType()
      {
         super();
      }
      
      public static function getCnName(type0:String) : String
      {
         return PriceType[type0 + "CnName"];
      }
      
      public static function getColor(type0:String) : String
      {
         if(type0 == COIN)
         {
            return "#66FFFF";
         }
         if(type0 == MONEY)
         {
            return "#FFFF00";
         }
         if(type0 == SCORE)
         {
            return "#FF99FF";
         }
         if(type0 == EXPLOIT_CARDS || type0 == TENCOIN)
         {
            return "#00FF00";
         }
         if(type0 == DEMBALL)
         {
            return GatherColor.purplenessColor;
         }
         return "#FF9900";
      }
      
      public static function color(v0:*, type0:String) : String
      {
         return ComMethod.color(String(v0),getColor(type0));
      }
      
      public static function getActionName(type0:String) : String
      {
         if(exchangeArr.indexOf(type0) >= 0)
         {
            return "兑换";
         }
         return "购买";
      }
      
      public static function getMethodCn(type0:String) : String
      {
         return getCnName(type0) + getActionName(type0);
      }
      
      public static function getInfo(type0:String) : String
      {
         if(type0 == ZONGZI)
         {
            return "端午节期间，参加捡粽子活动获得。";
         }
         if(type0 == ANNI_COIN)
         {
            return "参加指定活动获得。";
         }
         if(type0 == PUMPKIN)
         {
            return "参加2024年中秋博饼获得，活动过后将自动清零。";
         }
         if(type0 == DEMBALL)
         {
            return "修罗模式掉落。";
         }
         if(type0 == TENCOIN)
         {
            return "十周年活动获得。";
         }
         if(type0 == PARTSCOIN)
         {
            return "虚幻塔获得。";
         }
         return "";
      }
      
      public static function getPriceIconMcLabel(type0:String) : String
      {
         if(type0 == PriceType.PUMPKIN)
         {
            return PriceType.pumpkinIconMcLabel;
         }
         return type0;
      }
   }
}

