package dataAll.pro.dataList
{
   import dataAll._base.NormalDefineGroup;
   
   public class DataListDefineGroup extends NormalDefineGroup
   {
      
      public static var pro_arr:Array = null;
      
      public function DataListDefineGroup()
      {
         super();
         defineClass = DataListDefine;
      }
      
      public function getDefine(name0:String) : DataListDefine
      {
         return obj[name0];
      }
      
      public function getValue(name0:String, lv0:int) : Number
      {
         var d0:DataListDefine = getNormalDefine(name0) as DataListDefine;
         if(Boolean(d0))
         {
            return d0.getValue(lv0);
         }
         INIT.showError("不存在：" + name0 + "，lv:" + lv0);
         return 0;
      }
      
      public function getSum(name0:String, lv0:int) : Number
      {
         var d0:DataListDefine = getNormalDefine(name0) as DataListDefine;
         if(<PERSON>olean(d0))
         {
            return d0.getSum(lv0);
         }
         INIT.showError("不存在：" + name0 + "，lv:" + lv0);
         return 0;
      }
      
      public function testAllData() : String
      {
         var d0:DataListDefine = null;
         var str0:String = "";
         for each(d0 in arr)
         {
            str0 += "\n" + d0.testAllData();
         }
         return str0;
      }
   }
}

