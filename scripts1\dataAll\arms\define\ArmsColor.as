package dataAll.arms.define
{
   import dataAll.equip.define.EquipColor;
   
   public class ArmsColor extends EquipColor
   {
      
      public static const canRefiningArr:Array = [RED,BLACK];
      
      private static const skin2Arr:Array = ["sniperCicada","rifleHornet","shotgunSkunk"];
      
      private static const skin1:Array = ["Gold"];
      
      private static const skin2:Array = ["Gold","Gold2"];
      
      public function ArmsColor()
      {
         super();
      }
      
      public static function getUIDpsMul(color0:String) : Number
      {
         if(moreBlackB(color0))
         {
            return 1.3;
         }
         return 1;
      }
      
      private static function getSkinArr(d0:ArmsDefine) : Array
      {
         if(skin2Arr.indexOf(d0.name) >= 0)
         {
            return skin2;
         }
         return skin1;
      }
      
      public static function swapSkin(d0:ArmsDefine, now0:String) : String
      {
         var last0:String = null;
         var f0:int = 0;
         var skinArr0:Array = getSkinArr(d0);
         var first0:String = d0.bodyImgRange[0];
         if(now0 != "")
         {
            last0 = now0.replace(first0,"");
            f0 = skinArr0.indexOf(last0) + 1;
            if(f0 > skinArr0.length - 1)
            {
               now0 = "";
            }
            else
            {
               now0 = first0 + skinArr0[f0];
            }
         }
         else
         {
            now0 = first0 + skinArr0[0];
         }
         return now0;
      }
   }
}

