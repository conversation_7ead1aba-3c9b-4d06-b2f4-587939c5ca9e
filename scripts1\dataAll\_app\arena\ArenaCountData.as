package dataAll._app.arena
{
   import com.sounto.utils.StringMethod;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._player.PlayerData;
   
   public class ArenaCountData
   {
      
      public var winB:Boolean = false;
      
      public var score:Number = 0;
      
      public var stampNum:int = 0;
      
      public var startNum:int = 1;
      
      public var baseScore:Number = 0;
      
      public var streakScore:Number = 0;
      
      public var revengeScore:Number = 0;
      
      public function ArenaCountData()
      {
         super();
      }
      
      private static function getTopRatio(top1:int, trueTop1:Number, top2:int) : Number
      {
         var topMul0:Number = getTopMul(top2);
         var extraMul0:Number = 0;
         if(trueTop1 >= 1)
         {
            extraMul0 = getExtraTopMul(top1,top2);
         }
         return topMul0 - 0.4 + (extraMul0 - 0.15) * 0.47;
      }
      
      private static function getTopMul(top0:int) : Number
      {
         if(top0 <= 10)
         {
            return 1 / Math.pow(top0,0.05);
         }
         return 1 / Math.pow(top0,0.2) + 0.25;
      }
      
      private static function getExtraTopMul(top1:int, top2:int) : Number
      {
         var ctop0:int = top1 - top2;
         if(ctop0 < 1)
         {
            ctop0 = 1;
         }
         var v0:Number = (Math.pow(ctop0,0.1) - 1) / 1.52;
         if(ctop0 < 10)
         {
            v0 = 0.17;
         }
         return v0;
      }
      
      public static function test() : void
      {
         var d0:TopBarDefineGroup = null;
         var darr0:Array = Gaming.defineGroup.top.getArrByGather("arena");
         for each(d0 in darr0)
         {
            trace(d0.cnName + "----------------------------------");
            testTop(d0,10000,1);
            testTop(d0,1000,1);
            testTop(d0,100,1);
            testTop(d0,10,1);
            testTop(d0,2,1);
            testTop(d0,0,1);
            testTop(d0,1,10000);
            testTop(d0,1,1000);
            testTop(d0,1,100);
            testTop(d0,1,10);
            testTop(d0,1,2);
            testTop(d0,10001,10000);
            testTop(d0,10001,1000);
            testTop(d0,10001,100);
            testTop(d0,10001,10);
            testTop(d0,10001,2);
         }
      }
      
      private static function testTop(topDefine0:TopBarDefineGroup, top1:Number, top2:Number) : void
      {
         var win0:ArenaCountData = new ArenaCountData();
         win0.inOneData(true,top1,top1,top2,topDefine0,0,false);
         var fail0:ArenaCountData = new ArenaCountData();
         fail0.inOneData(false,top1,top1,top2,topDefine0,0,false);
         var s0:String = win0.score + ",  " + win0.stampNum + ",  " + fail0.score + ",  " + fail0.stampNum;
         trace(s0);
      }
      
      public function init() : void
      {
         this.winB = false;
         this.score = 0;
         this.startNum = 0;
         this.baseScore = 0;
         this.streakScore = 0;
         this.revengeScore = 0;
      }
      
      public function clearScore() : void
      {
         this.score = 0;
         this.baseScore = 0;
         this.streakScore = 0;
         this.revengeScore = 0;
      }
      
      public function inOnlyWinData(winB0:Boolean) : void
      {
         this.winB = winB0;
      }
      
      public function inData(winB0:Boolean, pd1:PlayerData, top1:Number, trueTop1:Number, rivalTopData0:TopBarData, randomB0:Boolean = true) : void
      {
         this.inOneData(winB0,top1,trueTop1,rivalTopData0.rank,rivalTopData0.define,pd1.arena.save.streakNum,randomB0);
      }
      
      private function inOneData(winB0:Boolean, top1:Number, trueTop1:Number, top2:Number, topDefine2:TopBarDefineGroup, streakNum0:Number, randomB0:Boolean = true) : void
      {
         this.init();
         this.winB = winB0;
         if(top2 >= 10001)
         {
            topDefine2 = Gaming.defineGroup.top.getDefine("arena_5");
         }
         var scoreArr0:Array = StringMethod.toNumberArr(topDefine2.info);
         var scoreMax0:int = int(scoreArr0[0]);
         var scoreMin0:int = int(scoreArr0[1]);
         var stampAdd0:int = int(scoreArr0[2]);
         var topRatio0:Number = getTopRatio(top1,trueTop1,top2);
         if(winB0)
         {
            this.baseScore = Math.ceil(topRatio0 * (scoreMax0 - scoreMin0) + scoreMin0);
            this.startNum = Math.ceil(topRatio0 * 3 + 1.1);
         }
         else
         {
            this.baseScore = Math.ceil(topRatio0 * (15 - 5) + 5);
            this.startNum = 1;
         }
         if(randomB0)
         {
            this.baseScore += -int(Math.random() * 7) + 3;
         }
         if(this.baseScore < 0)
         {
            this.baseScore = 0;
         }
         this.score = this.baseScore + this.streakScore + this.revengeScore;
         if(this.score < 0)
         {
            this.score = 0;
         }
         if(winB0)
         {
            this.stampNum = this.baseScore + stampAdd0;
         }
         else
         {
            this.stampNum = Math.ceil(topRatio0 * (40 - 20) + 20) + 20;
         }
      }
      
      public function test2() : void
      {
         var da0:ArenaCountData = new ArenaCountData();
         for(var i:int = 1; i < 2000; i++)
         {
            trace(i + ":" + getExtraTopMul(i,0));
         }
      }
   }
}

