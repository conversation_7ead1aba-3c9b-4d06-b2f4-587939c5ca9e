package com.sounto.hit.anyShape
{
   public class AnyShapeNode
   {
      
      public var x:int = 0;
      
      public var y:int = 0;
      
      public var left:int = 0;
      
      public var right:int = 0;
      
      public var up:int = 0;
      
      public var down:int = 0;
      
      public var maxY:int = 0;
      
      public var minY:int = 0;
      
      public var maxX:int = 0;
      
      public var minX:int = 0;
      
      public var hitB:Boolean = false;
      
      public function AnyShapeNode()
      {
         super();
      }
   }
}

