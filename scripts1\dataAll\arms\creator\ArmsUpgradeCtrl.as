package dataAll.arms.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.arms.ArmsData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.items.creator.ItemsUpgradeCtrl;
   import dataAll.must.define.MustDefine;
   
   public class ArmsUpgradeCtrl
   {
      
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function ArmsUpgradeCtrl()
      {
         super();
      }
      
      public static function get maxAddLevel() : Number
      {
         return CF.getAttribute("maxAddLevel");
      }
      
      public static function set maxAddLevel(v0:Number) : void
      {
         CF.setAttribute("maxAddLevel",v0);
      }
      
      public static function getMaxAddLevel(da0:ArmsData) : Number
      {
         if(da0.save.upgradeB)
         {
            return maxAddLevel;
         }
         return 0;
      }
      
      public static function init() : void
      {
         maxAddLevel = 19;
      }
      
      private static function color(str0:String) : String
      {
         return ComMethod.color(str0,"#FFFF00");
      }
      
      public static function canUpgradeB(da0:ArmsData) : Boolean
      {
         return da0.save.addLevel < getMaxAddLevel(da0);
      }
      
      public static function getStateStr(da0:ArmsData, beforeB0:Boolean = true) : String
      {
         var s0:ArmsSave = da0.save;
         var lv0:int = s0.getTrueLevel();
         var max0:int = s0.itemsLevel + getMaxAddLevel(da0);
         var str0:String = "";
         if(lv0 > max0)
         {
            str0 += ComMethod.color("<b>已升级至最高等级</b>","#00FF00");
         }
         else
         {
            str0 += ComMethod.color("<b>" + (beforeB0 ? "当前" : "升级后") + lv0 + "级</b>","#00FF00") + ComMethod.color(beforeB0 ? "(最高可升至" + max0 + "级)" : "","#999999");
            str0 += "\n基础战斗力：" + color(da0.getTestBaseShowDps() + "");
         }
         return str0;
      }
      
      public static function getAfterData(da0:ArmsData) : ArmsData
      {
         var addLv0:int = 1;
         var s0:ArmsSave = da0.save;
         var lv0:int = s0.getTrueLevel();
         var dps0:Number = da0.getTestBaseShowDps();
         var dps2:Number = countUpgradeDps(dps0,1,lv0);
         var affter_s0:ArmsSave = da0.save.copy();
         ++affter_s0.addLevel;
         var affter_da0:ArmsData = new ArmsData();
         affter_da0.inData_bySave(affter_s0,null,false);
         affter_da0.fleshOriginalData();
         affter_s0.hurtRatio = ArmsDataCreator.getHurt(affter_da0,dps2 * da0.getDpsMul());
         affter_da0.fleshOriginalData();
         return affter_da0;
      }
      
      private static function countUpgradeDps(baseDps0:Number, addLv0:int, nowLv0:int) : Number
      {
         var per1:Number = NaN;
         var v2:Number = NaN;
         var lv1:int = Gaming.defineGroup.normal.getLvInArmsDps(baseDps0);
         var min1:Number = Gaming.defineGroup.normal.getArmsDps(lv1 - 1);
         var max1:Number = Gaming.defineGroup.normal.getArmsDps(lv1);
         var min2:Number = Gaming.defineGroup.normal.getArmsDps(lv1 - 1 + addLv0);
         var max2:Number = Gaming.defineGroup.normal.getArmsDps(lv1 + addLv0);
         var _max:Number = Gaming.defineGroup.normal.getArmsDps(nowLv0 + addLv0 + 5);
         if(min1 >= max1 || min2 >= max2)
         {
            return baseDps0;
         }
         per1 = (baseDps0 - min1) / (max1 - min1);
         v2 = min2 + (max2 - min2) * per1;
         if(v2 > _max)
         {
            v2 = _max;
         }
         return v2;
      }
      
      public static function upgradeOne(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var after_da0:ArmsData = getAfterData(da0);
         s0.hurtRatio = after_da0.save.hurtRatio;
         s0.addLevel += 1;
         if(da0.placeType == "bag")
         {
            da0.fleshData_byEquip(Gaming.PG.DATA.getMerge());
         }
         else
         {
            da0.fleshData_byMeEquip();
         }
      }
      
      public static function getMust(da0:ArmsData) : MustDefine
      {
         return ItemsUpgradeCtrl.getArmsMust(da0,1);
      }
   }
}

