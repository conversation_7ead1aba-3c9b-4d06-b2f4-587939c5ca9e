package dataAll.pet.gene
{
   import dataAll.equip.define.EquipColor;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.pet.gene.save.GeneSaveGroup;
   
   public class GeneDataGroup extends ItemsDataGroup
   {
      
      public var saveGroup:GeneSaveGroup;
      
      public function GeneDataGroup()
      {
         super();
         dataType = ItemsDataGroup.TYPE_GENE;
         placeType = ItemsDataGroup.PLACE_BAG;
      }
      
      override public function inData_bySaveGroup(sg0:ItemsSaveGroup) : void
      {
         var n:* = undefined;
         var s0:GeneSave = null;
         var da0:GeneData = null;
         clearData();
         this.saveGroup = sg0 as GeneSaveGroup;
         for(n in sg0.arr)
         {
            s0 = sg0.arr[n];
            da0 = new GeneData();
            da0.inData_bySave(s0,normalPlayerData);
            da0.setPlaceType(placeType);
            dataArr[n] = da0;
            _siteDataArr = null;
         }
      }
      
      override public function sort(dg0:ItemsDataGroup) : void
      {
         sortByTempSortId("type");
      }
      
      override public function addSave(s0:ItemsSave, fleshSaveGroupB0:Boolean = true) : IO_ItemsData
      {
         var da0:GeneData = new GeneData();
         da0.inData_bySave(s0 as GeneSave,playerData);
         addData(da0,fleshSaveGroupB0);
         return da0;
      }
      
      override public function getSaveGroup() : ItemsSaveGroup
      {
         return this.saveGroup;
      }
      
      override public function getGripType() : String
      {
         return "equipGrip";
      }
      
      override public function getComposeTextTip() : String
      {
         return "把指定条件的基因体分解成强化剂";
      }
      
      public function getArrByPetName(name0:String, minColor0:String) : Array
      {
         var da0:GeneData = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            if(da0.save.name == name0 && EquipColor.firstMax(da0.save.color,minColor0,true))
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
   }
}

