package dataAll.equip.vehicle
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.body.attack.BodyAttackDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.bullet.BulletDefine;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.must.define.MustSimpleThingsDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.define.SkillDescrip;
   
   public class VehicleDataCreator
   {
      
      public function VehicleDataCreator()
      {
         super();
      }
      
      public static function getSave(name0:String, lv0:int) : VehicleSave
      {
         var d0:VehicleDefine = Gaming.defineGroup.vehicle.getDefine(name0);
         if(!(d0 is VehicleDefine))
         {
            INIT.showError("找不到定义VehicleDefine：" + name0);
         }
         return getSaveByDefine(d0,lv0);
      }
      
      public static function getSaveByDefine(d0:VehicleDefine, lv0:int) : VehicleSave
      {
         var s0:VehicleSave = new VehicleSave();
         s0.inDataByDefine(d0);
         s0.itemsLevel = lv0;
         return s0;
      }
      
      public static function getTempData(d0:VehicleDefine, pd0:NormalPlayerData, itemsLevel0:int = 0, evoTargetSave0:VehicleSave = null) : VehicleData
      {
         var s0:VehicleSave = null;
         s0 = new VehicleSave();
         s0.inDataByDefine(d0);
         if(Boolean(evoTargetSave0))
         {
            s0.skill.inData_byObj(evoTargetSave0.skill);
            s0.lifeMulAddLv = evoTargetSave0.lifeMulAddLv;
            s0.attackMulAddLv = evoTargetSave0.attackMulAddLv;
            s0.mainMulAddLv = evoTargetSave0.mainMulAddLv;
            s0.subMulAddLv = evoTargetSave0.subMulAddLv;
            s0.id = evoTargetSave0.id;
         }
         if(itemsLevel0 == 0)
         {
            if(Boolean(pd0))
            {
               s0.itemsLevel = pd0.level;
            }
         }
         else
         {
            s0.itemsLevel = itemsLevel0;
         }
         var da0:VehicleData = s0.getDataClass() as VehicleData;
         da0.inData_bySave(s0,pd0);
         da0.fleshByNormalPlayerData();
         return da0;
      }
      
      public static function getTempDataByName(name0:String, pd0:NormalPlayerData) : VehicleData
      {
         var d0:VehicleDefine = Gaming.defineGroup.vehicle.getDefine(name0);
         if(!(d0 is VehicleDefine))
         {
            INIT.showError("找不到定义VehicleDefine：" + name0);
         }
         return getTempData(d0,pd0);
      }
      
      private static function getStrengthenBaseProArr(da0:VehicleData) : Array
      {
         return da0.vehicleDefine.filterProNameArr(VehicleProper.strengthenBaseProArr);
      }
      
      public static function getComposeMust(da0:VehicleData) : MustDefine
      {
         var d0:VehicleDefine = null;
         var must_d0:MustDefine = null;
         d0 = da0.getVehicleSave().getVehicleDefine();
         must_d0 = new MustDefine();
         must_d0.lv = da0.save.getTrueLevel();
         must_d0.coin = int(Gaming.defineGroup.normal.getPlayerCoinIncome(must_d0.lv) / 10);
         var thingsStr0:String = d0.getMustThingsName() + ";" + d0.mustCash;
         must_d0.inThingsDataByArr([thingsStr0]);
         return must_d0;
      }
      
      public static function getComposeProString(da0:VehicleData) : String
      {
         var pro0:String = null;
         var d0:PropertyArrayDefine = null;
         var str0:String = "";
         var proArr0:Array = da0.vehicleDefine.filterProNameArr(VehicleProper.composeBaseProArr);
         for each(pro0 in proArr0)
         {
            d0 = Gaming.defineGroup.vehicle.getPropertyDefine(pro0);
            str0 += da0.vehicleDefine.fitlerProCnName(d0) + "\n";
         }
         return str0;
      }
      
      public static function getComposeValueString(da0:VehicleData) : String
      {
         var pro0:String = null;
         var d0:PropertyArrayDefine = null;
         var v0:* = undefined;
         var vString0:String = null;
         var str0:String = "";
         var proArr0:Array = da0.vehicleDefine.filterProNameArr(VehicleProper.composeBaseProArr);
         for each(pro0 in proArr0)
         {
            d0 = Gaming.defineGroup.vehicle.getPropertyDefine(pro0);
            v0 = getBasePropertyValue(pro0,da0);
            vString0 = d0.getFixedValueString(v0);
            if(v0 == "")
            {
               vString0 = "无";
            }
            str0 += vString0 + "\n";
         }
         return str0;
      }
      
      public static function getUpgradeMust(da0:VehicleData) : MustDefine
      {
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var must_d0:MustDefine = new MustDefine();
         must_d0.lv = da0.save.getTrueLevel() + 1;
         if(must_d0.lv > 99)
         {
            must_d0.lv = 99;
         }
         must_d0.coin = int(Gaming.defineGroup.normal.getPlayerCoinIncome(must_d0.lv) / 50);
         var num0:int = Gaming.defineGroup.normal.getPropertyValue("vehicleUpgradeMust",must_d0.lv);
         if(num0 > 10)
         {
            num0 = 10;
         }
         var thingsArr0:Array = ["godStone;" + num0];
         if(da0.getUpgradeAndLv() >= 99)
         {
            if(d0.getBodyDefine().isLandBody())
            {
               thingsArr0 = ["fireGem;20","electricGem;20","frozenGem;20","poisonGem;20"];
            }
            else
            {
               thingsArr0 = ["wisdomGem;15","agileGem;20","defenceGem;20","alertGem;15"];
            }
         }
         must_d0.inThingsDataByArr(thingsArr0);
         return must_d0;
      }
      
      public static function getUpgradeTip(da0:VehicleData) : String
      {
         var pro0:String = null;
         var d0:PropertyArrayDefine = null;
         var v0:* = undefined;
         var attackLabel0:String = null;
         var bulletNum0:int = 0;
         var bulletNumStr0:String = null;
         var vehicle_d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var proArr0:Array = da0.vehicleDefine.filterProNameArr(VehicleProper.upgradeBaseProArr);
         var str0:String = "";
         for each(pro0 in proArr0)
         {
            d0 = Gaming.defineGroup.vehicle.getPropertyDefine(pro0);
            v0 = getBasePropertyValue(pro0,da0);
            if(v0 is Number)
            {
               v0 = d0.getFixedValueString(v0 as Number);
            }
            attackLabel0 = VehicleProper.getLabelByProName(pro0);
            bulletNum0 = vehicle_d0.getBullletNum(attackLabel0);
            bulletNumStr0 = bulletNum0 > 1 ? ComMethod.color("x" + bulletNum0,"#00FF00") : "";
            str0 += vehicle_d0.fitlerProCnName(d0) + "：" + ComMethod.color(v0,"#FFFF00") + bulletNumStr0;
            str0 += "\n";
         }
         return str0;
      }
      
      public static function getEvolutionMust(da0:VehicleData) : MustDefine
      {
         var zodiac0:String = null;
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var evoD0:VehicleDefine = d0.getEvolutionDefine();
         var mlv0:int = evoD0.evolutionLv;
         var must_d0:MustDefine = new MustDefine();
         must_d0.lv = 60;
         must_d0.coin = int(Gaming.defineGroup.normal.getPlayerCoinIncome(must_d0.lv) / 2);
         var num0:int = Math.ceil(evoD0.mustCash);
         var thingsStr0:String = d0.getEvoMustThings() + ";" + num0;
         var thingsArr0:Array = [thingsStr0];
         if(mlv0 >= 4)
         {
            must_d0.lv = 85;
            if(mlv0 == 4)
            {
               thingsArr0.push("NianCarCash;" + (evoD0.duration * 2.5 - 20));
            }
            else if(mlv0 >= 5)
            {
               zodiac0 = VehicleDefineType.getZodiac(d0.getBaseLabel());
               if(VehicleDefineType.mustYaB(d0.getBaseLabel()))
               {
                  thingsArr0.push("yaStone;70");
                  thingsArr0.push("yaRock;50");
               }
               else
               {
                  thingsArr0.push("demStone;" + int(evoD0.duration * 1.5));
                  thingsArr0.push(zodiac0 + ";" + int(20 + evoD0.duration / 3));
               }
            }
         }
         must_d0.inThingsDataByArr(thingsArr0);
         return must_d0;
      }
      
      public static function getEvolutionTip(da0:VehicleData, addSkillB0:Boolean = false) : String
      {
         var pro0:String = null;
         var addObj0:Object = null;
         var addStr0:String = null;
         var n:* = undefined;
         var d0:PropertyArrayDefine = null;
         var v0:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var addSkillArr0:Array = null;
         var vehicle_d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var proArr0:Array = da0.vehicleDefine.filterProNameArr(VehicleProper.evolutionBaseProArr);
         proArr0.unshift(VehicleProper.LIFE_MUL);
         var str0:String = "";
         for each(pro0 in proArr0)
         {
            d0 = Gaming.defineGroup.vehicle.getPropertyDefine(pro0);
            v0 = getBasePropertyValue(pro0,da0);
            str0 += vehicle_d0.fitlerProCnName(d0) + "：" + ComMethod.color(d0.getFixedValueString(v0),"#FFFF00");
            str0 += "\n";
         }
         addObj0 = vehicle_d0.getAddObj();
         addStr0 = "";
         for(n in addObj0)
         {
            proD0 = EquipPropertyDataCreator.getPropertyArrayDefine(n);
            if(addStr0 != "")
            {
               addStr0 += "\n";
            }
            addStr0 += "角色·" + proD0.cnName + " " + ComMethod.color("+" + TextWay.numberToPer(addObj0[n]),"#FFFF00");
         }
         str0 += addStr0;
         if(addSkillB0)
         {
            addSkillArr0 = vehicle_d0.getAddSkillArr();
            if(addSkillArr0.length > 0)
            {
               str0 += "\n新增技能：" + ComMethod.color(SkillDescrip.getCnText(addSkillArr0),"#FF6600");
            }
         }
         return str0;
      }
      
      public static function getBeforeEvolutionMustNum(name0:String) : int
      {
         var num0:int = 0;
         var beforeD0:VehicleDefine = Gaming.defineGroup.vehicle.getBeforeDefine(name0);
         if(beforeD0 is VehicleDefine)
         {
            num0 += beforeD0.mustCash;
            num0 += getBeforeEvolutionMustNum(beforeD0.name);
         }
         return num0;
      }
      
      public static function getStrengthenProDataGroup(da0:VehicleData, setProGroup0:VehicleOneProDataGroup = null) : VehicleOneProDataGroup
      {
         var n:* = undefined;
         var name0:String = null;
         var proDa0:VehicleOneProData = null;
         var proD0:PropertyArrayDefine = null;
         var addV0:Number = NaN;
         var maxV0:Number = NaN;
         if(Boolean(setProGroup0))
         {
            if(setProGroup0.dat != da0)
            {
               setProGroup0 = null;
            }
         }
         var s0:VehicleSave = da0.getVehicleSave();
         var d0:VehicleDefine = s0.getVehicleDefine();
         var dg0:VehicleOneProDataGroup = setProGroup0;
         var strengthenBaseProArr0:Array = getStrengthenBaseProArr(da0);
         if(!dg0)
         {
            dg0 = new VehicleOneProDataGroup(strengthenBaseProArr0.length);
         }
         for(n in strengthenBaseProArr0)
         {
            name0 = strengthenBaseProArr0[n];
            proDa0 = dg0.arr[n];
            proD0 = Gaming.defineGroup.vehicle.getPropertyDefine(name0);
            addV0 = s0.getMulAddLv(name0);
            maxV0 = getStrengthenMaxLv(name0,d0);
            proDa0.cnName = d0.fitlerProCnName(proD0);
            proDa0.name = proD0.name;
            proDa0.value = addV0;
            proDa0.max = maxV0;
            if(addV0 >= maxV0)
            {
               proDa0.valueString = ComMethod.color("+" + addV0 + " / " + maxV0,"#00FF00");
            }
            else
            {
               proDa0.valueString = ComMethod.color("+" + addV0,"#FFFF00") + " / " + maxV0;
            }
            proDa0.tipString = "提升1级强化等级可提升" + getStrengthenOneAdd(name0) + "倍的" + d0.fitlerProCnName(proD0) + "。";
         }
         dg0.dat = da0;
         return dg0;
      }
      
      public static function getStrengthenMust(da0:VehicleData, proD0:VehicleOneProDataGroup, nowIsRemakeB0:Boolean = false) : MustDefine
      {
         var s0:VehicleSave = da0.getVehicleSave();
         var allLv0:int = s0.getAllAddLv();
         var must_d0:MustDefine = new MustDefine();
         var mustLv0:int = 50 + da0.getStrengthenLv();
         if(mustLv0 > 90)
         {
            mustLv0 = 90;
         }
         must_d0.lv = mustLv0;
         must_d0.coin = int(Gaming.defineGroup.normal.getPlayerCoinIncome(must_d0.lv) / 10);
         var thingsStr0:String = "bloodStone;" + getStrengthenMustNum(allLv0);
         var thingsArr0:Array = [thingsStr0];
         var fineNum0:int = getStrenMustNumFine(allLv0);
         if(fineNum0 > 0)
         {
            thingsArr0.push("fineStone;" + fineNum0);
         }
         must_d0.inThingsDataByArr(thingsArr0);
         return must_d0;
      }
      
      private static function getStrengthenMustNum(addLv0:int) : int
      {
         var v0:int = addLv0 * 2 + 16;
         if(v0 > 74)
         {
            v0 = 74;
         }
         return v0;
      }
      
      private static function getStrenMustNumFine(addLv0:int) : int
      {
         var v0:int = 0;
         if(addLv0 >= 30)
         {
            v0 = int((addLv0 - 30) / 3) + 3;
            if(v0 > 6)
            {
               v0 = 6;
            }
            return v0;
         }
         return 0;
      }
      
      public static function getAllStrengthenMust(addLv0:int, mul0:Number = 1) : GiftAddDefineGroup
      {
         var num0:int = 0;
         var fineNum0:int = 0;
         for(var i:int = 0; i < addLv0; i++)
         {
            num0 += getStrengthenMustNum(i);
            fineNum0 += getStrenMustNumFine(i);
         }
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         num0 = Math.ceil(num0 * mul0);
         if(num0 > 0)
         {
            g0.addGiftByStr("things;bloodStone;" + num0);
         }
         fineNum0 = Math.ceil(fineNum0 * mul0);
         if(fineNum0 > 0)
         {
            g0.addGiftByStr("things;fineStone;" + fineNum0);
         }
         return g0;
      }
      
      public static function getStrengthenMaxLv(name0:String, d0:VehicleDefine) : int
      {
         var mul0:Number = d0.getMulByLabel(name0);
         if(mul0 > 1.8)
         {
            mul0 = 1.8;
         }
         var base0:int = 18;
         if(name0 == "lifeMul")
         {
            base0 = 22;
         }
         return base0 + int(mul0 * 4);
      }
      
      public static function getStrengthenMaxAdd(name0:String, d0:VehicleDefine) : Number
      {
         return ComMethod.toFixed(getStrengthenMaxLv(name0,d0) * getStrengthenOneAdd(name0),1);
      }
      
      public static function getStrengthenOneAdd(name0:String) : Number
      {
         return 0.1;
      }
      
      public static function getStrengthenAllLv() : int
      {
         return 50;
      }
      
      public static function getSkillStudyLv() : int
      {
         return 30;
      }
      
      public static function getSkillMustStrengLv(skillLv0:int) : int
      {
         return (skillLv0 + 1) * 3;
      }
      
      public static function getStrengthenTrueAllLv(da0:VehicleData) : int
      {
         var name0:String = null;
         var allLv0:int = 0;
         var maxLv0:int = 0;
         var strengthenBaseProArr0:Array = getStrengthenBaseProArr(da0);
         for each(name0 in strengthenBaseProArr0)
         {
            maxLv0 += getStrengthenMaxLv(name0,da0.vehicleDefine);
         }
         allLv0 = getStrengthenAllLv();
         if(maxLv0 > allLv0)
         {
            maxLv0 = allLv0;
         }
         return maxLv0;
      }
      
      public static function strengthenOne(da0:VehicleData, proD0:VehicleOneProDataGroup) : String
      {
         var name0:String = null;
         var pD0:PropertyArrayDefine = null;
         var s0:VehicleSave = null;
         var proDa0:VehicleOneProData = proD0.getRandom();
         if(Boolean(proD0))
         {
            name0 = proDa0.name;
            pD0 = Gaming.defineGroup.vehicle.getPropertyDefine(name0);
            s0 = da0.getVehicleSave();
            s0.strengthenOne(name0);
            da0.fleshByNormalPlayerData();
            return "强化成功！“" + da0.vehicleDefine.fitlerProCnName(pD0) + "”强化等级" + ComMethod.color("+1","#FFFF0") + "。";
         }
         return "";
      }
      
      public static function remakeStrengthenOne(da0:VehicleData, proD0:VehicleOneProDataGroup) : String
      {
         var f0:int = 0;
         var name0:String = null;
         var maxLv0:int = 0;
         var randomMax0:int = 0;
         var v0:int = 0;
         var s0:VehicleSave = da0.getVehicleSave();
         var d0:VehicleDefine = s0.getVehicleDefine();
         var nameArr0:Array = getStrengthenBaseProArr(da0);
         var num0:int = int(nameArr0.length);
         var all0:int = s0.getAllAddLv();
         var supple0:int = all0;
         for(var i:int = 0; i < num0; i++)
         {
            f0 = int(Math.random() * nameArr0.length);
            name0 = nameArr0[f0];
            nameArr0.splice(f0,1);
            maxLv0 = getStrengthenMaxLv(name0,d0);
            randomMax0 = maxLv0 > supple0 ? supple0 : int(maxLv0 + 0.1);
            v0 = Math.random() * randomMax0;
            if(i >= num0 - 1)
            {
               v0 = supple0;
               if(v0 > maxLv0)
               {
                  v0 = maxLv0;
               }
            }
            supple0 -= v0;
            s0.setMulAddLv(name0,v0);
         }
         if(supple0 > 0)
         {
            distributionSupple(da0,supple0);
         }
         da0.fleshByNormalPlayerData();
         return "洗炼完成！";
      }
      
      private static function distributionSupple(da0:VehicleData, supple0:int) : void
      {
         var f0:int = 0;
         var name0:String = null;
         var maxLv0:int = 0;
         var v0:int = 0;
         var addV0:int = 0;
         var s0:VehicleSave = da0.getVehicleSave();
         var d0:VehicleDefine = s0.getVehicleDefine();
         var nameArr0:Array = getStrengthenBaseProArr(da0);
         var num0:int = int(nameArr0.length);
         for(var i:int = 0; i < num0; i++)
         {
            f0 = int(Math.random() * nameArr0.length);
            name0 = nameArr0[f0];
            nameArr0.splice(f0,1);
            maxLv0 = getStrengthenMaxLv(name0,d0);
            v0 = s0.getMulAddLv(name0);
            addV0 = 0;
            if(v0 < maxLv0)
            {
               addV0 = maxLv0 - v0;
               if(addV0 > supple0)
               {
                  addV0 = supple0;
               }
               supple0 -= addV0;
               v0 += addV0;
            }
            s0.setMulAddLv(name0,v0);
            if(supple0 <= 0)
            {
               return;
            }
         }
      }
      
      public static function getSkillMustByDefine(d0:HeroSkillDefine, setTargetLv0:int = 0) : MustDefine
      {
         var m0:MustDefine = new MustDefine();
         var lv0:int = d0.lv + 1;
         if(setTargetLv0 > 0)
         {
            lv0 = setTargetLv0;
         }
         var num0:int = lv0 * 10 * d0.studyMustMul;
         m0.lv = 60;
         m0.coin = int(Gaming.defineGroup.normal.getPlayerCoinIncome(m0.lv) / 30 * lv0);
         var thingsArr0:Array = ["lightStone;" + num0];
         if(lv0 >= 10)
         {
            m0.coin = 0;
            thingsArr0.push("nuclearStone;10");
         }
         m0.inThingsDataByArr(thingsArr0);
         return m0;
      }
      
      public static function getSkillBackGift(skillName0:String) : GiftAddDefineGroup
      {
         var tlv0:int = 0;
         var g0:GiftAddDefineGroup = null;
         var i:int = 0;
         var m0:MustDefine = null;
         var thingsArr0:Array = null;
         var thingsM0:MustSimpleThingsDefine = null;
         var d0:HeroSkillDefine = Gaming.defineGroup.skill.getDefine(skillName0) as HeroSkillDefine;
         if(Boolean(d0))
         {
            tlv0 = d0.lv;
            g0 = new GiftAddDefineGroup();
            for(i = 1; i <= tlv0; i++)
            {
               m0 = getSkillMustByDefine(d0,i);
               thingsArr0 = m0.getThingsArr();
               for each(thingsM0 in thingsArr0)
               {
                  g0.mergeGiftByStr("things;" + thingsM0.name + ";" + thingsM0.num);
               }
            }
            return g0;
         }
         return null;
      }
      
      public static function getTipByDefine(d0:VehicleDefine) : String
      {
         var da0:VehicleData = getTempData(d0,Gaming.PG.da);
         return getTip(da0);
      }
      
      public static function getTip(da0:VehicleData, compareDa0:VehicleData = null) : String
      {
         var attackLabel0:String = null;
         var compareObj0:Object = null;
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var str0:String = "";
         if(d0.name == "FireWolfCar")
         {
            str0 += "<b><purple 桌趣签到活动专属载具/></b>\n";
         }
         str0 += getBaseTip(da0,"基本信息",compareDa0);
         var attackLabelArr0:Array = d0.getAttackLabelArr();
         for each(attackLabel0 in attackLabelArr0)
         {
            str0 += getOneBulletTip(da0,attackLabel0,compareDa0);
         }
         compareObj0 = null;
         if(Boolean(compareDa0))
         {
            compareObj0 = compareDa0.save.getTrueObj();
         }
         str0 += "<i1>|<blue <b>提升人物：</b>/>";
         str0 += "\n" + EquipPropertyDataCreator.getText_byObj(da0.save.getTrueObj(),compareObj0);
         var specialStr0:String = d0.getSpecialInfo();
         if(specialStr0 != "")
         {
            str0 += "<i1>|<blue <b>特殊能力：</b>/>";
            str0 += "\n" + specialStr0;
         }
         var cnArr0:Array = da0.getHaveSkillCnArr();
         if(cnArr0.length > 0)
         {
            str0 += "\n<i1>|<blue <b>已学技能：</b>/>";
            str0 += "\n" + StringMethod.concatStringArr(cnArr0,99);
         }
         return str0;
      }
      
      private static function getBaseTip(da0:VehicleData, cn0:String, compareDa0:VehicleData = null) : String
      {
         var pro0:String = null;
         var v0:* = undefined;
         var v2:* = undefined;
         var str0:String = "";
         str0 += "<i1>|<blue <b>" + cn0 + "：</b>/>\n";
         var proArr0:Array = VehicleProper.tipBaseProArr;
         for each(pro0 in proArr0)
         {
            v0 = getBasePropertyValue(pro0,da0);
            v2 = null;
            if(Boolean(compareDa0))
            {
               v2 = getBasePropertyValue(pro0,compareDa0);
            }
            str0 += getOneTip(da0.vehicleDefine,pro0,v0,v2) + "\n";
         }
         return str0;
      }
      
      private static function getBasePropertyValue(name0:String, da0:VehicleData) : *
      {
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var fun0:Function = VehicleDataCreator[name0];
         if(fun0 is Function)
         {
            return fun0(name0,da0);
         }
         if(name0.indexOf("limit") == 0)
         {
            fun0 = da0[name0];
            if(fun0 is Function)
            {
               return da0[name0]();
            }
         }
         if(d0.hasOwnProperty(name0))
         {
            if(!(d0[name0] is String))
            {
               return d0[name0];
            }
         }
         return 0;
      }
      
      private static function level(name0:String, da0:VehicleData) : String
      {
         return da0.getVehicleSave().getLevelTipString() + "级";
      }
      
      private static function vehicleType(name0:String, da0:VehicleData) : String
      {
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         return d0.getVehicleTypeCn();
      }
      
      private static function lifeMul(name0:String, da0:VehicleData) : Number
      {
         return da0.getLifeMul();
      }
      
      private static function baseLifeMul(name0:String, da0:VehicleData) : Number
      {
         return da0.vehicleDefine.lifeMul;
      }
      
      private static function baseLife(name0:String, da0:VehicleData) : Number
      {
         return da0.getBaseLife();
      }
      
      private static function nowLife(name0:String, da0:VehicleData) : Number
      {
         return da0.getMaxLife();
      }
      
      private static function mainMul(name0:String, da0:VehicleData) : Number
      {
         var d0:ShootVehicleDefine = da0.getVehicleSave().getShootVehicleDefine();
         if(Boolean(d0))
         {
            return d0.main.dpsMul;
         }
         return 0;
      }
      
      private static function subMul(name0:String, da0:VehicleData) : Number
      {
         var d0:ShootVehicleDefine = da0.getVehicleSave().getShootVehicleDefine();
         if(Boolean(d0))
         {
            return d0.sub.dpsMul;
         }
         return 0;
      }
      
      private static function speed(name0:String, da0:VehicleData) : Number
      {
         var bodyD0:NormalBodyDefine = da0.getBodyDefine();
         return bodyD0.maxVx;
      }
      
      private static function evo(name0:String, da0:VehicleData) : String
      {
         var evo0:VehicleDefine = da0.getVehicleSave().getVehicleDefine().getEvolutionDefine();
         if(Boolean(evo0))
         {
            return evo0.cnName;
         }
         return "无";
      }
      
      private static function getOneBulletTip(da0:VehicleData, type0:String, compareDa0:VehicleData = null) : String
      {
         var pro0:String = null;
         var v0:Number = NaN;
         var v2:* = undefined;
         var bulletNum0:int = 0;
         var bulletNum2:int = 0;
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var cn0:String = d0.fitlerProCn(VehicleAttackLabel.getCn(type0));
         var proArr0:Array = Gaming.defineGroup.vehicle.bulletProperty.getNameArr();
         var obj0:Object = VehicleDataCreator[type0 + "BulletObj"](da0);
         var obj2:Object = null;
         if(Boolean(compareDa0))
         {
            obj2 = VehicleDataCreator[type0 + "BulletObj"](compareDa0);
         }
         var str0:String = "";
         str0 += "<i1>|<blue <b>" + cn0 + "：</b>/>\n";
         for each(pro0 in proArr0)
         {
            if(obj0.hasOwnProperty(pro0))
            {
               v0 = Number(obj0[pro0]);
               v2 = null;
               if(Boolean(obj2))
               {
                  v2 = obj2[pro0];
                  bulletNum2 = compareDa0.getVehicleSave().getVehicleDefine().getBullletNum(type0);
                  v2 *= bulletNum2;
               }
               bulletNum0 = d0.getBullletNum(type0);
               str0 += getOneTip(d0,pro0,v0,v2,bulletNum0,type0) + "\n";
            }
         }
         return str0;
      }
      
      private static function mainBulletObj(mda0:VehicleData, haveNowHurtB0:Boolean = true) : Object
      {
         var da0:ShootVehicleData = mda0 as ShootVehicleData;
         if(!da0)
         {
            return null;
         }
         var d0:ShootVehicleDefine = da0.getVehicleSave().getShootVehicleDefine();
         var bulletD0:BulletDefine = d0.main.getBulletDefine();
         var dpsMul0:Number = da0.getMainMul();
         var hurtRatio0:Number = bulletD0.hurtRatio * da0.getDpsByType("main");
         var attackGap0:Number = 1 / bulletD0.attackGap;
         var rotateRange0:Number = d0.main.getRotateRange();
         var obj0:Object = {};
         obj0["dpsMul"] = dpsMul0;
         if(haveNowHurtB0)
         {
            obj0["hurtRatio"] = hurtRatio0;
         }
         obj0["attackGap"] = attackGap0;
         obj0["rotateRange"] = rotateRange0;
         extraDataInObj(obj0,da0,bulletD0.hurtRatio,"main");
         return obj0;
      }
      
      private static function subBulletObj(mda0:VehicleData, haveNowHurtB0:Boolean = true) : Object
      {
         var da0:ShootVehicleData = mda0 as ShootVehicleData;
         if(!da0)
         {
            return null;
         }
         var d0:ShootVehicleDefine = da0.getVehicleSave().getShootVehicleDefine();
         var bulletD0:BulletDefine = d0.sub.getBulletDefine();
         var dpsMul0:Number = da0.getSubMul();
         var hurtRatio0:Number = bulletD0.hurtRatio * da0.getDpsByType("sub");
         var attackGap0:Number = 1 / bulletD0.attackGap;
         var rotateRange0:Number = d0.sub.getRotateRange();
         var obj0:Object = {};
         obj0["dpsMul"] = dpsMul0;
         if(haveNowHurtB0)
         {
            obj0["hurtRatio"] = hurtRatio0;
         }
         obj0["attackGap"] = attackGap0;
         if(d0.vehicleType == "car")
         {
            obj0["rotateRange"] = rotateRange0;
         }
         extraDataInObj(obj0,da0,bulletD0.hurtRatio,"sub");
         return obj0;
      }
      
      private static function attackBulletObj(da0:VehicleData, haveNowHurtB0:Boolean = true) : Object
      {
         var dpsMul0:Number = NaN;
         var hurtRatio0:Number = NaN;
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var bodyD0:NormalBodyDefine = da0.getBodyDefine();
         var at_d0:BodyAttackDefine = bodyD0.hurtArr[0];
         var obj0:Object = {};
         if(at_d0 is BodyAttackDefine)
         {
            dpsMul0 = da0.getAttackMul();
            hurtRatio0 = at_d0.hurtRatio * da0.getDpsByType("attack");
            obj0["dpsMul"] = dpsMul0;
            if(haveNowHurtB0)
            {
               obj0["hurtRatio"] = hurtRatio0;
               extraDataInObj(obj0,da0,at_d0.hurtRatio,"attack");
            }
         }
         return obj0;
      }
      
      private static function extraDataInObj(obj0:Object, da0:VehicleData, hurtMul0:Number, type0:String) : void
      {
         if(Gaming.isLocal())
         {
            obj0["hurtTheory"] = hurtMul0 * da0.getPDDpsByArms(type0);
            obj0["hurtLimit"] = hurtMul0 * da0.getPDDpsLimit(type0);
         }
      }
      
      private static function getOneTip(vehicleD0:VehicleDefine, name0:String, v0:*, compareV0:* = null, bulletNum0:int = 1, type0:String = "") : String
      {
         var v2:Number = NaN;
         var d0:PropertyArrayDefine = Gaming.defineGroup.vehicle.getPropertyDefine(name0);
         var color0:String = d0.gatherColor;
         var lastIcon0:String = "";
         if(compareV0)
         {
            if(!(compareV0 is String))
            {
               v2 = compareV0 as Number;
               if(v0 * bulletNum0 > v2)
               {
                  lastIcon0 = "|<i6>";
               }
               else if(v0 < v2)
               {
                  lastIcon0 = "|<i7>";
               }
            }
         }
         var bulletNumStr0:String = "<green " + (bulletNum0 > 1 ? "x" + bulletNum0 : "") + "/>";
         if(name0 == "dpsMul")
         {
            if(type0 != "attack")
            {
               bulletNumStr0 = "";
            }
         }
         else if(name0 != "hurtRatio")
         {
            bulletNumStr0 = "";
         }
         return "<" + color0 + " " + vehicleD0.fitlerProCnName(d0) + "/>|<" + color0 + " " + d0.getFixedValueString(v0) + "/>" + bulletNumStr0 + lastIcon0;
      }
   }
}

