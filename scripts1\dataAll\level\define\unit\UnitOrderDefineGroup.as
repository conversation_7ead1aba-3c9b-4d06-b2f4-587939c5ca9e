package dataAll.level.define.unit
{
   import com.common.text.TextWay;
   import dataAll.body.define.BodyCamp;
   import dataAll.body.define.NormalBodyDefine;
   
   public class UnitOrderDefineGroup
   {
      
      public var allDefault:OneUnitOrderDefine = new OneUnitOrderDefine();
      
      public var obj:Object = {};
      
      public var bodyNameObj:Object = {};
      
      public var haveSuperOrBossB:Boolean = false;
      
      public var haveBossB:Boolean = false;
      
      public function UnitOrderDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var xml2:XML = null;
         var d0:UnitOrderDefine = null;
         if(!xml0)
         {
            return;
         }
         this.allDefault.inData_byXML(xml0.allDefault[0],"");
         var xmllist2:XMLList = xml0.unitOrder;
         for(n in xmllist2)
         {
            xml2 = xmllist2[n];
            d0 = new UnitOrderDefine();
            d0.inData_byXML(xml2,this.allDefault,this);
            this.addUnitOrderDefine(d0,n);
         }
         this.fleshBodyName();
         this.fleshSuperAndBoss();
      }
      
      public function addUnitOrderDefine(d0:UnitOrderDefine, n:int = 0) : void
      {
         if(d0.id == "")
         {
            INIT.showError("第" + (n + 1) + "个unitOrder没有id属性");
         }
         else
         {
            this.obj[d0.id] = d0;
         }
      }
      
      public function addUnitOrderDefineXML(xml0:XML) : UnitOrderDefine
      {
         var d0:UnitOrderDefine = new UnitOrderDefine();
         d0.inData_byXML(xml0,this.allDefault,this);
         this.addUnitOrderDefine(d0);
         return d0;
      }
      
      public function fleshBodyName() : void
      {
         var n:* = undefined;
         var ud0:UnitOrderDefine = null;
         var arr0:Array = null;
         var i:* = undefined;
         var d0:OneUnitOrderDefine = null;
         var d5:OneUnitOrderDefine = null;
         var minus0:int = 0;
         for(n in this.obj)
         {
            ud0 = this.obj[n];
            ud0.fleshBodyName();
         }
         this.bodyNameObj = {};
         for(n in this.obj)
         {
            ud0 = this.obj[n];
            arr0 = ud0.arr;
            for(i in arr0)
            {
               d0 = arr0[i];
               if(this.bodyNameObj.hasOwnProperty(d0.name))
               {
                  d5 = this.bodyNameObj[d0.name];
                  minus0 = UnitType.minus(d0.unitType,d5.unitType);
                  if(minus0 > 0)
                  {
                     this.bodyNameObj[d0.name] = d0;
                  }
               }
               else
               {
                  this.bodyNameObj[d0.name] = d0;
               }
            }
         }
      }
      
      public function fleshSuperAndBoss() : void
      {
         var n:* = undefined;
         var ud0:UnitOrderDefine = null;
         this.haveBossB = false;
         this.haveSuperOrBossB = false;
         for(n in this.obj)
         {
            ud0 = this.obj[n];
            if(ud0.haveBossB)
            {
               this.haveBossB = true;
            }
            if(ud0.haveSuperOrBossB)
            {
               this.haveSuperOrBossB = true;
            }
         }
      }
      
      public function getBossCnStr() : String
      {
         var nameArr0:Array = this.getBossCnArr();
         return TextWay.mixedStringArr(nameArr0,10);
      }
      
      public function getBossCnArr() : Array
      {
         var d0:OneUnitOrderDefine = null;
         var nameArr0:Array = [];
         for each(d0 in this.bodyNameObj)
         {
            if(d0.unitType == UnitType.BOSS && d0.camp != "we")
            {
               nameArr0.push(d0.cnName);
            }
         }
         return nameArr0;
      }
      
      public function getBossNameArr() : Array
      {
         var d0:UnitOrderDefine = null;
         var od0:OneUnitOrderDefine = null;
         var arr0:Array = [];
         for each(d0 in this.obj)
         {
            for each(od0 in d0.arr)
            {
               if(od0.unitType == UnitType.BOSS)
               {
                  if(arr0.indexOf(od0.name) == -1)
                  {
                     arr0.push(od0.name);
                  }
               }
            }
         }
         return arr0;
      }
      
      public function getBossOne() : OneUnitOrderDefine
      {
         var d0:UnitOrderDefine = null;
         var od0:OneUnitOrderDefine = null;
         for each(d0 in this.obj)
         {
            for each(od0 in d0.arr)
            {
               if(od0.unitType == UnitType.BOSS)
               {
                  return od0;
               }
            }
         }
         return null;
      }
      
      public function getUnitOrderDefine(id0:String) : UnitOrderDefine
      {
         return this.obj[id0];
      }
      
      public function getOneByName(name0:String) : OneUnitOrderDefine
      {
         return this.bodyNameObj[name0];
      }
      
      public function getUnitNameArr() : Array
      {
         var n:* = undefined;
         var d0:UnitOrderDefine = null;
         var i:* = undefined;
         var od0:OneUnitOrderDefine = null;
         var name0:String = null;
         var bodyDefine0:NormalBodyDefine = null;
         var arr0:Array = [];
         for(n in this.obj)
         {
            d0 = this.obj[n];
            for(i in d0.arr)
            {
               od0 = d0.arr[i];
               name0 = od0.name;
               if(arr0.indexOf(name0) == -1)
               {
                  arr0.push(name0);
                  bodyDefine0 = od0.getBodyDefine();
                  if(bodyDefine0 is NormalBodyDefine)
                  {
                     if(bodyDefine0.otherUnitCnNameArr.length > 0)
                     {
                        arr0 = arr0.concat(bodyDefine0.getOtherUnitNameArr());
                     }
                  }
               }
            }
         }
         return arr0;
      }
      
      public function getSkillArr() : Array
      {
         var d0:UnitOrderDefine = null;
         var od0:OneUnitOrderDefine = null;
         var skillName0:String = null;
         var arr0:Array = [];
         for each(d0 in this.obj)
         {
            for each(od0 in d0.arr)
            {
               for each(skillName0 in od0.skillArr)
               {
                  if(arr0.indexOf(skillName0) == -1)
                  {
                     arr0.push(skillName0);
                  }
               }
            }
         }
         return arr0;
      }
      
      public function getEnemyCnArr() : Array
      {
         var d0:UnitOrderDefine = null;
         var od0:OneUnitOrderDefine = null;
         var arr0:Array = [];
         for each(d0 in this.obj)
         {
            for each(od0 in d0.arr)
            {
               if(od0.camp == BodyCamp.ENEMY && od0.unitType == UnitType.NORMAL)
               {
                  if(arr0.indexOf(od0.cnName) == -1)
                  {
                     arr0.push(od0.cnName);
                  }
               }
            }
         }
         return arr0;
      }
      
      public function fixedBy(ug0:UnitOrderDefineGroup, type0:String) : void
      {
         if(type0 == "all")
         {
            this.allDefault = ug0.allDefault;
            this.obj = ug0.obj;
         }
         else if(type0 == "before" || type0 == "affter")
         {
            this.fixedObj(ug0.obj);
         }
         else if(type0 == "onlyWe")
         {
            this.fixed_only(ug0.obj,"we");
         }
         else if(type0 == "onlyEnemy")
         {
            this.fixed_only(ug0.obj,"enemy");
            this.allDefault = ug0.allDefault;
         }
         this.fleshBodyName();
         this.fleshSuperAndBoss();
      }
      
      private function fixedObj(obj0:Object) : void
      {
         var n:* = undefined;
         for(n in obj0)
         {
            this.obj[n] = obj0[n];
         }
      }
      
      private function fixed_only(obj0:Object, camp0:String) : void
      {
         var n:* = undefined;
         var d0:UnitOrderDefine = null;
         this.delByCamp(camp0);
         for(n in obj0)
         {
            d0 = obj0[n];
            if(d0.camp == camp0)
            {
               this.obj[n] = d0;
            }
         }
      }
      
      private function delByCamp(camp0:String) : void
      {
         var n:* = undefined;
         var d0:UnitOrderDefine = null;
         var obj0:Object = {};
         for(n in this.obj)
         {
            d0 = this.obj[n];
            if(d0.camp != camp0)
            {
               obj0[n] = d0;
            }
         }
         this.obj = obj0;
      }
   }
}

