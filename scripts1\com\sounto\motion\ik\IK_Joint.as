package com.sounto.motion.ik
{
   import com.sounto.math.Maths;
   
   public class IK_Joint
   {
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var ra:Number = 0;
      
      public var inputB:Boolean = false;
      
      public var bone1:IK_Bone = null;
      
      public var bone2:IK_Bone = null;
      
      public var rotationRange:Number = 6.283185307179586;
      
      public var rotationBase:Number = 0;
      
      public function IK_Joint()
      {
         super();
      }
      
      public function fleshMaxRa(onlyPanB:Boolean = false) : Boolean
      {
         var joint_3:IK_Joint = null;
         var ra1:Number = NaN;
         var c_ra1:Number = NaN;
         var bone_ra:Number = NaN;
         var bb0:Boolean = false;
         if(<PERSON><PERSON><PERSON>(this.bone1))
         {
            this.ra = this.bone1.ra;
         }
         if(<PERSON><PERSON><PERSON>(this.bone2))
         {
            joint_3 = this.bone2.tailJoint;
            if(<PERSON><PERSON><PERSON>(joint_3))
            {
               ra1 = Maths.ZhunJ(Math.atan2(joint_3.y - this.y,joint_3.x - this.x) - this.ra);
               c_ra1 = ra1 - this.rotationBase;
               if(Math.abs(c_ra1) > this.rotationRange)
               {
                  if(!onlyPanB)
                  {
                     bone_ra = 0;
                     if(c_ra1 > 0)
                     {
                        bone_ra = this.rotationBase + this.ra + this.rotationRange;
                     }
                     else
                     {
                        bone_ra = this.rotationBase + this.ra - this.rotationRange;
                     }
                     this.bone2.ra = bone_ra;
                     joint_3.x = this.x + this.bone2.length * Math.cos(bone_ra);
                     joint_3.y = this.y + this.bone2.length * Math.sin(bone_ra);
                  }
                  bb0 = true;
               }
               if(!onlyPanB)
               {
                  this.bone2.flesh();
               }
            }
         }
         return bb0;
      }
      
      public function getMaxRa1() : Number
      {
         return Maths.ZhunJ(this.rotationBase + this.ra + this.rotationRange);
      }
      
      public function getMaxRa2() : Number
      {
         return Maths.ZhunJ(this.rotationBase + this.ra - this.rotationRange);
      }
   }
}

