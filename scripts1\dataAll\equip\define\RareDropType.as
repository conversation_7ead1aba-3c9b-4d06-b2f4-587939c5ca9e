package dataAll.equip.define
{
   public class RareDropType
   {
      
      public static var NONE:String = "";
      
      public static var NORMAL:String = "normal";
      
      public static var RARE:String = "rare";
      
      public static var BLACK:String = "black";
      
      public function RareDropType()
      {
         super();
      }
      
      public static function getDropMul(type0:String, lv0:int) : Number
      {
         var mul0:Number = 2;
         if(lv0 >= 81)
         {
            mul0 = 4;
         }
         if(type0 == NORMAL)
         {
            return 0.8 * mul0;
         }
         if(type0 == RARE)
         {
            return 0.05 * mul0;
         }
         return 0;
      }
      
      public static function getDropType(lv0:int) : String
      {
         var arr0:Array = null;
         var num0:int = 0;
         if(lv0 >= 70)
         {
            arr0 = null;
            num0 = lv0 % 5;
            if(num0 == 1)
            {
               arr0 = [EquipType.BELT,EquipType.HEAD];
            }
            else if(num0 == 2)
            {
               arr0 = EquipType.NORMAL_ARR;
            }
            else if(num0 == 3)
            {
               arr0 = [EquipType.BELT];
            }
            else if(num0 == 4)
            {
               arr0 = [EquipType.HEAD];
            }
            else if(num0 == 0)
            {
               arr0 = [EquipType.COAT,EquipType.PANTS];
            }
            if(Boolean(arr0))
            {
               return arr0[int(Math.random() * arr0.length)];
            }
         }
         return "";
      }
   }
}

